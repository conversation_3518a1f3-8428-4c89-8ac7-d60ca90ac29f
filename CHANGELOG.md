# 更新日志

## [未发布] - 2024-01-XX

### 修改
- **交易数据模块优化**: 将多维表现卡片和明细数据详情卡片合并为一个统一的卡片组件
  - 新增了 `DimensionAnalysisWithDetail.js` 组件，使用标签页(Tabs)来切换图表视图和明细数据视图
  - 移除了独立的 `DimensionAnalysis` 和 `DetailTable` 组件的单独使用
  - 更新了 `TradingDataModule.js` 以使用新的合并组件
  - 改善了用户体验，减少了页面滚动需求

### 技术细节
- 使用 Antd Tabs 组件实现视图切换
- 保持了原有的所有功能和数据展示
- 兼容现有的API接口和数据结构
- 添加了相应的CSS样式优化

### 文件修改
- 新增: `frontend/src/components/DimensionAnalysisWithDetail.js`
- 修改: `frontend/src/components/TradingDataModule.js`
- 修改: `frontend/src/App.css`

---

## 说明
此变更日志记录了项目的重要修改和新增功能。 