#!/usr/bin/env python3
"""
调试MTD计算逻辑
分析为什么2025年8月1日时MTD为0的问题
"""

from datetime import datetime, timedelta
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from real_data_queries import get_week_date_range, calculate_mtd_gmv
from database import execute_query

def debug_mtd_calculation():
    """调试MTD计算逻辑"""
    print("=== MTD计算逻辑调试 ===")
    print(f"当前时间: {datetime.now()}")
    
    # 模拟当前时间为2025年8月1日
    current_date = datetime(2025, 8, 1)
    print(f"模拟当前时间: {current_date}")
    
    current_year = current_date.year
    current_week = current_date.isocalendar()[1]
    current_month = current_date.month
    
    print(f"当前年份: {current_year}")
    print(f"当前周数: {current_week}")
    print(f"当前月份: {current_month}")
    
    # 获取上一个完整周
    prev_week = current_week - 1
    prev_year = current_year
    
    print(f"上一个完整周: 第{prev_week}周")
    
    # 处理跨年情况
    if prev_week <= 0:
        prev_year = current_year - 1
        last_day_of_prev_year = datetime(prev_year, 12, 31)
        prev_week = last_day_of_prev_year.isocalendar()[1]
        print(f"跨年处理: {prev_year}年第{prev_week}周")
    
    # 获取上一个完整周的日期范围
    prev_week_start, prev_week_end = get_week_date_range(prev_year, prev_week)
    prev_week_end_date = datetime.strptime(prev_week_end, '%Y-%m-%d')
    
    print(f"上一个完整周日期范围: {prev_week_start} 到 {prev_week_end}")
    print(f"上一个完整周结束日期: {prev_week_end_date}")
    
    # 判断上一个完整周的周日是否在本月内
    current_month_start = datetime(current_year, current_month, 1)
    print(f"本月开始日期: {current_month_start}")
    
    print(f"\n=== 关键判断 ===")
    print(f"上一个完整周结束日期年份: {prev_week_end_date.year}")
    print(f"当前年份: {current_year}")
    print(f"上一个完整周结束日期月份: {prev_week_end_date.month}")
    print(f"当前月份: {current_month}")
    
    is_in_current_month = (prev_week_end_date.year == current_year and 
                          prev_week_end_date.month == current_month)
    
    print(f"上一个完整周是否在本月内: {is_in_current_month}")
    
    if is_in_current_month:
        print(f"\n✅ 应该计算MTD: 从 {current_month_start.strftime('%Y-%m-%d')} 到 {prev_week_end}")
        
        # 构建SQL查询
        sql = f"""
        SELECT COALESCE(SUM(gmv), 0) as mtd_gmv
        FROM dws_o2o_sales_d
        WHERE ds BETWEEN '{current_month_start.strftime('%Y-%m-%d')}' AND '{prev_week_end}'
        """
        
        print(f"SQL查询: {sql}")
        
        try:
            result = execute_query(sql)
            if result:
                mtd_gmv = float(result[0]['mtd_gmv'])
                print(f"MTD GMV: {mtd_gmv:,.2f}")
            else:
                print("查询无结果")
        except Exception as e:
            print(f"查询失败: {e}")
            
    else:
        print(f"\n❌ 不计算MTD: 上一个完整周不在本月内")
        print(f"   上一个完整周结束日期: {prev_week_end_date.strftime('%Y-%m-%d')}")
        print(f"   当前月份范围: {current_year}-{current_month:02d}")
    
    print(f"\n=== 问题分析 ===")
    if not is_in_current_month:
        print("🔍 问题原因: 当前逻辑要求上一个完整周的结束日期必须在当前月份内")
        print("   但是2025年8月1日时，上一个完整周是7月份的周，所以MTD被设为0")
        print("\n💡 解决方案建议:")
        print("   1. 修改逻辑：MTD应该计算到当前日期为止，而不是上一个完整周")
        print("   2. 或者：如果当前周已经有部分数据，应该包含在MTD计算中")
        print("   3. 用户期望：截止到7月31日的7月份完成情况")

def test_alternative_mtd_logic():
    """测试替代的MTD计算逻辑"""
    print(f"\n=== 测试替代MTD计算逻辑 ===")
    
    # 模拟当前时间为2025年8月1日
    current_date = datetime(2025, 8, 1)
    
    # 方案1: 计算到昨天为止的MTD
    yesterday = current_date - timedelta(days=1)
    if yesterday.month == current_date.month:
        # 昨天还在本月内，计算本月MTD
        month_start = datetime(current_date.year, current_date.month, 1)
        print(f"方案1 - 计算到昨天: 从 {month_start.strftime('%Y-%m-%d')} 到 {yesterday.strftime('%Y-%m-%d')}")
    else:
        # 昨天不在本月内，说明今天是本月第一天，MTD为0
        print(f"方案1 - 今天是本月第一天，MTD为0")
    
    # 方案2: 计算到上月最后一天的MTD（如果当前是月初）
    if current_date.day == 1:
        # 今天是月初，计算上月的完整MTD
        last_month = current_date.replace(day=1) - timedelta(days=1)
        last_month_start = datetime(last_month.year, last_month.month, 1)
        last_month_end = last_month
        print(f"方案2 - 上月完整MTD: 从 {last_month_start.strftime('%Y-%m-%d')} 到 {last_month_end.strftime('%Y-%m-%d')}")
        
        # 测试查询上月数据
        sql = f"""
        SELECT COALESCE(SUM(gmv), 0) as last_month_gmv
        FROM dws_o2o_sales_d
        WHERE ds BETWEEN '{last_month_start.strftime('%Y-%m-%d')}' AND '{last_month_end.strftime('%Y-%m-%d')}'
        """
        
        print(f"SQL查询: {sql}")
        
        try:
            result = execute_query(sql)
            if result:
                last_month_gmv = float(result[0]['last_month_gmv'])
                print(f"上月完整GMV: {last_month_gmv:,.2f}")
            else:
                print("查询无结果")
        except Exception as e:
            print(f"查询失败: {e}")

def check_july_data():
    """检查7月份的数据情况"""
    print(f"\n=== 检查7月份数据 ===")
    
    # 检查7月份完整数据
    july_start = "2025-07-01"
    july_end = "2025-07-31"
    
    sql = f"""
    SELECT 
        COALESCE(SUM(gmv), 0) as july_total_gmv,
        COUNT(*) as record_count,
        MIN(ds) as min_date,
        MAX(ds) as max_date
    FROM dws_o2o_sales_d
    WHERE ds BETWEEN '{july_start}' AND '{july_end}'
    """
    
    print(f"SQL查询: {sql}")
    
    try:
        result = execute_query(sql)
        if result:
            data = result[0]
            print(f"7月份总GMV: {float(data['july_total_gmv']):,.2f}")
            print(f"记录数量: {data['record_count']}")
            print(f"数据日期范围: {data['min_date']} 到 {data['max_date']}")
        else:
            print("查询无结果")
    except Exception as e:
        print(f"查询失败: {e}")

if __name__ == "__main__":
    debug_mtd_calculation()
    test_alternative_mtd_logic()
    check_july_data()
