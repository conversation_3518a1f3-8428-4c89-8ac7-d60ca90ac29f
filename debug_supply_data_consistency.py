#!/usr/bin/env python3
"""
调试供给数据一致性问题
检查上方卡片和下方表格的数据来源差异
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from database import execute_query
from real_data_queries import get_week_date_range, get_supply_summary_real, get_supply_detail_real
import logging
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_supply_summary_data():
    """调试供给概览数据（上方卡片）"""
    print("🔍 调试供给概览数据（上方卡片）...")
    
    try:
        result = get_supply_summary_real(week=28, brand='圣农')
        
        print("📊 供给概览数据:")
        print(f"   GMV: ¥{result.get('gmv', 0):,.2f}")
        print(f"   店铺数: {result.get('store_count', 0):,}")
        print(f"   渗透率: {result.get('store_penetration', 0):.1%}")
        print(f"   活跃率: {result.get('store_activity_rate', 0):.1%}")
        print(f"   平均SKU: {result.get('avg_sku_per_store', 0):,}")
        print(f"   缺货率: {result.get('sku_sold_out_rate', 0):.1%}")
        
        return result
        
    except Exception as e:
        print(f"❌ 供给概览数据查询失败: {e}")
        return None

def debug_supply_detail_data():
    """调试供给明细数据（下方表格）"""
    print("\n🔍 调试供给明细数据（下方表格）...")
    
    try:
        result = get_supply_detail_real(dimension_type='子品牌', brand='圣农', selected_week=28)
        
        if result.get('data_source') == 'error':
            print(f"❌ 供给明细数据查询失败: {result.get('error')}")
            return None
            
        data = result.get('data', [])
        if data:
            item = data[0]
            print("📊 供给明细数据:")
            print(f"   维度值: {item.get('dimension_value', 'N/A')}")
            print(f"   GMV: ¥{item.get('gmv', 0):,.2f}")
            print(f"   店铺数: {item.get('store_count', 0):,}")
            print(f"   渗透率: {item.get('store_penetration', 0):.1%}")
            print(f"   活跃率: {item.get('store_activity_rate', 0):.1%}")
            print(f"   平均SKU: {item.get('avg_sku_per_store', 0):,}")
            print(f"   缺货率: {item.get('sku_sold_out_rate', 0):.1%}")
            
            return item
        else:
            print("❌ 供给明细数据为空")
            return None
            
    except Exception as e:
        print(f"❌ 供给明细数据查询失败: {e}")
        return None

def debug_raw_supply_data():
    """调试原始供给数据"""
    print("\n🔍 调试原始供给数据...")
    
    # 获取第28周的周六日期
    week_start, week_end = get_week_date_range(2025, 28)
    week_start_date = datetime.strptime(week_start, '%Y-%m-%d')
    saturday_date = week_start_date + timedelta(days=5)  # 周六
    saturday_yyyymmdd = saturday_date.strftime('%Y%m%d')
    
    print(f"第28周周六日期: {saturday_yyyymmdd}")
    
    # 1. 检查供给概览使用的数据源（brand_city表）
    print("\n1. 供给概览数据源（brand_city表）:")
    try:
        sql = f"""
        SELECT 
            collect_brand,
            SUM(onsale_poi_num) as total_store_count,
            AVG(poi_permeate_rate) as avg_penetration,
            AVG(poi_sold_rate) as avg_activity_rate,
            AVG(store_avg_on_sales_sku) as avg_sku_per_store,
            AVG(sku_sold_out_rate) as avg_sold_out_rate,
            COUNT(*) as record_count
        FROM dws_mt_brand_city_details_daily_data_attribute_d
        WHERE ds = '{saturday_yyyymmdd}'
        AND platform = '美团'
        AND collect_brand = '圣农'
        GROUP BY collect_brand
        """
        
        result = execute_query(sql)
        if result:
            row = result[0]
            print(f"   品牌: {row['collect_brand']}")
            print(f"   店铺数: {row['total_store_count']:,}")
            print(f"   渗透率: {row['avg_penetration']:.6f} ({row['avg_penetration']:.1%})")
            print(f"   活跃率: {row['avg_activity_rate']:.6f} ({row['avg_activity_rate']:.1%})")
            print(f"   平均SKU: {row['avg_sku_per_store']:.2f}")
            print(f"   缺货率: {row['avg_sold_out_rate']:.6f} ({row['avg_sold_out_rate']:.1%})")
            print(f"   记录数: {row['record_count']}")
        else:
            print("   ❌ 未找到数据")
            
    except Exception as e:
        print(f"   ❌ 查询失败: {e}")
    
    # 2. 检查供给明细使用的数据源（channel_city表，按子品牌聚合）
    print("\n2. 供给明细数据源（channel_city表，按子品牌聚合）:")
    try:
        sql = f"""
        SELECT 
            collect_sub_brand,
            SUM(onsale_poi_num) as total_store_count,
            AVG(poi_permeate_rate) as avg_penetration,
            AVG(poi_sold_rate) as avg_activity_rate,
            AVG(store_avg_on_sales_sku) as avg_sku_per_store,
            AVG(sku_sold_out_rate) as avg_sold_out_rate,
            COUNT(*) as record_count
        FROM dws_mt_channel_city_details_daily_data_attribute_d
        WHERE ds = '{saturday_yyyymmdd}'
        AND platform = '美团'
        AND collect_brand = '圣农'
        AND collect_sub_brand IS NOT NULL
        GROUP BY collect_sub_brand
        """
        
        result = execute_query(sql)
        if result:
            for row in result:
                print(f"   子品牌: {row['collect_sub_brand']}")
                print(f"   店铺数: {row['total_store_count']:,}")
                print(f"   渗透率: {row['avg_penetration']:.6f} ({row['avg_penetration']:.1%})")
                print(f"   活跃率: {row['avg_activity_rate']:.6f} ({row['avg_activity_rate']:.1%})")
                print(f"   平均SKU: {row['avg_sku_per_store']:.2f}")
                print(f"   缺货率: {row['avg_sold_out_rate']:.6f} ({row['avg_sold_out_rate']:.1%})")
                print(f"   记录数: {row['record_count']}")
                print("   ---")
        else:
            print("   ❌ 未找到数据")
            
    except Exception as e:
        print(f"   ❌ 查询失败: {e}")

def compare_data_sources():
    """比较两个数据源的差异"""
    print("\n🔍 比较两个数据源的差异...")
    
    # 获取第28周的周六日期
    week_start, week_end = get_week_date_range(2025, 28)
    week_start_date = datetime.strptime(week_start, '%Y-%m-%d')
    saturday_date = week_start_date + timedelta(days=5)  # 周六
    saturday_yyyymmdd = saturday_date.strftime('%Y%m%d')
    
    print("检查两个表是否使用相同的数据源...")
    
    # 检查brand_city表和channel_city表的数据是否一致
    try:
        # brand_city表的数据
        brand_sql = f"""
        SELECT 
            'brand_city' as source,
            SUM(onsale_poi_num) as total_store_count,
            AVG(poi_permeate_rate) as avg_penetration,
            AVG(poi_sold_rate) as avg_activity_rate,
            AVG(sku_sold_out_rate) as avg_sold_out_rate
        FROM dws_mt_brand_city_details_daily_data_attribute_d
        WHERE ds = '{saturday_yyyymmdd}'
        AND platform = '美团'
        AND collect_brand = '圣农'
        """
        
        # channel_city表的数据（按品牌聚合）
        channel_sql = f"""
        SELECT 
            'channel_city' as source,
            SUM(onsale_poi_num) as total_store_count,
            AVG(poi_permeate_rate) as avg_penetration,
            AVG(poi_sold_rate) as avg_activity_rate,
            AVG(sku_sold_out_rate) as avg_sold_out_rate
        FROM dws_mt_channel_city_details_daily_data_attribute_d
        WHERE ds = '{saturday_yyyymmdd}'
        AND platform = '美团'
        AND collect_brand = '圣农'
        """
        
        brand_result = execute_query(brand_sql)
        channel_result = execute_query(channel_sql)
        
        print("📊 数据源对比:")
        if brand_result:
            row = brand_result[0]
            print(f"brand_city表:")
            print(f"   店铺数: {row['total_store_count']:,}")
            print(f"   渗透率: {row['avg_penetration']:.1%}")
            print(f"   活跃率: {row['avg_activity_rate']:.1%}")
            print(f"   缺货率: {row['avg_sold_out_rate']:.1%}")
        
        if channel_result:
            row = channel_result[0]
            print(f"channel_city表:")
            print(f"   店铺数: {row['total_store_count']:,}")
            print(f"   渗透率: {row['avg_penetration']:.1%}")
            print(f"   活跃率: {row['avg_activity_rate']:.1%}")
            print(f"   缺货率: {row['avg_sold_out_rate']:.1%}")
            
    except Exception as e:
        print(f"❌ 数据源对比失败: {e}")

def main():
    """主函数"""
    print("🚀 开始调试供给数据一致性问题\n")
    
    # 1. 调试供给概览数据
    summary_data = debug_supply_summary_data()
    
    # 2. 调试供给明细数据
    detail_data = debug_supply_detail_data()
    
    # 3. 调试原始供给数据
    debug_raw_supply_data()
    
    # 4. 比较数据源
    compare_data_sources()
    
    # 5. 总结差异
    if summary_data and detail_data:
        print("\n📋 数据差异总结:")
        print(f"   店铺数: 概览 {summary_data.get('store_count', 0):,} vs 明细 {detail_data.get('store_count', 0):,}")
        print(f"   渗透率: 概览 {summary_data.get('store_penetration', 0):.1%} vs 明细 {detail_data.get('store_penetration', 0):.1%}")
        print(f"   活跃率: 概览 {summary_data.get('store_activity_rate', 0):.1%} vs 明细 {detail_data.get('store_activity_rate', 0):.1%}")
        print(f"   缺货率: 概览 {summary_data.get('sku_sold_out_rate', 0):.1%} vs 明细 {detail_data.get('sku_sold_out_rate', 0):.1%}")
    
    print("\n🎉 调试完成！")

if __name__ == "__main__":
    main()
