#!/bin/bash

echo "=== 周报自动化看板启动脚本 ==="

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: Python3 未安装"
    exit 1
fi

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "错误: Node.js 未安装"
    exit 1
fi

# 启动后端
echo "启动Flask后端..."
cd backend
if [ ! -d "venv" ]; then
    echo "创建Python虚拟环境..."
    python3 -m venv venv
fi

source venv/bin/activate
pip install -r requirements.txt

echo "后端启动在 http://localhost:5001"
python app.py &
BACKEND_PID=$!

# 等待后端启动
sleep 3

# 启动前端
echo "启动React前端..."
cd ../frontend

if [ ! -d "node_modules" ]; then
    echo "安装Node.js依赖..."
    npm install
fi

echo "前端启动在 http://localhost:3000"
npm start &
FRONTEND_PID=$!

echo "=== 启动完成 ==="
echo "后端服务: http://localhost:5001"
echo "前端服务: http://localhost:3000"
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
trap "echo '正在停止服务...'; kill $BACKEND_PID $FRONTEND_PID; exit 0" INT

wait 