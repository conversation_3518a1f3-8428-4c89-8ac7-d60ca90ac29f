<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端控制台调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .console-output {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .platform-btn {
            padding: 8px 16px;
            margin-right: 10px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        .platform-btn.active {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        .test-btn {
            padding: 10px 20px;
            background: #52c41a;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .clear-btn {
            padding: 10px 20px;
            background: #ff4d4f;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 前端控制台调试工具</h1>
        <p>监控前端JavaScript执行情况和API调用</p>

        <div class="debug-section">
            <h3>平台选择测试</h3>
            <button class="platform-btn active" data-platform="全平台">全平台</button>
            <button class="platform-btn" data-platform="美团">美团</button>
            <button class="platform-btn" data-platform="饿了么">饿了么</button>
            <button class="platform-btn" data-platform="京东到家">京东到家</button>
        </div>

        <div class="debug-section">
            <h3>测试控制</h3>
            <button class="test-btn" onclick="testActivityTrends()">测试活动趋势API</button>
            <button class="test-btn" onclick="testFrontendLogic()">测试前端逻辑</button>
            <button class="clear-btn" onclick="clearConsole()">清空日志</button>
        </div>

        <div class="debug-section">
            <h3>控制台输出</h3>
            <div id="console-output" class="console-output">等待测试...</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5002/api';
        const BRAND = '圣农';
        let currentPlatform = '全平台';
        let consoleOutput = document.getElementById('console-output');

        // 重写console.log来捕获输出
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;

        function addToConsole(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            const colorMap = {
                'log': '#0f0',
                'error': '#f00',
                'warn': '#ff0'
            };
            
            consoleOutput.innerHTML += `<span style="color: ${colorMap[type] || '#0f0'}">[${timestamp}] ${type.toUpperCase()}: ${message}</span>\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToConsole('log', ...args);
        };

        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToConsole('error', ...args);
        };

        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addToConsole('warn', ...args);
        };

        // 捕获未处理的错误
        window.addEventListener('error', function(e) {
            console.error('JavaScript错误:', e.message, 'at', e.filename + ':' + e.lineno);
        });

        window.addEventListener('unhandledrejection', function(e) {
            console.error('未处理的Promise拒绝:', e.reason);
        });

        // 平台切换事件
        document.querySelectorAll('.platform-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.platform-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                currentPlatform = btn.dataset.platform;
                console.log('平台切换到:', currentPlatform);
            });
        });

        // 测试活动趋势API
        async function testActivityTrends() {
            console.log('=== 开始测试活动趋势API ===');
            console.log('当前平台:', currentPlatform);
            
            try {
                // 模拟前端的周数计算逻辑
                const selectedWeek = '2025-W30';
                let weekNumber = null;
                if (selectedWeek && selectedWeek.includes('-W')) {
                    weekNumber = parseInt(selectedWeek.split('-W')[1]);
                } else if (selectedWeek) {
                    weekNumber = parseInt(selectedWeek);
                }
                if (!weekNumber || isNaN(weekNumber)) {
                    weekNumber = 26; // 默认值
                }

                console.log('计算的周数:', weekNumber);

                const url = `${API_BASE}/activity/trends?brand=${BRAND}&end_week=${weekNumber}&platform=${currentPlatform}`;
                console.log('API URL:', url);

                const response = await fetch(url);
                console.log('API响应状态:', response.status);

                if (response.ok) {
                    const data = await response.json();
                    console.log('API响应数据:', {
                        hasData: !!data,
                        hasTrendData: !!(data && data.trend_data),
                        trendDataLength: data && data.trend_data ? data.trend_data.length : 0,
                        firstItem: data && data.trend_data && data.trend_data[0] ? data.trend_data[0] : null
                    });

                    // 模拟前端组件的数据检查逻辑
                    const loading = false;
                    const selectedMetric = 'activity_gmv';
                    
                    console.log('前端组件数据检查:', {
                        loading,
                        hasData: !!data,
                        hasTrendData: !!(data && data.trend_data),
                        selectedMetric
                    });

                    if (loading || !data || !data.trend_data || !selectedMetric) {
                        console.warn('组件会显示loading状态，原因:', {
                            loading,
                            hasData: !!data,
                            hasTrendData: !!(data && data.trend_data),
                            selectedMetric
                        });
                    } else {
                        console.log('✅ 组件应该正常渲染图表');
                        
                        // 模拟数据处理
                        const trendData = data.trend_data;
                        const weeks = trendData.map(item => `第${item.week}周`);
                        const mainValues = trendData.map(item => Math.round(item.activity_gmv));
                        
                        console.log('处理后的数据:', {
                            weeks: weeks.slice(0, 3),
                            mainValues: mainValues.slice(0, 3)
                        });
                    }
                } else {
                    console.error('API调用失败:', response.status, response.statusText);
                }
            } catch (error) {
                console.error('测试过程中发生错误:', error);
            }
            
            console.log('=== 活动趋势API测试完成 ===');
        }

        // 测试前端逻辑
        function testFrontendLogic() {
            console.log('=== 开始测试前端逻辑 ===');
            
            // 模拟ActivityDataModule的状态
            const mockState = {
                selectedWeek: '2025-W30',
                selectedPlatform: currentPlatform,
                brand: BRAND,
                selectedCard: 'activity_gmv',
                loading: false,
                trendsData: {
                    trend_data: [
                        {
                            week: 19,
                            activity_gmv: 897899.7,
                            activity_gmv_yoy: -0.12877122644484404,
                            activity_gmv_ratio: 0.2367502740031358,
                            activity_gmv_ratio_yoy: -0.2324829393926779
                        }
                    ]
                }
            };

            console.log('模拟的组件状态:', mockState);

            // 模拟组件渲染逻辑
            const { trendsData, loading, selectedCard } = mockState;
            
            console.log('ActivityInteractiveTrendChart props:', {
                data: trendsData,
                loading,
                selectedMetric: selectedCard
            });

            // 模拟useEffect逻辑
            if (loading || !trendsData || !trendsData.trend_data || !selectedCard) {
                console.warn('组件会显示loading，条件检查:', {
                    loading,
                    hasData: !!trendsData,
                    hasTrendData: !!(trendsData && trendsData.trend_data),
                    selectedMetric: selectedCard
                });
            } else {
                console.log('✅ 组件应该渲染图表');
            }

            console.log('=== 前端逻辑测试完成 ===');
        }

        function clearConsole() {
            consoleOutput.innerHTML = '';
        }

        // 初始化
        console.log('调试工具已加载');
        console.log('当前平台:', currentPlatform);
    </script>
</body>
</html>
