<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>明细数据表格调试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .api-test {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            font-weight: bold;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔍 明细数据表格调试工具</h1>
        
        <div class="api-test">
            <h3>API测试</h3>
            <button onclick="testAPI('子品牌')">测试子品牌API</button>
            <button onclick="testAPI('大区')">测试大区API</button>
            <button onclick="testAPI('大区', '圣农', '28')">测试大区+品牌+周次API</button>
            
            <div id="apiStatus" class="status"></div>
            <pre id="apiResponse"></pre>
        </div>

        <div class="api-test">
            <h3>前端状态检查</h3>
            <button onclick="checkFrontend()">检查前端页面</button>
            <button onclick="checkConsole()">打开浏览器控制台</button>
            
            <div id="frontendStatus" class="status"></div>
            <pre id="frontendInfo"></pre>
        </div>

        <div class="api-test">
            <h3>问题诊断</h3>
            <div id="diagnosis">
                <p><strong>已知情况：</strong></p>
                <ul>
                    <li>✅ 后端API正常工作，返回状态码200</li>
                    <li>✅ 数据结构正确，包含data数组和trend数据</li>
                    <li>✅ 前端正在调用API（从日志可见）</li>
                    <li>❓ 前端组件可能没有正确渲染数据</li>
                </ul>
                
                <p><strong>可能原因：</strong></p>
                <ol>
                    <li>前端编译问题</li>
                    <li>React组件状态更新问题</li>
                    <li>数据传递问题</li>
                    <li>CSS样式问题导致表格不可见</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        async function testAPI(type, brand = '', week = '') {
            const statusEl = document.getElementById('apiStatus');
            const responseEl = document.getElementById('apiResponse');
            
            statusEl.textContent = '正在测试API...';
            statusEl.className = 'status';
            
            try {
                let url = `http://localhost:5001/api/trading/dimension-trends?type=${encodeURIComponent(type)}`;
                if (brand) url += `&brand=${encodeURIComponent(brand)}`;
                if (week) url += `&week=${week}`;
                
                const response = await fetch(url);
                const data = await response.json();
                
                if (response.ok) {
                    statusEl.textContent = `✅ API测试成功 (${response.status})`;
                    statusEl.className = 'status success';
                    responseEl.textContent = JSON.stringify(data, null, 2);
                    
                    // 分析数据结构
                    if (data.data && Array.isArray(data.data)) {
                        statusEl.textContent += ` - 返回${data.data.length}个${type}项目`;
                        if (data.data.length > 0 && data.data[0].trend) {
                            statusEl.textContent += `，每项包含${data.data[0].trend.length}周数据`;
                        }
                    }
                } else {
                    statusEl.textContent = `❌ API测试失败 (${response.status})`;
                    statusEl.className = 'status error';
                    responseEl.textContent = JSON.stringify(data, null, 2);
                }
            } catch (error) {
                statusEl.textContent = `❌ API测试错误: ${error.message}`;
                statusEl.className = 'status error';
                responseEl.textContent = error.stack;
            }
        }

        function checkFrontend() {
            const statusEl = document.getElementById('frontendStatus');
            const infoEl = document.getElementById('frontendInfo');
            
            statusEl.textContent = '检查前端页面...';
            
            // 打开前端页面
            window.open('http://localhost:3000/?brand=圣农', '_blank');
            
            statusEl.textContent = '✅ 前端页面已在新标签页打开';
            infoEl.textContent = '请检查：\n1. 页面是否正常加载\n2. 明细数据表格区域是否显示\n3. 浏览器控制台是否有错误信息\n4. 网络标签页是否有API调用';
        }

        function checkConsole() {
            alert('请按F12打开浏览器开发者工具，查看：\n\n1. Console标签页 - 查看JavaScript错误\n2. Network标签页 - 查看API调用\n3. Elements标签页 - 查看DOM结构\n\n特别关注是否有：\n- DetailTable相关的日志\n- API调用失败\n- React组件错误');
        }

        // 页面加载时自动测试
        window.onload = function() {
            document.getElementById('diagnosis').innerHTML += `
                <p><strong>当前时间：</strong>${new Date().toLocaleString()}</p>
                <p><strong>建议操作：</strong></p>
                <ol>
                    <li>点击"测试大区+品牌+周次API"验证后端</li>
                    <li>点击"检查前端页面"打开前端</li>
                    <li>在前端页面按F12查看控制台</li>
                    <li>切换到"大区"维度观察明细表格</li>
                </ol>
            `;
        };
    </script>
</body>
</html>
