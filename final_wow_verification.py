#!/usr/bin/env python3
"""
最终验证周环比修复效果
"""

import requests
import json

def test_wow_consistency():
    """测试周环比数据一致性"""
    print("🔍 测试周环比数据一致性...")
    
    try:
        # 1. 获取供给概览数据
        summary_response = requests.get("http://localhost:5001/api/supply/summary", 
                                      params={'week': 28, 'brand': '圣农'}, 
                                      timeout=10)
        
        if summary_response.status_code != 200:
            print(f"❌ 供给概览API失败: {summary_response.status_code}")
            return False
            
        summary_data = summary_response.json()
        
        # 2. 获取子品牌明细数据
        detail_response = requests.get("http://localhost:5001/api/supply/detail", 
                                     params={'type': '子品牌', 'brand': '圣农', 'selected_week': 28}, 
                                     timeout=10)
        
        if detail_response.status_code != 200:
            print(f"❌ 供给明细API失败: {detail_response.status_code}")
            return False
            
        detail_data = detail_response.json()
        
        if not detail_data.get('data'):
            print("❌ 子品牌明细数据为空")
            return False
            
        detail_item = detail_data['data'][0]
        
        # 3. 比较周环比数据
        print("📊 周环比数据对比:")
        
        # GMV周环比
        summary_gmv_wow = summary_data.get('gmv', {}).get('wow', 0)
        detail_gmv_wow = detail_item.get('gmv_wow', 0)
        gmv_wow_match = abs(summary_gmv_wow - detail_gmv_wow) < 0.001
        print(f"   GMV周环比: 概览 {summary_gmv_wow:.1%} vs 明细 {detail_gmv_wow:.1%} {'✅' if gmv_wow_match else '❌'}")
        
        # 店铺数周环比
        summary_store_wow = summary_data.get('store_count', {}).get('wow', 0)
        detail_store_wow = detail_item.get('store_count_wow', 0)
        store_wow_match = abs(summary_store_wow - detail_store_wow) < 0.001
        print(f"   店铺数周环比: 概览 {summary_store_wow:.1%} vs 明细 {detail_store_wow:.1%} {'✅' if store_wow_match else '❌'}")
        
        # 渗透率周环比
        summary_penetration_wow = summary_data.get('store_penetration', {}).get('wow', 0)
        detail_penetration_wow = detail_item.get('store_penetration_wow', 0)
        penetration_wow_match = abs(summary_penetration_wow - detail_penetration_wow) < 0.001
        print(f"   渗透率周环比: 概览 {summary_penetration_wow:.1%} vs 明细 {detail_penetration_wow:.1%} {'✅' if penetration_wow_match else '❌'}")
        
        # 活跃率周环比
        summary_activity_wow = summary_data.get('store_activity_rate', {}).get('wow', 0)
        detail_activity_wow = detail_item.get('store_activity_rate_wow', 0)
        activity_wow_match = abs(summary_activity_wow - detail_activity_wow) < 0.001
        print(f"   活跃率周环比: 概览 {summary_activity_wow:.1%} vs 明细 {detail_activity_wow:.1%} {'✅' if activity_wow_match else '❌'}")
        
        # 缺货率周环比
        summary_sku_wow = summary_data.get('sku_sold_out_rate', {}).get('wow', 0)
        detail_sku_wow = detail_item.get('sku_sold_out_rate_wow', 0)
        sku_wow_match = abs(summary_sku_wow - detail_sku_wow) < 0.001
        print(f"   缺货率周环比: 概览 {summary_sku_wow:.1%} vs 明细 {detail_sku_wow:.1%} {'✅' if sku_wow_match else '❌'}")
        
        # 总体一致性
        all_wow_match = gmv_wow_match and store_wow_match and penetration_wow_match and activity_wow_match and sku_wow_match
        
        print(f"\n🎯 周环比总体一致性: {'✅ 完全一致' if all_wow_match else '❌ 存在差异'}")
        
        return all_wow_match
        
    except Exception as e:
        print(f"❌ 周环比一致性测试失败: {e}")
        return False

def test_wow_fields_completeness():
    """测试周环比字段完整性"""
    print("\n🔍 测试周环比字段完整性...")
    
    try:
        response = requests.get("http://localhost:5001/api/supply/detail", 
                              params={'type': '子品牌', 'brand': '圣农', 'selected_week': 28}, 
                              timeout=10)
        
        if response.status_code != 200:
            print(f"❌ API调用失败: {response.status_code}")
            return False
            
        data = response.json()
        result_data = data.get('data', [])
        
        if not result_data:
            print("❌ 数据为空")
            return False
            
        item = result_data[0]
        
        # 检查所有期望的周环比字段
        expected_wow_fields = [
            'gmv_wow',
            'store_count_wow', 
            'store_penetration_wow',
            'store_activity_rate_wow',
            'avg_sku_per_store_wow',
            'sku_sold_out_rate_wow'
        ]
        
        print("📊 周环比字段检查:")
        missing_fields = []
        
        for field in expected_wow_fields:
            if field in item:
                value = item[field]
                print(f"   ✅ {field}: {value:.1%}")
            else:
                print(f"   ❌ {field}: 缺失")
                missing_fields.append(field)
        
        if not missing_fields:
            print("\n✅ 所有周环比字段都存在且有值")
            return True
        else:
            print(f"\n❌ 缺失字段: {missing_fields}")
            return False
            
    except Exception as e:
        print(f"❌ 字段完整性测试失败: {e}")
        return False

def test_specific_wow_values():
    """测试具体的周环比数值"""
    print("\n🔍 测试具体的周环比数值...")
    
    try:
        response = requests.get("http://localhost:5001/api/supply/detail", 
                              params={'type': '子品牌', 'brand': '圣农', 'selected_week': 28}, 
                              timeout=10)
        
        if response.status_code != 200:
            print(f"❌ API调用失败: {response.status_code}")
            return False
            
        data = response.json()
        result_data = data.get('data', [])
        
        if not result_data:
            print("❌ 数据为空")
            return False
            
        item = result_data[0]
        
        print("📊 具体周环比数值:")
        print(f"   维度值: {item.get('dimension_value', 'N/A')}")
        print(f"   GMV: ¥{item.get('gmv', 0):,.2f} (周环比: {item.get('gmv_wow', 0):.1%})")
        print(f"   店铺数: {item.get('store_count', 0):,} (周环比: {item.get('store_count_wow', 0):.1%})")
        print(f"   渗透率: {item.get('store_penetration', 0):.1%} (周环比: {item.get('store_penetration_wow', 0):.1%})")
        print(f"   活跃率: {item.get('store_activity_rate', 0):.1%} (周环比: {item.get('store_activity_rate_wow', 0):.1%})")
        print(f"   平均SKU: {item.get('avg_sku_per_store', 0):,} (周环比: {item.get('avg_sku_per_store_wow', 0):.1%})")
        print(f"   缺货率: {item.get('sku_sold_out_rate', 0):.1%} (周环比: {item.get('sku_sold_out_rate_wow', 0):.1%})")
        
        # 验证关键的店铺数周环比是否为期望值 (+0.6%)
        store_count_wow = item.get('store_count_wow', 0)
        expected_wow = 0.006  # 0.6%
        
        if abs(store_count_wow - expected_wow) < 0.001:
            print(f"\n✅ 店铺数周环比正确: {store_count_wow:.1%} (期望: {expected_wow:.1%})")
            return True
        else:
            print(f"\n❌ 店铺数周环比不正确: {store_count_wow:.1%} (期望: {expected_wow:.1%})")
            return False
            
    except Exception as e:
        print(f"❌ 具体数值测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始最终验证周环比修复效果\n")
    
    # 1. 测试周环比数据一致性
    consistency_ok = test_wow_consistency()
    
    # 2. 测试周环比字段完整性
    completeness_ok = test_wow_fields_completeness()
    
    # 3. 测试具体的周环比数值
    values_ok = test_specific_wow_values()
    
    print("\n" + "="*60)
    print("🎯 周环比修复验证结果总结:")
    print("="*60)
    
    print(f"1. 数据一致性: {'✅ 通过' if consistency_ok else '❌ 失败'}")
    print(f"2. 字段完整性: {'✅ 通过' if completeness_ok else '❌ 失败'}")
    print(f"3. 数值正确性: {'✅ 通过' if values_ok else '❌ 失败'}")
    
    overall_success = consistency_ok and completeness_ok and values_ok
    
    if overall_success:
        print("\n🎉 周环比修复完全成功！")
        print("\n📋 修复成果:")
        print("   ✅ 上方卡片和下方表格的周环比数据完全一致")
        print("   ✅ 所有周环比字段都已添加并正确计算")
        print("   ✅ 店铺数周环比从0%修复为+0.6%")
        print("   ✅ 计算逻辑与供给概览完全一致")
        print("\n🚀 现在访问页面可以看到一致的周环比数据！")
    else:
        print("\n❌ 周环比修复仍需完善")
        
    return overall_success

if __name__ == "__main__":
    main()
