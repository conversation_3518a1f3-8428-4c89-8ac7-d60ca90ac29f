<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>活动图表调试</title>
    <style>
        body {
            font-family: monospace;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .test-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .result {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            color: #52c41a;
        }
        .error {
            color: #ff4d4f;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #40a9ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>活动图表调试 - 问题诊断</h1>
        
        <div class="test-section">
            <div class="test-title">1. 测试后端API直接调用</div>
            <button onclick="testBackendAPI()">测试后端API</button>
            <div id="backendResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. 测试前端代理</div>
            <button onclick="testFrontendProxy()">测试前端代理</button>
            <div id="proxyResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. 检查浏览器控制台</div>
            <button onclick="checkConsole()">检查控制台日志</button>
            <div id="consoleResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">4. 模拟前端组件调用</div>
            <button onclick="simulateComponentCall()">模拟组件调用</button>
            <div id="componentResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">5. 诊断建议</div>
            <div id="diagnosisResult" class="result">
点击上方按钮进行诊断...

预期问题可能包括：
1. 前端代理配置问题
2. API响应格式不匹配
3. 前端组件状态管理问题
4. 网络请求被拦截或失败
5. 数据格式转换错误
            </div>
        </div>
    </div>
    
    <script>
        let testResults = {};
        
        async function testBackendAPI() {
            const resultDiv = document.getElementById('backendResult');
            resultDiv.textContent = '正在测试后端API...';
            
            try {
                const response = await fetch('http://127.0.0.1:5001/api/activity/platform-charts?brand=圣农');
                const data = await response.json();
                
                if (response.ok) {
                    testResults.backend = { success: true, data };
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 后端API调用成功
状态码: ${response.status}
平台数量: ${data.platform_data ? data.platform_data.length : 0}

响应数据结构:
${JSON.stringify(data, null, 2)}`;
                } else {
                    testResults.backend = { success: false, error: data };
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 后端API调用失败
状态码: ${response.status}
错误信息: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                testResults.backend = { success: false, error: error.message };
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 后端API请求异常: ${error.message}`;
            }
        }
        
        async function testFrontendProxy() {
            const resultDiv = document.getElementById('proxyResult');
            resultDiv.textContent = '正在测试前端代理...';
            
            try {
                // 通过前端代理调用
                const response = await fetch('/api/activity/platform-charts?brand=圣农');
                const data = await response.json();
                
                if (response.ok) {
                    testResults.proxy = { success: true, data };
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 前端代理调用成功
状态码: ${response.status}
平台数量: ${data.platform_data ? data.platform_data.length : 0}

响应数据结构:
${JSON.stringify(data, null, 2)}`;
                } else {
                    testResults.proxy = { success: false, error: data };
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 前端代理调用失败
状态码: ${response.status}
错误信息: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                testResults.proxy = { success: false, error: error.message };
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 前端代理请求异常: ${error.message}
这可能是因为前端开发服务器没有正确代理请求到后端`;
            }
        }
        
        function checkConsole() {
            const resultDiv = document.getElementById('consoleResult');
            
            // 检查控制台错误
            const originalError = console.error;
            const originalLog = console.log;
            const logs = [];
            
            console.error = function(...args) {
                logs.push({ type: 'error', message: args.join(' ') });
                originalError.apply(console, args);
            };
            
            console.log = function(...args) {
                logs.push({ type: 'log', message: args.join(' ') });
                originalLog.apply(console, args);
            };
            
            resultDiv.className = 'result success';
            resultDiv.textContent = `📊 控制台检查结果:

请打开浏览器开发者工具 (F12)，查看以下内容：

1. Console 标签页：
   - 查找包含 "ActivityCharts" 的日志
   - 查找包含 "ActivityDataModule" 的日志
   - 查找任何错误信息

2. Network 标签页：
   - 查找 "/api/activity/platform-charts" 请求
   - 检查请求状态码和响应内容
   - 确认请求是否成功发送

3. 关键调试信息：
   - 🔍 ActivityDataModule API响应
   - 🔍 ActivityCharts 组件状态
   - ⏳ 或 ❌ 加载状态信息

如果看到持续的加载状态，说明数据没有正确传递到组件。`;
        }
        
        async function simulateComponentCall() {
            const resultDiv = document.getElementById('componentResult');
            resultDiv.textContent = '正在模拟组件调用...';
            
            try {
                // 模拟前端组件的API调用流程
                const response = await fetch('/api/activity/platform-charts?brand=圣农');
                const platformCharts = await response.json();
                
                // 模拟组件状态检查
                const loading = false;
                const data = platformCharts;
                const hasData = !!data;
                const hasPlatformData = !!(data && data.platform_data);
                const platformDataLength = data && data.platform_data ? data.platform_data.length : 0;
                
                testResults.component = {
                    loading,
                    hasData,
                    hasPlatformData,
                    platformDataLength,
                    data
                };
                
                if (hasPlatformData && platformDataLength > 0) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 组件调用模拟成功
组件状态:
  loading: ${loading}
  hasData: ${hasData}
  hasPlatformData: ${hasPlatformData}
  platformDataLength: ${platformDataLength}

组件应该渲染图表，如果仍显示加载状态，可能是：
1. 前端组件状态管理问题
2. React组件重新渲染问题
3. 数据格式不匹配

平台数据:
${data.platform_data.map(item => `  ${item.platform}: GMV=${item.activity_gmv.toLocaleString()}`).join('\\n')}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 组件调用模拟失败
组件状态:
  loading: ${loading}
  hasData: ${hasData}
  hasPlatformData: ${hasPlatformData}
  platformDataLength: ${platformDataLength}

问题原因: 没有获取到有效的平台数据`;
                }
            } catch (error) {
                testResults.component = { error: error.message };
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 组件调用模拟异常: ${error.message}`;
            }
        }
        
        function updateDiagnosis() {
            const resultDiv = document.getElementById('diagnosisResult');
            
            let diagnosis = '🔍 诊断结果:\\n\\n';
            
            if (testResults.backend && testResults.backend.success) {
                diagnosis += '✅ 后端API工作正常\\n';
            } else {
                diagnosis += '❌ 后端API有问题\\n';
            }
            
            if (testResults.proxy && testResults.proxy.success) {
                diagnosis += '✅ 前端代理工作正常\\n';
            } else {
                diagnosis += '❌ 前端代理有问题\\n';
            }
            
            if (testResults.component && testResults.component.hasPlatformData) {
                diagnosis += '✅ 数据格式正确\\n';
            } else {
                diagnosis += '❌ 数据格式有问题\\n';
            }
            
            diagnosis += '\\n🛠️ 修复建议:\\n';
            
            if (testResults.backend && testResults.backend.success && 
                testResults.proxy && testResults.proxy.success &&
                testResults.component && testResults.component.hasPlatformData) {
                diagnosis += '数据流程正常，问题可能在于：\\n';
                diagnosis += '1. React组件状态更新问题\\n';
                diagnosis += '2. 组件重新渲染时机问题\\n';
                diagnosis += '3. 检查浏览器控制台的具体错误信息\\n';
                diagnosis += '4. 确认ActivityCharts组件是否正确接收到props';
            } else {
                diagnosis += '请按顺序修复上述问题';
            }
            
            resultDiv.textContent = diagnosis;
        }
        
        // 页面加载时自动开始诊断
        window.onload = function() {
            console.log('🔍 开始活动图表问题诊断...');
            setTimeout(() => {
                testBackendAPI();
                setTimeout(() => {
                    testFrontendProxy();
                    setTimeout(() => {
                        simulateComponentCall();
                        setTimeout(updateDiagnosis, 1000);
                    }, 1000);
                }, 1000);
            }, 500);
        };
    </script>
</body>
</html>
