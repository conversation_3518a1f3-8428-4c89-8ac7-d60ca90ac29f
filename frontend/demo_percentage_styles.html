<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>百分比样式改进演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }
        .percentage-positive {
            color: #52c41a;
            background-color: #f6ffed;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        .percentage-negative {
            color: #ff4d4f;
            background-color: #fff2f0;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        .percentage-neutral {
            color: #666;
            font-weight: bold;
        }
        .example-text {
            line-height: 1.8;
            font-size: 14px;
            margin: 10px 0;
            padding: 12px;
            background: #fafafa;
            border-radius: 4px;
        }
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        .btn:hover {
            background: #40a9ff;
        }
        .improvement-list {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 6px;
            padding: 16px;
            margin: 16px 0;
        }
        .improvement-list h4 {
            margin: 0 0 12px 0;
            color: #1890ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 百分比样式改进演示</h1>
        <p>展示智能的绿涨红跌样式逻辑</p>
        
        <div class="demo-section">
            <h3>🎯 改进前后对比</h3>
            
            <div class="improvement-list">
                <h4>✅ 改进内容</h4>
                <ul>
                    <li><strong>上下文感知</strong>：根据文字上下文判断涨跌含义</li>
                    <li><strong>占比指标识别</strong>：占比、渗透率等指标不显示红绿色</li>
                    <li><strong>语义理解</strong>：识别"下跌"、"增长"等关键词</li>
                    <li><strong>智能着色</strong>：结合数值正负和语义含义</li>
                </ul>
            </div>
            
            <button class="btn" onclick="showOriginalStyle()">显示改进前样式</button>
            <button class="btn" onclick="showImprovedStyle()">显示改进后样式</button>
            <button class="btn" onclick="clearDisplay()">清空显示</button>
            
            <div id="displayArea" style="margin-top: 16px;">
                点击上方按钮查看对比效果
            </div>
        </div>
        
        <div class="demo-section">
            <h3>📋 样式规则说明</h3>
            
            <h4>🔴 红色显示（负面）</h4>
            <ul>
                <li>包含"下跌"、"下降"、"减少"、"降低"等词汇</li>
                <li>包含"缺货"、"成本"等负面指标</li>
                <li>数值为负数且无特殊上下文</li>
            </ul>
            
            <h4>🟢 绿色显示（正面）</h4>
            <ul>
                <li>包含"增长"、"上涨"、"提升"、"改善"、"优化"等词汇</li>
                <li>数值为正数且无特殊上下文</li>
            </ul>
            
            <h4>⚫ 中性显示（占比指标）</h4>
            <ul>
                <li>包含"占比"、"渗透率"、"活跃度"、"转化率"等词汇</li>
                <li>包含"完成率"、"覆盖率"等比率指标</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <h3>🧪 实际应用示例</h3>
            <div class="example-text" id="exampleText">
                点击上方按钮查看实际应用效果
            </div>
        </div>
    </div>

    <script>
        function renderWithOriginalStyle(text) {
            // 原始简单逻辑：只看数值正负
            return text.replace(/([+-]?\d+\.?\d*%)/g, (match) => {
                const value = parseFloat(match.replace('%', ''));
                let className = 'percentage-neutral';
                
                if (value > 0) {
                    className = 'percentage-positive';
                } else if (value < 0) {
                    className = 'percentage-negative';
                }
                
                return `<span class="${className}">${match}</span>`;
            });
        }
        
        function renderWithImprovedStyle(text) {
            // 改进后的智能逻辑
            return text.replace(/([\u4e00-\u9fa5\w\s]*?)([+-]?\d+\.?\d*%)/g, (match, context, percentage) => {
                const value = parseFloat(percentage.replace('%', ''));
                const contextText = context.toLowerCase();
                
                let className = 'percentage-neutral';
                
                // 判断是否为占比指标（不需要红绿色）
                const isRatioIndicator = contextText.includes('占比') || 
                                        contextText.includes('渗透率') || 
                                        contextText.includes('活跃度') || 
                                        contextText.includes('转化率') ||
                                        contextText.includes('完成率') ||
                                        contextText.includes('覆盖率');
                
                if (!isRatioIndicator) {
                    // 判断上下文是否表示下跌/负面
                    const isNegativeContext = contextText.includes('下跌') || 
                                             contextText.includes('下降') || 
                                             contextText.includes('减少') || 
                                             contextText.includes('降低') ||
                                             contextText.includes('缺货') ||
                                             contextText.includes('成本');
                    
                    // 判断上下文是否表示上涨/正面
                    const isPositiveContext = contextText.includes('增长') || 
                                             contextText.includes('上涨') || 
                                             contextText.includes('提升') || 
                                             contextText.includes('改善') ||
                                             contextText.includes('优化');
                    
                    if (isNegativeContext) {
                        className = 'percentage-negative';
                    } else if (isPositiveContext) {
                        className = 'percentage-positive';
                    } else if (value > 0) {
                        className = 'percentage-positive';
                    } else if (value < 0) {
                        className = 'percentage-negative';
                    }
                }
                
                return context + `<span class="${className}">${percentage}</span>`;
            });
        }
        
        function showOriginalStyle() {
            const sampleText = `
## 业务数据分析

### 交易表现
- GMV增长+15.6%，表现良好
- 平台占比：美团45.2%，饿了么28.7%
- 成本上升****%，需要关注
- 转化率达到8.9%，符合预期

### 活动效果
- 活动GMV下跌-5.8%，需要优化
- 活动占比68.5%，保持稳定
- 门店渗透率78.5%，覆盖良好
- 缺货率上升****%，影响体验

### 用户数据
- 新用户增长+12.3%，势头良好
- 用户活跃度85.7%，表现稳定
- ARPU提升****%，价值增长
- 流失率下降-1.5%，留存改善
            `;
            
            document.getElementById('displayArea').innerHTML = `
                <h4>改进前样式（简单正负判断）</h4>
                <div class="example-text">${renderWithOriginalStyle(sampleText)}</div>
            `;
            
            document.getElementById('exampleText').innerHTML = `
                <strong>问题：</strong><br>
                • "成本上升****%" 显示为绿色（错误，成本上升是负面的）<br>
                • "缺货率上升****%" 显示为绿色（错误，缺货率上升是负面的）<br>
                • "流失率下降-1.5%" 显示为红色（错误，流失率下降是正面的）<br>
                • "门店渗透率78.5%" 显示为中性（正确，但缺乏一致性）
            `;
        }
        
        function showImprovedStyle() {
            const sampleText = `
## 业务数据分析

### 交易表现
- GMV增长+15.6%，表现良好
- 平台占比：美团45.2%，饿了么28.7%
- 成本上升****%，需要关注
- 转化率达到8.9%，符合预期

### 活动效果
- 活动GMV下跌-5.8%，需要优化
- 活动占比68.5%，保持稳定
- 门店渗透率78.5%，覆盖良好
- 缺货率上升****%，影响体验

### 用户数据
- 新用户增长+12.3%，势头良好
- 用户活跃度85.7%，表现稳定
- ARPU提升****%，价值增长
- 流失率下降-1.5%，留存改善
            `;
            
            document.getElementById('displayArea').innerHTML = `
                <h4>改进后样式（智能语义判断）</h4>
                <div class="example-text">${renderWithImprovedStyle(sampleText)}</div>
            `;
            
            document.getElementById('exampleText').innerHTML = `
                <strong>改进效果：</strong><br>
                • "成本上升****%" 显示为红色（正确，成本上升是负面的）<br>
                • "缺货率上升****%" 显示为红色（正确，缺货是负面的）<br>
                • "流失率下降-1.5%" 显示为绿色（正确，流失率下降是正面的）<br>
                • "门店渗透率78.5%" 显示为中性（正确，占比指标不用红绿色）<br>
                • "活动占比68.5%" 显示为中性（正确，占比指标保持中性）
            `;
        }
        
        function clearDisplay() {
            document.getElementById('displayArea').innerHTML = '点击上方按钮查看对比效果';
            document.getElementById('exampleText').innerHTML = '点击上方按钮查看实际应用效果';
        }
    </script>
</body>
</html>
