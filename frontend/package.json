{"name": "weekly-report-dashboard", "version": "1.0.0", "description": "Weekly Report Dashboard Frontend", "main": "src/index.js", "scripts": {"start": "PORT=3001 react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"@ant-design/icons": "^5.2.6", "antd": "^5.12.0", "axios": "^1.6.0", "dayjs": "^1.11.13", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "moment": "^2.29.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5002"}