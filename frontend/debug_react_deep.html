<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React深度调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .debug-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e6e6e6;
            border-radius: 6px;
        }
        .debug-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .result {
            background: #f8f8f8;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 11px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #52c41a; }
        .error { color: #ff4d4f; }
        .warning { color: #faad14; }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>React深度调试工具</h1>
        <p>深度分析React组件结构和状态</p>
        
        <div class="debug-section">
            <h3>🔍 React实例深度分析</h3>
            <button class="debug-button" onclick="analyzeReactInstances()">分析React实例</button>
            <button class="debug-button" onclick="findSelectComponent()">查找Select组件</button>
            <button class="debug-button" onclick="analyzeSelectProps()">分析Select属性</button>
            <div id="react-analysis" class="result"></div>
        </div>

        <div class="debug-section">
            <h3>🎯 直接React操作测试</h3>
            <button class="debug-button" onclick="testReactChange('渠道类型')">React切换到渠道类型</button>
            <button class="debug-button" onclick="testReactChange('大区')">React切换到大区</button>
            <button class="debug-button" onclick="testReactChange('品线')">React切换到品线</button>
            <div id="react-test" class="result"></div>
        </div>

        <div class="debug-section">
            <h3>🔧 组件状态操作</h3>
            <button class="debug-button" onclick="forceComponentUpdate()">强制组件更新</button>
            <button class="debug-button" onclick="triggerStateChange()">触发状态变化</button>
            <div id="state-test" class="result"></div>
        </div>
    </div>

    <script>
        function log(message, containerId = 'react-analysis') {
            const container = document.getElementById(containerId);
            const timestamp = new Date().toLocaleTimeString();
            container.textContent += `[${timestamp}] ${message}\n`;
            container.scrollTop = container.scrollHeight;
        }

        function analyzeReactInstances() {
            log('🔍 开始分析React实例...');
            
            // 查找维度选择器
            let selectElement = document.querySelector('.dimension-section .dimension-header .ant-select');
            if (!selectElement) {
                const cards = document.querySelectorAll('.ant-card');
                for (const card of cards) {
                    const title = card.querySelector('.section-title, .ant-card-head-title');
                    if (title && (title.textContent.includes('多维') || title.textContent.includes('维度'))) {
                        selectElement = card.querySelector('.ant-select');
                        break;
                    }
                }
            }

            if (!selectElement) {
                log('❌ 未找到Select元素');
                return;
            }

            log('✅ 找到Select元素');

            // 分析所有可能的React属性
            const keys = Object.keys(selectElement);
            log(`元素属性数量: ${keys.length}`);
            
            const reactKeys = keys.filter(key => 
                key.includes('react') || 
                key.includes('React') || 
                key.includes('fiber') || 
                key.includes('Fiber') ||
                key.startsWith('__')
            );
            
            log(`React相关属性: ${reactKeys.join(', ')}`);

            // 尝试获取React实例
            reactKeys.forEach(key => {
                try {
                    const instance = selectElement[key];
                    if (instance && typeof instance === 'object') {
                        log(`\n=== ${key} ===`);
                        log(`类型: ${instance.constructor?.name || 'Unknown'}`);
                        
                        if (instance.type) {
                            log(`组件类型: ${instance.type.name || instance.type}`);
                        }
                        
                        if (instance.memoizedProps) {
                            const props = instance.memoizedProps;
                            log(`Props: value=${props.value}, onChange=${typeof props.onChange}`);
                            
                            if (props.onChange) {
                                log(`onChange函数: ${props.onChange.toString().substring(0, 100)}...`);
                            }
                        }
                        
                        if (instance.props) {
                            const props = instance.props;
                            log(`直接Props: value=${props.value}, onChange=${typeof props.onChange}`);
                        }
                        
                        if (instance.stateNode) {
                            log(`StateNode: ${instance.stateNode.constructor?.name || 'Unknown'}`);
                        }
                    }
                } catch (error) {
                    log(`分析 ${key} 时出错: ${error.message}`);
                }
            });
        }

        function findSelectComponent() {
            log('🎯 查找Select组件...', 'react-analysis');
            
            const selectElement = document.querySelector('.dimension-section .ant-select');
            if (!selectElement) {
                log('❌ 未找到Select元素', 'react-analysis');
                return;
            }

            // 使用新的React实例获取方法
            const reactInstance = getReactInstance(selectElement);
            
            if (reactInstance) {
                log('✅ 找到React实例', 'react-analysis');
                
                // 遍历组件树查找Select相关组件
                traverseReactTree(reactInstance, 0);
            } else {
                log('❌ 未找到React实例', 'react-analysis');
            }
        }

        function getReactInstance(element) {
            try {
                // React 18+
                if (element._reactInternals) {
                    return element._reactInternals;
                }
                
                // React 17
                if (element._reactInternalFiber) {
                    return element._reactInternalFiber;
                }
                
                // 旧版本React
                const keys = Object.keys(element);
                const reactKey = keys.find(key => 
                    key.startsWith('__reactInternalInstance') || 
                    key.startsWith('__reactFiber')
                );
                
                if (reactKey) {
                    return element[reactKey];
                }

                return null;
            } catch (error) {
                log(`获取React实例失败: ${error.message}`, 'react-analysis');
                return null;
            }
        }

        function traverseReactTree(fiber, depth) {
            if (!fiber || depth > 5) return;
            
            const indent = '  '.repeat(depth);
            const componentName = fiber.type?.name || fiber.type?.displayName || fiber.type || 'Unknown';
            
            log(`${indent}${componentName}`, 'react-analysis');
            
            if (fiber.memoizedProps) {
                const props = fiber.memoizedProps;
                if (props.onChange) {
                    log(`${indent}  ✅ 找到onChange: ${typeof props.onChange}`, 'react-analysis');
                }
                if (props.value !== undefined) {
                    log(`${indent}  📊 value: ${props.value}`, 'react-analysis');
                }
            }
            
            // 遍历子组件
            if (fiber.child) {
                traverseReactTree(fiber.child, depth + 1);
            }
            
            // 遍历兄弟组件
            if (fiber.sibling && depth < 3) {
                traverseReactTree(fiber.sibling, depth);
            }
        }

        function analyzeSelectProps() {
            log('📊 分析Select组件属性...', 'react-analysis');
            
            const selectElement = document.querySelector('.dimension-section .ant-select');
            if (!selectElement) {
                log('❌ 未找到Select元素', 'react-analysis');
                return;
            }

            const reactInstance = getReactInstance(selectElement);
            if (!reactInstance) {
                log('❌ 未找到React实例', 'react-analysis');
                return;
            }

            // 深度分析props
            if (reactInstance.memoizedProps) {
                const props = reactInstance.memoizedProps;
                log('=== memoizedProps ===', 'react-analysis');
                Object.keys(props).forEach(key => {
                    const value = props[key];
                    const type = typeof value;
                    log(`${key}: ${type} = ${type === 'function' ? '[Function]' : value}`, 'react-analysis');
                });
            }

            if (reactInstance.props) {
                const props = reactInstance.props;
                log('\n=== props ===', 'react-analysis');
                Object.keys(props).forEach(key => {
                    const value = props[key];
                    const type = typeof value;
                    log(`${key}: ${type} = ${type === 'function' ? '[Function]' : value}`, 'react-analysis');
                });
            }
        }

        function testReactChange(dimension) {
            log(`🧪 测试React切换到: ${dimension}`, 'react-test');
            
            const selectElement = document.querySelector('.dimension-section .ant-select');
            if (!selectElement) {
                log('❌ 未找到Select元素', 'react-test');
                return;
            }

            const reactInstance = getReactInstance(selectElement);
            if (!reactInstance) {
                log('❌ 未找到React实例', 'react-test');
                return;
            }

            // 尝试调用onChange
            let success = false;
            
            if (reactInstance.memoizedProps && typeof reactInstance.memoizedProps.onChange === 'function') {
                try {
                    log(`🎯 调用memoizedProps.onChange(${dimension})`, 'react-test');
                    reactInstance.memoizedProps.onChange(dimension);
                    success = true;
                } catch (error) {
                    log(`❌ memoizedProps.onChange调用失败: ${error.message}`, 'react-test');
                }
            }

            if (!success && reactInstance.props && typeof reactInstance.props.onChange === 'function') {
                try {
                    log(`🎯 调用props.onChange(${dimension})`, 'react-test');
                    reactInstance.props.onChange(dimension);
                    success = true;
                } catch (error) {
                    log(`❌ props.onChange调用失败: ${error.message}`, 'react-test');
                }
            }

            if (!success) {
                log('❌ 未找到可用的onChange方法', 'react-test');
            }

            // 验证结果
            setTimeout(() => {
                const currentValue = document.querySelector('.dimension-section .ant-select-selection-item')?.textContent?.trim();
                log(`验证结果: 当前值 = ${currentValue}`, 'react-test');
            }, 500);
        }

        function forceComponentUpdate() {
            log('🔧 尝试强制组件更新...', 'state-test');
            
            const selectElement = document.querySelector('.dimension-section .ant-select');
            if (!selectElement) {
                log('❌ 未找到Select元素', 'state-test');
                return;
            }

            const reactInstance = getReactInstance(selectElement);
            if (!reactInstance) {
                log('❌ 未找到React实例', 'state-test');
                return;
            }

            // 尝试强制更新
            try {
                if (reactInstance.stateNode && reactInstance.stateNode.forceUpdate) {
                    reactInstance.stateNode.forceUpdate();
                    log('✅ 调用了forceUpdate', 'state-test');
                } else {
                    log('❌ 未找到forceUpdate方法', 'state-test');
                }
            } catch (error) {
                log(`❌ forceUpdate失败: ${error.message}`, 'state-test');
            }
        }

        function triggerStateChange() {
            log('🔄 尝试触发状态变化...', 'state-test');
            
            // 尝试触发各种事件
            const selectElement = document.querySelector('.dimension-section .ant-select');
            if (!selectElement) {
                log('❌ 未找到Select元素', 'state-test');
                return;
            }

            const events = ['focus', 'mousedown', 'mouseup', 'click', 'change'];
            
            events.forEach(eventType => {
                try {
                    const event = new Event(eventType, { bubbles: true, cancelable: true });
                    selectElement.dispatchEvent(event);
                    log(`✅ 触发了 ${eventType} 事件`, 'state-test');
                } catch (error) {
                    log(`❌ 触发 ${eventType} 事件失败: ${error.message}`, 'state-test');
                }
            });
        }
    </script>
</body>
</html>
