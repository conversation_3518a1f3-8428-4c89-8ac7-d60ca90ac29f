<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI洞察Markdown样式演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }
        .insights-display {
            background: #fafafa;
            border: 1px solid #f0f0f0;
            border-radius: 6px;
            padding: 16px;
            line-height: 1.6;
            font-size: 14px;
        }
        .insights-display h1 {
            color: #262626;
            font-size: 20px;
            font-weight: 600;
            margin: 24px 0 16px 0;
        }
        .insights-display h2 {
            color: #262626;
            font-size: 18px;
            font-weight: 600;
            margin: 20px 0 12px 0;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 6px;
        }
        .insights-display h3 {
            color: #262626;
            font-size: 16px;
            font-weight: 600;
            margin: 16px 0 8px 0;
            border-bottom: 1px solid #e8e8e8;
            padding-bottom: 4px;
        }
        .insights-display ul {
            margin: 8px 0;
            padding-left: 20px;
        }
        .insights-display li {
            margin: 4px 0;
            padding-left: 8px;
        }
        .percentage-positive {
            color: #52c41a;
            background-color: #f6ffed;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        .percentage-negative {
            color: #ff4d4f;
            background-color: #fff2f0;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        .percentage-neutral {
            color: #666;
            font-weight: bold;
        }
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        .btn:hover {
            background: #40a9ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 AI洞察Markdown样式演示</h1>
        <p>展示AI洞察报告的markdown渲染效果和百分比样式</p>
        
        <div class="demo-section">
            <h3>📊 样式效果预览</h3>
            <button class="btn" onclick="showSampleReport()">显示示例报告</button>
            <button class="btn" onclick="showPercentageDemo()">显示百分比样式</button>
            <button class="btn" onclick="clearDisplay()">清空显示</button>
            
            <div class="insights-display" id="displayArea" style="margin-top: 16px; min-height: 100px;">
                点击上方按钮查看样式效果
            </div>
        </div>
        
        <div class="demo-section">
            <h3>🎯 功能改进说明</h3>
            <ul>
                <li>✅ <strong>Markdown渲染</strong>：支持标题、列表、粗体等markdown格式</li>
                <li>✅ <strong>百分比样式</strong>：绿涨红跌的高亮显示，正数绿色背景，负数红色背景</li>
                <li>✅ <strong>数据收集优化</strong>：改进数据收集逻辑，减少"数据不可用"的情况</li>
                <li>✅ <strong>归因分析框架</strong>：按照"驱动因素→结果指标→归因分析"的逻辑重新组织</li>
                <li>✅ <strong>业务逻辑优化</strong>：交易数据作为结果指标，其他四项作为驱动因素</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <h3>📋 新的分析框架</h3>
            <ol>
                <li><strong>交易表现总览</strong>（结果指标）- GMV总量、趋势、平台分布</li>
                <li><strong>活动驱动分析</strong>（驱动因素一）- 活动对GMV的贡献</li>
                <li><strong>投放效果分析</strong>（驱动因素二）- RTB投放对交易的促进</li>
                <li><strong>供给能力分析</strong>（驱动因素三）- 供给对交易的支撑</li>
                <li><strong>用户价值分析</strong>（驱动因素四）- 用户对交易的贡献</li>
                <li><strong>综合归因与建议</strong> - 核心驱动因素识别和优化建议</li>
            </ol>
        </div>
    </div>

    <script>
        function renderMarkdown(text) {
            // 处理标题
            let html = text
                .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                .replace(/^# (.*$)/gim, '<h1>$1</h1>');
            
            // 处理列表
            html = html
                .replace(/^\* (.*$)/gim, '<li>$1</li>')
                .replace(/^- (.*$)/gim, '<li>$1</li>');
            
            // 包装连续的li标签
            html = html.replace(/(<li[^>]*>.*?<\/li>\s*)+/gs, '<ul>$&</ul>');
            
            // 处理百分比数字的样式（绿涨红跌）
            html = html.replace(/([+-]?\d+\.?\d*%)/g, (match) => {
                const value = parseFloat(match.replace('%', ''));
                let className = 'percentage-neutral';
                
                if (value > 0) {
                    className = 'percentage-positive';
                } else if (value < 0) {
                    className = 'percentage-negative';
                }
                
                return `<span class="${className}">${match}</span>`;
            });
            
            // 处理粗体
            html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            
            // 处理换行
            html = html.replace(/\n/g, '<br>');
            
            return html;
        }
        
        function showSampleReport() {
            const sampleText = `# 圣农品牌第47周业务数据分析报告

## 1. 交易表现总览（结果指标）

### 整体交易表现
- GMV总量达到**2,847万元**，环比增长**+12.5%**，同比增长**+8.3%**
- 平台表现：美团贡献**45.2%**，饿了么**28.7%**，京东到家**15.1%**
- 子品牌贡献：核心品牌增长**+15.2%**，新品牌表现**-3.4%**

## 2. 活动驱动分析（驱动因素一）

### 活动效果评估
- 活动GMV占比**68.5%**，环比提升**+5.2%**
- 活动费比控制在**12.8%**，同比优化**-1.5%**
- 平台活动差异：美团活动转化率**+18.7%**，饿了么**+12.3%**

## 3. 投放效果分析（驱动因素二）

### 投放效率
- RTB投放ROI达到**3.2**，环比提升**+0.8**
- 新客获取成本**45.6元**，同比下降**-12.4%**
- 投放转化率**8.9%**，环比提升******%**

## 4. 供给能力分析（驱动因素三）

### 供给覆盖
- 门店渗透率**78.5%**，环比提升******%**
- 门店活跃度**85.7%**，同比提升******%**
- SKU缺货率**2.3%**，环比下降**-0.8%**

## 5. 用户价值分析（驱动因素四）

### 用户结构
- 新用户占比**32.1%**，环比增长******%**
- 老用户ARPU**156.8元**，同比提升**+11.2%**
- 用户复购率**42.6%**，环比提升******%**`;
            
            document.getElementById('displayArea').innerHTML = renderMarkdown(sampleText);
        }
        
        function showPercentageDemo() {
            const percentageText = `## 百分比样式演示

### 正增长（绿色）
- 销售增长：**+15.6%**
- 用户增长：******%**
- 转化率提升：**+12.3%**

### 负增长（红色）
- 成本上升：**-5.2%**
- 缺货率：**-2.1%**
- 流失率：**-8.7%**

### 中性数据（灰色）
- 基准值：**0%**
- 目标完成：**100%**
- 覆盖率：**85%**`;
            
            document.getElementById('displayArea').innerHTML = renderMarkdown(percentageText);
        }
        
        function clearDisplay() {
            document.getElementById('displayArea').innerHTML = '点击上方按钮查看样式效果';
        }
    </script>
</body>
</html>
