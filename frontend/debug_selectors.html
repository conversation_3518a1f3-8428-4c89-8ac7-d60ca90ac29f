<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选择器调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .debug-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e6e6e6;
            border-radius: 6px;
        }
        .debug-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .debug-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .debug-button:hover {
            background: #40a9ff;
        }
        .result {
            background: #f8f8f8;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .success { color: #52c41a; }
        .error { color: #ff4d4f; }
        .warning { color: #faad14; }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>选择器调试工具</h1>
        <p>此工具用于调试和验证页面中的选择器定位</p>
        
        <div class="debug-section">
            <div class="debug-title">🔍 查找所有选择器</div>
            <button class="debug-button" onclick="findAllSelectors()">查找所有 ant-select</button>
            <button class="debug-button" onclick="findDimensionSelectors()">查找维度相关选择器</button>
            <button class="debug-button" onclick="findCards()">查找所有卡片</button>
            <div id="selectors-result" class="result"></div>
        </div>

        <div class="debug-section">
            <div class="debug-title">📋 检查维度选择器</div>
            <button class="debug-button" onclick="checkDimensionSelector()">检查维度选择器</button>
            <button class="debug-button" onclick="getCurrentDimension()">获取当前维度</button>
            <button class="debug-button" onclick="testDimensionClick()">测试点击维度选择器</button>
            <div id="dimension-result" class="result"></div>
        </div>

        <div class="debug-section">
            <div class="debug-title">🎯 测试新的选择器逻辑</div>
            <button class="debug-button" onclick="testNewSelectorLogic()">测试新选择器逻辑</button>
            <button class="debug-button" onclick="testBackupSelector()">测试备选选择器</button>
            <div id="new-logic-result" class="result"></div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            return `[${timestamp}] ${prefix} ${message}\n`;
        }

        function findAllSelectors() {
            const result = document.getElementById('selectors-result');
            let output = '';
            
            output += log('查找所有 ant-select 相关元素...', 'info');
            
            // 查找所有选择器
            const allSelectors = document.querySelectorAll('.ant-select');
            output += log(`找到 ${allSelectors.length} 个 .ant-select 元素`, 'info');
            
            allSelectors.forEach((selector, index) => {
                const selectorElement = selector.querySelector('.ant-select-selector');
                const selectionItem = selector.querySelector('.ant-select-selection-item');
                const currentValue = selectionItem?.textContent?.trim() || '无值';
                
                output += log(`选择器 ${index + 1}: 值="${currentValue}", 类名="${selector.className}"`, 'info');
                
                // 查找父级容器
                let parent = selector.parentElement;
                let parentInfo = '';
                while (parent && parentInfo.length < 100) {
                    if (parent.className) {
                        parentInfo += parent.className.split(' ')[0] + ' > ';
                    }
                    parent = parent.parentElement;
                }
                output += log(`  父级路径: ${parentInfo}`, 'info');
            });
            
            result.textContent = output;
        }

        function findDimensionSelectors() {
            const result = document.getElementById('selectors-result');
            let output = '';
            
            output += log('查找维度相关选择器...', 'info');
            
            // 方法1：通过 .dimension-section 查找
            const dimensionSection = document.querySelector('.dimension-section');
            if (dimensionSection) {
                output += log('✅ 找到 .dimension-section', 'success');
                
                const selectors = dimensionSection.querySelectorAll('.ant-select');
                output += log(`在 dimension-section 中找到 ${selectors.length} 个选择器`, 'info');
                
                selectors.forEach((selector, index) => {
                    const selectionItem = selector.querySelector('.ant-select-selection-item');
                    const currentValue = selectionItem?.textContent?.trim() || '无值';
                    output += log(`  选择器 ${index + 1}: "${currentValue}"`, 'info');
                });
            } else {
                output += log('❌ 未找到 .dimension-section', 'error');
            }
            
            // 方法2：通过 .dimension-header 查找
            const dimensionHeader = document.querySelector('.dimension-header');
            if (dimensionHeader) {
                output += log('✅ 找到 .dimension-header', 'success');
                
                const selector = dimensionHeader.querySelector('.ant-select');
                if (selector) {
                    const selectionItem = selector.querySelector('.ant-select-selection-item');
                    const currentValue = selectionItem?.textContent?.trim() || '无值';
                    output += log(`dimension-header 中的选择器值: "${currentValue}"`, 'success');
                } else {
                    output += log('❌ dimension-header 中未找到选择器', 'error');
                }
            } else {
                output += log('❌ 未找到 .dimension-header', 'error');
            }
            
            result.textContent = output;
        }

        function findCards() {
            const result = document.getElementById('selectors-result');
            let output = '';
            
            output += log('查找所有卡片...', 'info');
            
            const cards = document.querySelectorAll('.ant-card');
            output += log(`找到 ${cards.length} 个卡片`, 'info');
            
            cards.forEach((card, index) => {
                const title = card.querySelector('.section-title, .ant-card-head-title');
                const titleText = title?.textContent?.trim() || '无标题';
                
                output += log(`卡片 ${index + 1}: "${titleText}"`, 'info');
                
                if (titleText.includes('多维') || titleText.includes('维度')) {
                    output += log(`  ⭐ 这是维度相关卡片!`, 'success');
                    
                    const selector = card.querySelector('.ant-select');
                    if (selector) {
                        const selectionItem = selector.querySelector('.ant-select-selection-item');
                        const currentValue = selectionItem?.textContent?.trim() || '无值';
                        output += log(`  选择器值: "${currentValue}"`, 'success');
                    } else {
                        output += log(`  ❌ 卡片中未找到选择器`, 'error');
                    }
                }
            });
            
            result.textContent = output;
        }

        function checkDimensionSelector() {
            const result = document.getElementById('dimension-result');
            let output = '';
            
            output += log('检查维度选择器...', 'info');
            
            // 使用新的选择器逻辑
            let dimensionSelector = document.querySelector('.dimension-section .dimension-header .ant-select-selector');
            
            if (dimensionSelector) {
                output += log('✅ 使用主要方法找到维度选择器', 'success');
            } else {
                output += log('⚠️ 主要方法未找到，尝试备选方案...', 'warning');
                
                const multiDimensionCard = Array.from(document.querySelectorAll('.ant-card')).find(card => {
                    const title = card.querySelector('.section-title, .ant-card-head-title');
                    return title && (title.textContent.includes('多维表现') || title.textContent.includes('维度'));
                });
                
                if (multiDimensionCard) {
                    dimensionSelector = multiDimensionCard.querySelector('.ant-select-selector');
                    if (dimensionSelector) {
                        output += log('✅ 使用备选方案找到维度选择器', 'success');
                    } else {
                        output += log('❌ 备选方案也未找到选择器', 'error');
                    }
                } else {
                    output += log('❌ 未找到多维表现卡片', 'error');
                }
            }
            
            if (dimensionSelector) {
                const rect = dimensionSelector.getBoundingClientRect();
                output += log(`选择器位置: x=${rect.x}, y=${rect.y}, width=${rect.width}, height=${rect.height}`, 'info');
                output += log(`选择器可见: ${rect.width > 0 && rect.height > 0}`, 'info');
            }
            
            result.textContent = output;
        }

        function getCurrentDimension() {
            const result = document.getElementById('dimension-result');
            let output = '';
            
            output += log('获取当前维度...', 'info');
            
            // 使用新的选择器逻辑
            let currentDimensionElement = document.querySelector('.dimension-section .dimension-header .ant-select-selection-item');
            
            if (currentDimensionElement) {
                const currentDimension = currentDimensionElement.textContent.trim();
                output += log(`✅ 当前维度: "${currentDimension}"`, 'success');
            } else {
                output += log('⚠️ 主要方法未找到，尝试备选方案...', 'warning');
                
                const multiDimensionCard = Array.from(document.querySelectorAll('.ant-card')).find(card => {
                    const title = card.querySelector('.section-title, .ant-card-head-title');
                    return title && (title.textContent.includes('多维表现') || title.textContent.includes('维度'));
                });
                
                if (multiDimensionCard) {
                    currentDimensionElement = multiDimensionCard.querySelector('.ant-select-selection-item');
                    if (currentDimensionElement) {
                        const currentDimension = currentDimensionElement.textContent.trim();
                        output += log(`✅ 使用备选方案获取当前维度: "${currentDimension}"`, 'success');
                    } else {
                        output += log('❌ 备选方案也未找到当前维度', 'error');
                    }
                } else {
                    output += log('❌ 未找到多维表现卡片', 'error');
                }
            }
            
            result.textContent = output;
        }

        function testDimensionClick() {
            const result = document.getElementById('dimension-result');
            let output = '';
            
            output += log('测试点击维度选择器...', 'info');
            
            // 使用新的选择器逻辑
            let dimensionSelect = document.querySelector('.dimension-section .dimension-header .ant-select-selector');
            
            if (!dimensionSelect) {
                const multiDimensionCard = Array.from(document.querySelectorAll('.ant-card')).find(card => {
                    const title = card.querySelector('.section-title, .ant-card-head-title');
                    return title && (title.textContent.includes('多维表现') || title.textContent.includes('维度'));
                });
                
                if (multiDimensionCard) {
                    dimensionSelect = multiDimensionCard.querySelector('.ant-select-selector');
                    output += log('使用备选方案找到选择器', 'info');
                }
            }
            
            if (dimensionSelect) {
                output += log('点击维度选择器...', 'info');
                dimensionSelect.click();
                
                setTimeout(() => {
                    const dropdowns = document.querySelectorAll('.ant-select-dropdown:not(.ant-select-dropdown-hidden)');
                    output += log(`找到 ${dropdowns.length} 个活动下拉菜单`, 'info');
                    
                    if (dropdowns.length > 0) {
                        const latestDropdown = dropdowns[dropdowns.length - 1];
                        const options = latestDropdown.querySelectorAll('.ant-select-item');
                        const optionTexts = Array.from(options).map(o => o.textContent.trim());
                        output += log(`下拉选项: ${optionTexts.join(', ')}`, 'success');
                        
                        // 关闭下拉菜单
                        document.body.click();
                    } else {
                        output += log('❌ 未找到下拉菜单', 'error');
                    }
                    
                    result.textContent = output;
                }, 500);
            } else {
                output += log('❌ 未找到维度选择器', 'error');
                result.textContent = output;
            }
        }

        function testNewSelectorLogic() {
            const result = document.getElementById('new-logic-result');
            let output = '';
            
            output += log('测试新的选择器逻辑...', 'info');
            
            // 完整的新逻辑测试
            const testSelectors = [
                '.dimension-section .dimension-header .ant-select-selector',
                '.dimension-section .ant-select-selector',
                '.ant-card .ant-select-selector'
            ];
            
            testSelectors.forEach((selector, index) => {
                const elements = document.querySelectorAll(selector);
                output += log(`选择器 ${index + 1} "${selector}": 找到 ${elements.length} 个元素`, 'info');
                
                elements.forEach((el, elIndex) => {
                    const selectionItem = el.parentElement.querySelector('.ant-select-selection-item');
                    const value = selectionItem?.textContent?.trim() || '无值';
                    output += log(`  元素 ${elIndex + 1}: 值="${value}"`, 'info');
                });
            });
            
            result.textContent = output;
        }

        function testBackupSelector() {
            const result = document.getElementById('new-logic-result');
            let output = '';
            
            output += log('测试备选选择器逻辑...', 'info');
            
            const multiDimensionCard = Array.from(document.querySelectorAll('.ant-card')).find(card => {
                const title = card.querySelector('.section-title, .ant-card-head-title');
                return title && (title.textContent.includes('多维表现') || title.textContent.includes('维度'));
            });
            
            if (multiDimensionCard) {
                output += log('✅ 找到多维表现卡片', 'success');
                
                const title = multiDimensionCard.querySelector('.section-title, .ant-card-head-title');
                output += log(`卡片标题: "${title?.textContent?.trim()}"`, 'info');
                
                const selector = multiDimensionCard.querySelector('.ant-select-selector');
                if (selector) {
                    output += log('✅ 在卡片中找到选择器', 'success');
                    
                    const selectionItem = multiDimensionCard.querySelector('.ant-select-selection-item');
                    const value = selectionItem?.textContent?.trim() || '无值';
                    output += log(`选择器值: "${value}"`, 'success');
                } else {
                    output += log('❌ 卡片中未找到选择器', 'error');
                }
            } else {
                output += log('❌ 未找到多维表现卡片', 'error');
            }
            
            result.textContent = output;
        }
    </script>
</body>
</html>
