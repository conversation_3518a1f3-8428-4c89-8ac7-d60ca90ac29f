import moment from 'moment';
import 'moment/locale/zh-cn';

// 配置moment.js使用ISO 8601标准（周一为一周开始）
moment.locale('zh-cn');
moment.updateLocale('zh-cn', {
  week: {
    dow: 1, // 周一为一周的开始
    doy: 4  // 1月4日所在的周为第一周
  }
});

// 调试当前的周计算
const debugCurrentWeek = () => {
  const now = moment();
  console.log('=== 当前时间调试 ===');
  console.log('当前时间:', now.format('YYYY-MM-DD dddd'));
  console.log('当前年份:', now.year());
  console.log('当前周数:', now.week());
  console.log('当前周开始:', now.clone().startOf('week').format('YYYY-MM-DD dddd'));
  console.log('当前周结束:', now.clone().endOf('week').format('YYYY-MM-DD dddd'));
  
  const lastWeek = now.clone().subtract(1, 'week').startOf('week');
  console.log('\n=== 上一周调试 ===');
  console.log('上一周时间:', lastWeek.format('YYYY-MM-DD dddd'));
  console.log('上一周年份:', lastWeek.year());
  console.log('上一周周数:', lastWeek.week());
  console.log('上一周开始:', lastWeek.clone().startOf('week').format('YYYY-MM-DD dddd'));
  console.log('上一周结束:', lastWeek.clone().endOf('week').format('YYYY-MM-DD dddd'));
  
  // 检查moment对象是否有效
  console.log('\n=== moment对象有效性检查 ===');
  console.log('lastWeek.isValid():', lastWeek.isValid());
  console.log('lastWeek.valueOf():', lastWeek.valueOf());
  console.log('lastWeek.toISOString():', lastWeek.toISOString());
  
  return lastWeek;
};

// 在浏览器控制台中运行
if (typeof window !== 'undefined') {
  window.debugCurrentWeek = debugCurrentWeek;
}

export { debugCurrentWeek };
