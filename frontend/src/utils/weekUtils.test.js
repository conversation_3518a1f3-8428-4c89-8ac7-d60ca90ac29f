import moment from 'moment';
import 'moment/locale/zh-cn';

// 配置moment.js使用ISO 8601标准（周一为一周开始）
moment.locale('zh-cn');
moment.updateLocale('zh-cn', {
  week: {
    dow: 1, // 周一为一周的开始
    doy: 4  // 1月4日所在的周为第一周
  }
});

// 测试周计算功能
describe('Week Picker Utils', () => {
  test('应该正确计算ISO 8601周数', () => {
    // 测试2024年第1周（包含1月4日的那一周）
    const jan4_2024 = moment('2024-01-04');
    expect(jan4_2024.week()).toBe(1);
    
    // 测试2024年第1周的开始日期应该是周一
    const week1Start = moment().year(2024).week(1).startOf('week');
    expect(week1Start.day()).toBe(1); // 周一
    expect(week1Start.format('YYYY-MM-DD')).toBe('2024-01-01');
  });

  test('应该正确格式化周显示文本', () => {
    const formatWeekDisplay = (momentObj) => {
      const startOfWeek = momentObj.clone().startOf('week');
      const endOfWeek = momentObj.clone().endOf('week');
      const weekNumber = momentObj.week();
      const year = momentObj.year();
      
      return `${year}年第${weekNumber}周 (${startOfWeek.format('MM/DD')} - ${endOfWeek.format('MM/DD')})`;
    };

    const testDate = moment('2024-11-04'); // 2024年第45周
    const formatted = formatWeekDisplay(testDate);
    
    expect(formatted).toMatch(/2024年第\d+周 \(\d{2}\/\d{2} - \d{2}\/\d{2}\)/);
    expect(formatted).toContain('2024年');
  });

  test('应该正确处理跨年的周', () => {
    // 测试2023年最后一周和2024年第一周
    const dec31_2023 = moment('2023-12-31');
    const jan1_2024 = moment('2024-01-01');
    
    // 2023年12月31日应该属于2024年第1周（因为包含1月4日）
    expect(jan1_2024.week()).toBe(1);
    expect(jan1_2024.year()).toBe(2024);
  });

  test('周一应该是一周的开始', () => {
    const anyDate = moment('2024-11-07'); // 2024年11月7日（周四）
    const startOfWeek = anyDate.clone().startOf('week');
    
    expect(startOfWeek.day()).toBe(1); // 周一
    expect(startOfWeek.format('dddd')).toBe('星期一');
  });

  test('周日应该是一周的结束', () => {
    const anyDate = moment('2024-11-07'); // 2024年11月7日（周四）
    const endOfWeek = anyDate.clone().endOf('week');

    expect(endOfWeek.day()).toBe(0); // 周日
    expect(endOfWeek.format('dddd')).toBe('星期日');
  });

  test('应该正确计算默认选中周（上一周）', () => {
    const getDefaultWeek = () => {
      const currentWeek = moment().week();
      const previousWeek = currentWeek - 1;

      // 如果上一周是0，说明跨年了，需要获取上一年的最后一周
      if (previousWeek <= 0) {
        const lastYearLastWeek = moment().subtract(1, 'year').weeksInYear();
        return lastYearLastWeek;
      }

      return previousWeek;
    };

    const defaultWeek = getDefaultWeek();
    const currentWeek = moment().week();

    // 默认周应该是当前周的上一周
    if (currentWeek > 1) {
      expect(defaultWeek).toBe(currentWeek - 1);
    } else {
      // 如果当前是第1周，默认周应该是上一年的最后一周
      const lastYearLastWeek = moment().subtract(1, 'year').weeksInYear();
      expect(defaultWeek).toBe(lastYearLastWeek);
    }
  });

  test('应该正确处理年初第1周的情况', () => {
    // 模拟当前是2024年第1周的情况
    const jan1_2024 = moment('2024-01-01');
    const currentWeek = jan1_2024.week();
    const previousWeek = currentWeek - 1;

    if (previousWeek <= 0) {
      const lastYearLastWeek = moment('2023-12-31').weeksInYear();
      expect(lastYearLastWeek).toBeGreaterThan(50); // 上一年应该有50+周
    }
  });

  test('新的moment对象方法应该正确工作', () => {
    // 测试新的基于moment对象的方法
    const getDefaultWeekMoment = () => {
      const now = moment();
      return now.clone().subtract(1, 'week').startOf('week');
    };

    const defaultWeekMoment = getDefaultWeekMoment();
    const currentMoment = moment();

    // 默认周应该是上一周
    expect(defaultWeekMoment.week()).toBe(currentMoment.week() - 1 > 0 ? currentMoment.week() - 1 : moment().subtract(1, 'year').weeksInYear());

    // 默认周应该是周一开始
    expect(defaultWeekMoment.day()).toBe(1); // 周一
  });

  test('格式化显示应该包含正确的年份', () => {
    const formatWeekDisplay = (momentObj) => {
      const startOfWeek = momentObj.clone().startOf('week');
      const endOfWeek = momentObj.clone().endOf('week');
      const weekNumber = momentObj.week();
      const year = momentObj.year();

      return `${year}年第${weekNumber}周 (${startOfWeek.format('MM/DD')} - ${endOfWeek.format('MM/DD')})`;
    };

    // 测试当前年份
    const currentWeek = moment();
    const formatted = formatWeekDisplay(currentWeek);
    expect(formatted).toContain(currentWeek.year().toString());

    // 测试上一年
    const lastYearWeek = moment().subtract(1, 'year').week(50);
    const lastYearFormatted = formatWeekDisplay(lastYearWeek);
    expect(lastYearFormatted).toContain((moment().year() - 1).toString());
  });
});
