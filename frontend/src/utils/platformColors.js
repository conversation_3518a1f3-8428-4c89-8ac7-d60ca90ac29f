// 平台显示顺序配置
export const PLATFORM_ORDER = ['美团', '饿了么', '京东到家', '多点', '淘鲜达'];

// 平台颜色配置
export const PLATFORM_COLORS = {
  '多点': '#FF6F00',
  '美团': '#FFD161',
  '饿了么': '#0086FF',
  '京东到家': '#01af00',
  '淘鲜达': '#FF5500'
};

// 获取平台颜色的函数
export const getPlatformColor = (platformName) => {
  return PLATFORM_COLORS[platformName] || '#1890ff'; // 默认颜色
};

// 获取所有平台颜色数组
export const getPlatformColors = (platforms) => {
  return platforms.map(platform => getPlatformColor(platform));
};

// 为图表生成颜色映射
export const generateColorMapping = (platformData) => {
  const colorMapping = {};
  Object.keys(platformData).forEach((platform, index) => {
    colorMapping[platform] = getPlatformColor(platform);
  });
  return colorMapping;
};

// 按照指定顺序排序平台数据
export const sortPlatformData = (platformData) => {
  if (!platformData || typeof platformData !== 'object') {
    return platformData;
  }

  const sortedData = {};

  // 先按照预定义顺序添加存在的平台
  PLATFORM_ORDER.forEach(platform => {
    if (platformData.hasOwnProperty(platform)) {
      sortedData[platform] = platformData[platform];
    }
  });

  // 再添加不在预定义顺序中的平台
  Object.keys(platformData).forEach(platform => {
    if (!PLATFORM_ORDER.includes(platform)) {
      sortedData[platform] = platformData[platform];
    }
  });

  return sortedData;
};

// 按照指定顺序排序平台数组
export const sortPlatformArray = (platforms) => {
  if (!Array.isArray(platforms)) {
    return platforms;
  }

  const sortedPlatforms = [];

  // 先按照预定义顺序添加存在的平台
  PLATFORM_ORDER.forEach(platform => {
    if (platforms.includes(platform)) {
      sortedPlatforms.push(platform);
    }
  });

  // 再添加不在预定义顺序中的平台
  platforms.forEach(platform => {
    if (!PLATFORM_ORDER.includes(platform)) {
      sortedPlatforms.push(platform);
    }
  });

  return sortedPlatforms;
};
