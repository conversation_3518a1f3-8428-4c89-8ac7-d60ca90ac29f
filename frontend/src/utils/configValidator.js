// 配置验证工具
import { getApiBaseUrl } from '../config/api';

/**
 * 验证API配置是否正确
 */
export const validateApiConfig = async () => {
  const baseUrl = getApiBaseUrl();
  console.log('🔍 当前API配置验证:');
  console.log('API Base URL:', baseUrl);
  console.log('环境变量 REACT_APP_API_BASE_URL:', process.env.REACT_APP_API_BASE_URL);
  
  try {
    // 测试健康检查端点
    const healthUrl = `${baseUrl}/health`;
    console.log('测试健康检查端点:', healthUrl);
    
    const response = await fetch(healthUrl);
    if (response.ok) {
      const data = await response.json();
      console.log('✅ API连接正常:', data);
      return { success: true, data };
    } else {
      console.error('❌ API连接失败:', response.status, response.statusText);
      return { success: false, error: `HTTP ${response.status}: ${response.statusText}` };
    }
  } catch (error) {
    console.error('❌ API连接异常:', error.message);
    return { success: false, error: error.message };
  }
};

/**
 * 在开发环境中自动验证配置
 */
export const autoValidateInDev = () => {
  if (process.env.NODE_ENV === 'development') {
    console.log('🚀 开发环境 - 自动验证API配置');
    setTimeout(() => {
      validateApiConfig();
    }, 1000);
  }
};

/**
 * 获取当前环境信息
 */
export const getEnvironmentInfo = () => {
  return {
    nodeEnv: process.env.NODE_ENV,
    apiBaseUrl: getApiBaseUrl(),
    envApiUrl: process.env.REACT_APP_API_BASE_URL,
    publicUrl: process.env.PUBLIC_URL,
    buildTime: new Date().toISOString()
  };
};

/**
 * 在浏览器控制台中显示配置信息
 */
export const logConfigInfo = () => {
  const info = getEnvironmentInfo();
  console.group('📋 应用配置信息');
  console.table(info);
  console.groupEnd();
};

// 在开发环境中自动运行验证
if (typeof window !== 'undefined') {
  // 将验证函数暴露到全局，方便调试
  window.validateApiConfig = validateApiConfig;
  window.logConfigInfo = logConfigInfo;
  
  // 自动验证
  autoValidateInDev();
}
