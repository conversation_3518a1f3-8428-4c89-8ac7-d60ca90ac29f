import moment from 'moment';
import 'moment/locale/zh-cn';

// 配置moment.js使用ISO 8601标准（周一为一周开始）
moment.locale('zh-cn');
moment.updateLocale('zh-cn', {
  week: {
    dow: 1, // 周一为一周的开始
    doy: 4  // 1月4日所在的周为第一周
  }
});

// 计算默认选中的周（当前时间所在周的上一周）
const getDefaultWeek = () => {
  const currentWeek = moment().week();
  const previousWeek = currentWeek - 1;
  
  // 如果上一周是0，说明跨年了，需要获取上一年的最后一周
  if (previousWeek <= 0) {
    const lastYearLastWeek = moment().subtract(1, 'year').weeksInYear();
    return lastYearLastWeek;
  }
  
  return previousWeek;
};

// 格式化周显示文本
const formatWeekDisplay = (momentObj) => {
  const startOfWeek = momentObj.clone().startOf('week');
  const endOfWeek = momentObj.clone().endOf('week');
  const weekNumber = momentObj.week();
  const year = momentObj.year();
  
  return `${year}年第${weekNumber}周 (${startOfWeek.format('MM/DD')} - ${endOfWeek.format('MM/DD')})`;
};

// 验证当前设置
const currentMoment = moment();
const currentWeek = currentMoment.week();
const defaultWeek = getDefaultWeek();
const defaultMoment = moment().week(defaultWeek);

console.log('=== 周选择器默认设置验证 ===');
console.log(`当前时间: ${currentMoment.format('YYYY-MM-DD dddd')}`);
console.log(`当前周: ${formatWeekDisplay(currentMoment)}`);
console.log(`默认选中周: ${formatWeekDisplay(defaultMoment)}`);
console.log(`默认周数: ${defaultWeek}`);
console.log(`当前周数: ${currentWeek}`);
console.log(`差值: ${currentWeek - defaultWeek} 周`);

export { getDefaultWeek, formatWeekDisplay };
