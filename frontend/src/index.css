body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.app-container {
  height: 100vh;
  overflow: hidden;
}

.header-toolbar {
  background: #fff;
  padding: 12px 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-bottom: 1px solid #f0f0f0;
}

.main-layout {
  height: calc(100vh - 64px);
}

.sidebar {
  background: #fff;
  border-right: 1px solid #f0f0f0;
}

.content-area {
  background: #f5f5f5;
  padding: 16px;
  overflow-y: auto;
}

.trading-section {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 16px;
}

.platform-filters {
  margin-bottom: 24px;
}

.summary-cards {
  margin-bottom: 24px;
}

.charts-section {
  margin-bottom: 24px;
}

.dimension-section {
  margin-bottom: 24px;
}

.top10-section {
  margin-top: 24px;
}

.card-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-header {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 16px;
  color: #262626;
}

.metric-value {
  font-size: 28px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 8px;
}

.metric-label {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 12px;
}

.change-indicators {
  display: flex;
  gap: 16px;
}

.change-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
}

.change-positive {
  color: #52c41a;
}

.change-negative {
  color: #ff4d4f;
}

.progress-container {
  margin-top: 16px;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 13px;
}

.dimension-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

.table-container {
  margin-top: 16px;
}

@media (max-width: 768px) {
  .content-area {
    padding: 8px;
  }
  
  .trading-section {
    padding: 16px;
  }
  
  .metric-value {
    font-size: 24px;
  }
} 