import axios from 'axios';
import { getApiBaseUrl, DEFAULT_REQUEST_CONFIG } from '../config/api';

// 创建axios实例
const api = axios.create({
  baseURL: getApiBaseUrl(),
  timeout: DEFAULT_REQUEST_CONFIG.timeout,
  headers: DEFAULT_REQUEST_CONFIG.headers
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

// 交易数据相关API
export const fetchTradingSummary = async (week, platform, brand = '') => {
  const params = { week, platform };
  if (brand) params.brand = brand;
  return await api.get('/trading/summary', { params });
};

export const fetchTradingTrends = async (platform, brand = '') => {
  const params = { platform };
  if (brand) params.brand = brand;
  return await api.get('/trading/trends', { params });
};

export const fetchDimensionAnalysis = async (dimensionType, brand = '', platform = '全平台') => {
  const params = { type: dimensionType };
  if (brand) params.brand = brand;
  if (platform !== '全平台') params.platform = platform;
  return await api.get('/trading/dimensions', { params });
};

export const fetchDimensionTrends = async (dimensionType, brand = '', selectedWeek = null, platform = '全平台') => {
  const params = { type: dimensionType };
  if (brand) params.brand = brand;
  if (selectedWeek) params.week = selectedWeek;
  if (platform !== '全平台') params.platform = platform;
  return await api.get('/trading/dimension-trends', { params });
};

export const fetchTop10Data = async (category = '商品', brand = '', platform = '全平台') => {
  const params = { category };
  if (brand) params.brand = brand;
  if (platform !== '全平台') params.platform = platform;
  return await api.get('/trading/top10', { params });
};

// 活动数据相关API
export const fetchActivitySummary = async (week, brand = '', platform = '全平台') => {
  const params = { week, brand };
  if (platform !== '全平台') {
    params.platform = platform;
  }
  return await api.get('/activity/summary', {
    params
  });
};

export const fetchActivityTrends = async (brand = '', endWeek = null, platform = '全平台') => {
  const params = { brand };
  if (endWeek !== null) {
    params.end_week = endWeek;
  }
  if (platform !== '全平台') {
    params.platform = platform;
  }
  return await api.get('/activity/trends', {
    params
  });
};

export const fetchActivityCharts = async (brand = '', platform = '全平台') => {
  const params = { brand };
  if (platform !== '全平台') {
    params.platform = platform;
  }
  return await api.get('/activity/charts', {
    params
  });
};

export const fetchActivityPlatformCharts = async (brand = '', platform = '全平台') => {
  const params = { brand };
  if (platform !== '全平台') {
    params.platform = platform;
  }
  return await api.get('/activity/platform-charts', {
    params
  });
};

export const fetchActivityDetail = async (dimensionType, selectedWeek, brand, page = 1, pageSize = 20, platform = '全平台') => {
  const params = {
    type: dimensionType,
    selected_week: selectedWeek,
    brand,
    page,
    page_size: pageSize
  };
  if (platform !== '全平台') {
    params.platform = platform;
  }
  return await api.get('/activity/detail', {
    params
  });
};

// RTB投放相关API
export const fetchRTBSummary = async (week, brand = '') => {
  return await api.get('/rtb/summary', {
    params: { week, brand }
  });
};

export const fetchRTBTrends = async (brand = '') => {
  return await api.get('/rtb/trends', {
    params: { brand }
  });
};

export const fetchRTBDetail = async (dimensionType, selectedWeek, brand = '') => {
  return await api.get('/rtb/detail', {
    params: {
      type: dimensionType,
      selected_week: selectedWeek,
      brand
    }
  });
};

// 供给表现相关API
export const fetchSupplySummary = async (week, brand = '') => {
  const params = { week };
  if (brand) params.brand = brand;
  return await api.get('/supply/summary', { params });
};

export const fetchSupplyTrends = async (brand = '') => {
  const params = {};
  if (brand) params.brand = brand;
  return await api.get('/supply/trends', { params });
};

export const fetchSupplyDetail = async (dimensionType, brand = '', selectedWeek = null) => {
  const params = { type: encodeURIComponent(dimensionType) };
  if (brand) params.brand = brand;
  if (selectedWeek) params.selected_week = selectedWeek;
  return await api.get('/supply/detail', { params });
};

// 健康检查
export const healthCheck = async () => {
  return await api.get('/health');
};

// AI洞察相关API
export const generateAIInsights = async (selectedWeek, brand = '', selectedPlatform = '全平台') => {
  return await api.post('/ai-insights', {
    selectedWeek,
    brand,
    selectedPlatform
  });
};

export const getInsightsProgress = async (taskId) => {
  return await api.get(`/ai-insights/progress/${taskId}`);
};

export const saveAIInsights = async (insights, selectedWeek, brand = '') => {
  return await api.post('/ai-insights/save', {
    insights,
    selectedWeek,
    brand
  });
};

// Excel导出相关API
const API_BASE = 'http://127.0.0.1:5002/api';

export const exportExcel = async (pageName, week, brand = '', aiInsights = '', chartImages = {}) => {
  try {
    const response = await fetch(`${API_BASE}/export/excel`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        page_name: pageName,
        week: week,
        brand: brand,
        ai_insights: aiInsights,
        chart_images: chartImages
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || '导出失败');
    }

    // 获取文件名
    const contentDisposition = response.headers.get('Content-Disposition');
    let filename = `${pageName}第${week}周周报.xlsx`;
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
      if (filenameMatch && filenameMatch[1]) {
        filename = filenameMatch[1].replace(/['"]/g, '');
      }
    }

    // 创建下载链接
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);

    return { success: true };
  } catch (error) {
    console.error('Excel导出失败:', error);
    throw error;
  }
};

export const exportExcelWithProgress = async (pageName, week, brand = '', aiInsights = '', chartImages = {}) => {
  try {
    const response = await fetch(`${API_BASE}/export/excel`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        page_name: pageName,
        week: week,
        brand: brand,
        ai_insights: aiInsights,
        chart_images: chartImages,
        use_progress: true  // 启用进度跟踪
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || '导出失败');
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Excel导出失败:', error);
    throw error;
  }
};

export const exportAllExcel = async (week, brand = '') => {
  try {
    // 启动批量导出任务
    const response = await fetch(`${API_BASE}/export/excel/all`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        week: week,
        brand: brand
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || '启动批量导出失败');
    }

    const data = await response.json();
    if (!data.success) {
      throw new Error(data.error || '启动批量导出失败');
    }

    return { success: true, task_id: data.task_id };
  } catch (error) {
    console.error('启动批量Excel导出失败:', error);
    throw error;
  }
};

export const getExportProgress = async (taskId) => {
  try {
    const response = await fetch(`${API_BASE}/export/excel/progress/${taskId}`);
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('获取导出进度失败:', error);
    throw error;
  }
};

export const downloadExportResult = async (taskId) => {
  try {
    const response = await fetch(`${API_BASE}/export/excel/download/${taskId}`);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || '下载失败');
    }

    // 获取文件名
    const contentDisposition = response.headers.get('Content-Disposition');
    let filename = '周报数据.xlsx'; // 默认为Excel文件
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
      if (filenameMatch && filenameMatch[1]) {
        filename = filenameMatch[1].replace(/['"]/g, '');
      }
    }

    // 创建下载链接
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);

    return { success: true, filename };
  } catch (error) {
    console.error('下载导出结果失败:', error);
    throw error;
  }
};

export default api;