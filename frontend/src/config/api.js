// API配置文件
// 统一管理所有API相关的配置

// 获取API基础URL
export const getApiBaseUrl = () => {
  // 优先使用环境变量，如果没有则使用默认值
  return process.env.REACT_APP_API_BASE_URL || '/api';
};

// API端点配置
export const API_ENDPOINTS = {
  // 健康检查
  HEALTH: '/health',
  
  // 交易数据
  TRADING: {
    SUMMARY: '/trading/summary',
    TRENDS: '/trading/trends',
    DIMENSIONS: '/trading/dimensions',
    TOP10: '/trading/top10'
  },
  
  // 供给数据
  SUPPLY: {
    SUMMARY: '/supply/summary',
    TRENDS: '/supply/trends',
    DETAIL: '/supply/detail'
  },
  
  // 活动数据
  ACTIVITY: {
    SUMMARY: '/activity/summary',
    TRENDS: '/activity/trends',
    PLATFORM_CHARTS: '/activity/platform-charts',
    DETAIL: '/activity/detail'
  },
  
  // 用户数据
  USER: {
    SUMMARY: '/user/summary',
    TRENDS: '/user/trends',
    DETAIL: '/user/detail'
  }
};

// 构建完整的API URL
export const buildApiUrl = (endpoint, params = {}) => {
  const baseUrl = getApiBaseUrl();
  let url = `${baseUrl}${endpoint}`;
  
  // 添加查询参数
  const searchParams = new URLSearchParams();
  Object.keys(params).forEach(key => {
    if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
      searchParams.append(key, params[key]);
    }
  });
  
  const queryString = searchParams.toString();
  if (queryString) {
    url += `?${queryString}`;
  }
  
  return url;
};

// 默认请求配置
export const DEFAULT_REQUEST_CONFIG = {
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
};

const apiConfig = {
  getApiBaseUrl,
  API_ENDPOINTS,
  buildApiUrl,
  DEFAULT_REQUEST_CONFIG
};

export default apiConfig;
