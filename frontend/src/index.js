import React from 'react';
import ReactDOM from 'react-dom/client';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import 'antd/dist/reset.css';
import './index.css';
import App from './App';
import moment from 'moment';
import 'moment/locale/zh-cn';

// 配置moment.js使用ISO 8601标准（周一为一周开始）
moment.locale('zh-cn');
moment.updateLocale('zh-cn', {
  week: {
    dow: 1, // 周一为一周的开始
    doy: 4  // 1月4日所在的周为第一周
  }
});

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <ConfigProvider locale={zhCN}>
      <App />
    </ConfigProvider>
  </React.StrictMode>
);