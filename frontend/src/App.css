.app-container {
  height: 100vh;
  overflow: hidden;
}

.main-layout {
  height: calc(100vh - 64px);
}

.sidebar {
  background: #fff;
  border-right: 1px solid #f0f0f0;
}

.content-area {
  background: #f5f5f5;
  padding: 0;
  overflow-y: auto;
}

/* 活动数据卡片样式 - 确保5个卡片在一行显示 */
.activity-cards-row {
  display: flex !important;
  flex-wrap: nowrap !important;
  gap: 16px;
}

.activity-cards-row .ant-col {
  flex: 1 !important;
  min-width: 0 !important;
  max-width: none !important;
}

/* 在大屏幕上强制5个卡片等宽显示 */
@media (min-width: 1200px) {
  .activity-cards-row .ant-col {
    width: calc(20% - 12.8px) !important;
    flex: 0 0 calc(20% - 12.8px) !important;
  }
}

/* 变化指示器样式 */
.change-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
}

.change-positive {
  color: #52c41a;
  font-weight: 500;
}

.change-negative {
  color: #ff4d4f;
  font-weight: 500;
}

/* 进度条容器样式 */
.progress-container {
  margin-top: 8px;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
  color: #666;
}

/* 交易数据模块样式 */
.trading-section {
  background: white;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
}

.summary-cards {
  padding: 16px;
}

/* 图表样式优化 */
.charts-section .ant-card,
.dimension-section .ant-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.charts-section .ant-card:hover,
.dimension-section .ant-card:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.charts-section .ant-card-body,
.dimension-section .ant-card-body {
  padding: 20px;
}

/* 图表容器圆角 */
.charts-section .echarts-for-react,
.dimension-section .echarts-for-react {
  border-radius: 8px;
  overflow: hidden;
}

/* 维度分析头部样式 */
.dimension-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

/* 合并组件的标签页样式 */
.dimension-section .ant-tabs-tab {
  font-weight: 500;
}

.dimension-section .ant-tabs-content-holder {
  padding-top: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.dimension-header .ant-select {
  border-radius: 8px;
}

.dimension-header .ant-select .ant-select-selector {
  border-radius: 8px;
  border-color: #d9d9d9;
  transition: all 0.3s ease;
}

.dimension-header .ant-select:hover .ant-select-selector {
  border-color: #40a9ff;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
}

/* 平台筛选样式 */
.platform-filters {
  padding: 16px;
}

.platform-radio-group {
  width: 100%;
  display: flex;
  gap: 0;
}

/* 单平台模式样式 */
.platform-radio-group.single-platform {
  width: auto;
  justify-content: flex-start;
}

.platform-radio-group .ant-radio-button-wrapper {
  flex: 1;
  text-align: center;
  border-radius: 0 !important;
  border-left: 1px solid #d9d9d9;
  margin: 0;
}

/* 单平台模式下的按钮样式 */
.platform-radio-group.single-platform .ant-radio-button-wrapper {
  flex: none;
  width: calc(100% / 6);
  min-width: calc(100% / 6);
}

.platform-radio-group .ant-radio-button-wrapper:first-child {
  border-radius: 6px 0 0 6px !important;
  border-left: 1px solid #d9d9d9;
}

.platform-radio-group .ant-radio-button-wrapper:last-child {
  border-radius: 0 6px 6px 0 !important;
}

.platform-radio-group .ant-radio-button-wrapper-checked {
  border-color: #1890ff;
  z-index: 1;
}

.platform-radio-button {
  flex: 1;
  text-align: center;
  border-radius: 0 !important;
}

.platform-radio-button:first-child {
  border-radius: 6px 0 0 6px !important;
}

.platform-radio-button:last-child {
  border-radius: 0 6px 6px 0 !important;
}

/* 确保卡片高度一致 */
.summary-cards .ant-card {
  height: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.summary-cards .ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.summary-cards .ant-card-body {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.card-container {
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 220px;
}

.card-header {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
  font-weight: 500;
}

.metric-value {
  font-size: 28px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 6px;
  line-height: 1.2;
}

.metric-label {
  font-size: 13px;
  color: #8c8c8c;
  margin-bottom: 16px;
}

.change-indicators {
  font-size: 12px;
  margin: 16px 0;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 4px;
}

.progress-container {
  margin-top: auto;
  padding-top: 8px;
}