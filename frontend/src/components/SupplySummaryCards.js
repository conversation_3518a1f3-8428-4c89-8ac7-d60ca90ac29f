import React from 'react';
import { Card, Row, Col, <PERSON>ati<PERSON>, Spin } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';

const SupplySummaryCards = ({ data, loading, onCardClick, selectedCard }) => {
  if (loading) {
    return (
      <div>
        <h3 style={{
          marginBottom: '16px',
          fontSize: '16px',
          fontWeight: 'bold'
        }}>
          供给表现概览
        </h3>
        <div style={{
          display: 'flex',
          gap: '16px',
          width: '100%',
          flexWrap: 'nowrap'
        }}>
          {[1, 2, 3, 4, 5].map(i => (
            <div
              key={i}
              style={{
                flex: '1',
                minWidth: '0'
              }}
            >
              <Card style={{ height: '120px' }}>
                <Spin size="small" />
              </Card>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!data) return null;

  const formatValue = (value, type) => {
    if (type === 'percentage') {
      return `${(value * 100).toFixed(2)}%`;
    } else if (type === 'pp') {
      return `${(value * 100).toFixed(2)}pp`;
    } else if (type === 'currency') {
      return `¥${Math.round(value).toLocaleString()}`;
    } else {
      return Math.round(value).toLocaleString();
    }
  };

  const getWowIcon = (wow) => {
    if (wow > 0) {
      return <ArrowUpOutlined style={{ color: '#52c41a' }} />;
    } else if (wow < 0) {
      return <ArrowDownOutlined style={{ color: '#ff4d4f' }} />;
    }
    return null;
  };

  const getWowColor = (wow) => {
    if (wow > 0) return '#52c41a';
    if (wow < 0) return '#ff4d4f';
    return '#666';
  };

  const cards = [
    {
      key: 'store_count',
      title: '铺货店铺数',
      value: data.store_count?.value || 0,
      wow: data.store_count?.wow || 0,
      type: 'number',
      suffix: '家',
      wowType: 'percentage'
    },
    {
      key: 'store_penetration',
      title: '店铺渗透率',
      value: data.store_penetration?.value || 0,
      wow: data.store_penetration?.wow || 0,
      type: 'percentage',
      suffix: '',
      wowType: 'pp'
    },
    {
      key: 'store_activity_rate',
      title: '店铺动销率',
      value: data.store_activity_rate?.value || 0,
      wow: data.store_activity_rate?.wow || 0,
      type: 'percentage',
      suffix: '',
      wowType: 'pp'
    },
    {
      key: 'avg_sku_per_store',
      title: '店均在售SKU数',
      value: data.avg_sku_per_store?.value || 0,
      wow: data.avg_sku_per_store?.wow || 0,
      type: 'number',
      suffix: '个',
      wowType: 'percentage'
    },
    {
      key: 'sku_sold_out_rate',
      title: 'SKU售罄率',
      value: data.sku_sold_out_rate?.value || 0,
      wow: data.sku_sold_out_rate?.wow || 0,
      type: 'percentage',
      suffix: '',
      wowType: 'pp'
    }
  ];

  return (
    <div>
      <h3 style={{
        marginBottom: '16px',
        fontSize: '16px',
        fontWeight: 'bold'
      }}>
        供给表现概览
      </h3>
      <div style={{
        display: 'flex',
        gap: '16px',
        width: '100%',
        flexWrap: 'nowrap'
      }}>
        {cards.map((card, index) => (
          <div
            key={card.key}
            style={{
              flex: '1',
              minWidth: '0'
            }}
          >
            <Card
              hoverable
              onClick={() => onCardClick && onCardClick(card.key)}
              style={{
                height: '120px',
                borderRadius: '8px',
                cursor: onCardClick ? 'pointer' : 'default',
                border: selectedCard === card.key ? '2px solid #1890ff' : '1px solid #d9d9d9',
                boxShadow: selectedCard === card.key ? '0 4px 12px rgba(24, 144, 255, 0.3)' : '0 2px 8px rgba(0,0,0,0.1)'
              }}
            >
              <Statistic
                title={
                  <span style={{ fontSize: '14px', color: '#666' }}>
                    {card.title}
                  </span>
                }
                value={formatValue(card.value, card.type)}
                suffix={card.suffix}
                valueStyle={{
                  fontSize: '20px',
                  fontWeight: 'bold',
                  color: '#1890ff'
                }}
              />
              <div style={{ marginTop: '8px' }}>
                <span style={{ fontSize: '12px', color: '#999' }}>
                  周环比
                </span>
                <span style={{
                  marginLeft: '4px',
                  color: getWowColor(card.wow),
                  fontWeight: 'bold'
                }}>
                  {getWowIcon(card.wow)}
                  {card.wowType === 'pp' ?
                    `${(card.wow * 100).toFixed(2)}pp` :
                    `${(card.wow * 100).toFixed(2)}%`
                  }
                </span>
              </div>
            </Card>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SupplySummaryCards; 