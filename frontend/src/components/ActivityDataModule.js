import React, { useState, useEffect } from 'react';
import { Space } from 'antd';
import PlatformFilter from './PlatformFilter';
import ActivitySummaryCards from './ActivitySummaryCards';
import ActivityInteractiveTrendChart from './ActivityInteractiveTrendChart';
import ActivityCharts from './ActivityCharts';

import ActivityDetailTable from './ActivityDetailTable';
import { fetchActivitySummary, fetchActivityTrends, fetchActivityDetail, fetchActivityPlatformCharts } from '../services/api';

const ActivityDataModule = ({ selectedWeek, brand }) => {
  const [selectedPlatform, setSelectedPlatform] = useState('全平台');
  const [selectedDimension, setSelectedDimension] = useState('整体');
  const [summaryData, setSummaryData] = useState(null);
  const [trendsData, setTrendsData] = useState(null);
  const [platformChartsData, setPlatformChartsData] = useState(null);
  const [detailData, setDetailData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [selectedCard, setSelectedCard] = useState('activity_gmv'); // 默认选中第1个卡片

  // 加载基础数据（概览、趋势、图表）
  useEffect(() => {
    const loadBaseData = async () => {
      setLoading(true);
      try {
        // 从selectedWeek中提取周数，如果没有则使用当前周
        let weekNumber = null;
        if (selectedWeek && selectedWeek.includes('-W')) {
          weekNumber = parseInt(selectedWeek.split('-W')[1]);
        } else if (selectedWeek) {
          weekNumber = parseInt(selectedWeek);
        }
        // 如果仍然没有有效的周数，使用默认值
        if (!weekNumber || isNaN(weekNumber)) {
          weekNumber = 26; // 默认使用第26周
        }

        // 根据平台选择决定是否加载图表数据
        const loadPromises = [
          fetchActivitySummary(selectedWeek, brand, selectedPlatform),
          fetchActivityTrends(brand, weekNumber, selectedPlatform)
        ];

        // 只有选择全平台时才加载平台图表数据
        if (selectedPlatform === '全平台') {
          loadPromises.push(fetchActivityPlatformCharts(brand, selectedPlatform));
        }

        const results = await Promise.all(loadPromises);
        const [summary, trends, platformCharts] = [
          results[0],
          results[1],
          selectedPlatform === '全平台' ? results[2] : null
        ];

        console.log('ActivityDataModule - 数据加载成功:', {
          selectedWeek,
          weekNumber,
          selectedPlatform,
          brand,
          summary: summary,
          trends: trends,
          platformCharts: platformCharts
        });

        setSummaryData(summary);
        setTrendsData(trends);
        // 只有在全平台模式下才设置图表数据
        if (selectedPlatform === '全平台') {
          setPlatformChartsData(platformCharts);
        } else {
          setPlatformChartsData(null); // 清空图表数据
        }
      } catch (error) {
        console.error('Failed to load activity base data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadBaseData();
  }, [selectedWeek, brand, selectedPlatform]);

  // 单独加载详细数据
  const [detailLoading, setDetailLoading] = useState(false);

  useEffect(() => {
    const loadDetailData = async () => {
      setDetailLoading(true);
      try {
        const detail = await fetchActivityDetail(selectedDimension, selectedWeek, brand, currentPage, pageSize, selectedPlatform);
        setDetailData(detail);
      } catch (error) {
        console.error('Failed to load activity detail data:', error);
      } finally {
        setDetailLoading(false);
      }
    };

    loadDetailData();
  }, [selectedDimension, selectedWeek, brand, currentPage, pageSize, selectedPlatform]);

  const handleDimensionChange = (dimension) => {
    setSelectedDimension(dimension);
    setCurrentPage(1); // 切换维度时重置到第一页
  };

  const handlePageChange = (page, size) => {
    setCurrentPage(page);
    if (size !== pageSize) {
      setPageSize(size);
    }
  };

  // 处理平台筛选变化
  const handlePlatformChange = (platform) => {
    setSelectedPlatform(platform);
  };

  // 处理卡片点击
  const handleCardClick = (cardKey) => {
    setSelectedCard(cardKey);
  };

  return (
    <div style={{ padding: '16px' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 平台筛选器 */}
        <div className="trading-section">
          <PlatformFilter
            selectedPlatform={selectedPlatform}
            onPlatformChange={handlePlatformChange}
          />
        </div>

        {/* 概览卡片 */}
        <div className="trading-section">
          <ActivitySummaryCards
            data={summaryData}
            loading={loading}
            onCardClick={handleCardClick}
            selectedCard={selectedCard}
          />
        </div>

        {/* 活动趋势分析 - 联动图表 */}
        <div className="trading-section">
          <ActivityInteractiveTrendChart
            key={`${selectedPlatform}-${selectedCard}`}
            data={trendsData}
            loading={loading}
            selectedMetric={selectedCard}
          />
        </div>



        {/* 图表区域 - 只有选择全平台时才显示 */}
        {selectedPlatform === '全平台' && (
          <div className="trading-section">
            <ActivityCharts
              data={platformChartsData}
              loading={loading}
            />
          </div>
        )}

        {/* 活动详细数据表格 */}
        <div className="trading-section">
          <ActivityDetailTable
            key={selectedDimension}
            data={detailData}
            selectedDimension={selectedDimension}
            onDimensionChange={handleDimensionChange}
            onPageChange={handlePageChange}
            loading={detailLoading}
          />
        </div>
      </Space>
    </div>
  );
};

export default ActivityDataModule; 