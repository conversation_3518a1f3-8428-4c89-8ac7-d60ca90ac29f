import React, { useRef, useEffect } from 'react';
import { Row, Col, Card, Skeleton } from 'antd';
import * as echarts from 'echarts';

// 平台配色方案
const platformColors = {
  '美团': '#FFD161',
  '饿了么': '#0086FF',
  '京东到家': '#01af00',
  '多点': '#FF6F00',
  '淘鲜达': '#FF5500'
};

// 柱状图卡片组件
const BarChartCard = ({ title, data }) => {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  useEffect(() => {
    if (!chartRef.current || !data || !Array.isArray(data) || data.length === 0) return;

    // 初始化图表
    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
    }

    // 准备数据
    const platforms = data.map(item => item.platform);
    let values = [];
    let isPercentage = false;
    let isDualBar = false;
    let values2 = [];

    // 根据标题确定要显示的数据
    if (title === '活动GMV') {
      values = data.map(item => item.activity_gmv || 0);
    } else if (title === '活动GMV占比') {
      values = data.map(item => item.activity_gmv_ratio || 0);
      isPercentage = true;
    } else if (title === '核销金额') {
      values = data.map(item => item.verification_amount || 0);
    } else if (title === '费比') {
      values = data.map(item => item.activity_cost_ratio || 0);
      values2 = data.map(item => item.total_cost_ratio || 0);
      isPercentage = true;
      isDualBar = true;
    }

    const colors = data.map(item => platformColors[item.platform] || '#1890ff');

    // 配置选项
    const option = {
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: isDualBar ? '20%' : '15%',
        containLabel: true
      },
      legend: isDualBar ? {
        data: ['活动费比', '全量费比'],
        top: '5%',
        textStyle: {
          fontSize: 11,
          color: '#666'
        }
      } : undefined,
      xAxis: {
        type: 'category',
        data: platforms,
        axisLabel: {
          fontSize: 11,
          color: '#666',
          interval: 0,
          rotate: platforms.some(p => p.length > 3) ? 45 : 0
        },
        axisLine: {
          lineStyle: {
            color: '#e8e8e8'
          }
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          fontSize: 11,
          color: '#666',
          formatter: function(value) {
            if (isPercentage) {
              return `${(value * 100).toFixed(0)}%`;
            } else if (value >= 100000000) {
              return `${Math.round(value / 100000000)}亿`;
            } else if (value >= 10000) {
              return `${Math.round(value / 10000)}万`;
            }
            return value.toLocaleString();
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0'
          }
        }
      },
      series: isDualBar ? [
        {
          name: '活动费比',
          type: 'bar',
          data: values.map((value, index) => ({
            value: value,
            itemStyle: {
              color: colors[index],
              borderRadius: [4, 4, 0, 0]
            }
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.2)'
            }
          },
          barWidth: '35%',
          label: {
            show: true,
            position: 'top',
            fontSize: 9,
            color: '#333',
            formatter: function(params) {
              return `${(params.value * 100).toFixed(2)}%`;
            }
          }
        },
        {
          name: '全量费比',
          type: 'bar',
          data: values2.map((value, index) => ({
            value: value,
            itemStyle: {
              color: colors[index],
              opacity: 0.6,
              borderRadius: [4, 4, 0, 0]
            }
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.2)'
            }
          },
          barWidth: '35%',
          label: {
            show: true,
            position: 'top',
            fontSize: 9,
            color: '#333',
            formatter: function(params) {
              return `${(params.value * 100).toFixed(2)}%`;
            }
          }
        }
      ] : [{
        type: 'bar',
        data: values.map((value, index) => ({
          value: value,
          itemStyle: {
            color: colors[index],
            borderRadius: [4, 4, 0, 0]
          }
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.2)'
          }
        },
        barWidth: '50%',
        label: {
          show: true,
          position: 'top',
          fontSize: 10,
          color: '#333',
          formatter: function(params) {
            if (isPercentage) {
              return `${(params.value * 100).toFixed(2)}%`;
            }
            // 统一使用千分位符格式化整数金额
            return Math.round(params.value).toLocaleString();
          }
        }
      }],
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          if (isDualBar) {
            let result = `${params[0].name}<br/>`;
            params.forEach(param => {
              const value = `${(param.value * 100).toFixed(2)}%`;
              result += `${param.marker}${param.seriesName}: ${value}<br/>`;
            });
            return result;
          } else {
            const param = params[0];
            let value = param.value;
            if (isPercentage) {
              value = `${(value * 100).toFixed(2)}%`;
            } else if (value >= 100000000) {
              value = `${Math.round(value / 100000000)}亿`;
            } else if (value >= 10000) {
              value = `${Math.round(value / 10000)}万`;
            } else {
              value = value.toLocaleString();
            }
            return `${param.name}: ${value}`;
          }
        }
      }
    };

    chartInstance.current.setOption(option);

    // 窗口大小变化时重新调整图表
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, [data, title]);

  if (!data || !Array.isArray(data) || data.length === 0) {
    return (
      <Card title={title} style={{ height: '300px' }}>
        <div style={{ textAlign: 'center', color: '#999', marginTop: '100px' }}>
          暂无数据
        </div>
      </Card>
    );
  }

  return (
    <Card title={title} style={{ height: '300px' }}>
      <div ref={chartRef} style={{ width: '100%', height: '250px' }} />
    </Card>
  );
};

// 主要的ActivityCharts组件
const ActivityCharts = ({ data, loading }) => {
  // 检查数据是否有效
  const hasValidData = data && data.platform_data && Array.isArray(data.platform_data) && data.platform_data.length > 0;

  if (loading || !hasValidData) {
    return (
      <div>
        <div className="section-title" style={{ marginBottom: 16 }}>
          活动图表分析
        </div>
        <Row gutter={[16, 16]}>
          {[1, 2, 3, 4].map(i => (
            <Col xs={24} sm={12} lg={6} key={i}>
              <Card>
                <Skeleton active paragraph={{ rows: 4 }} />
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    );
  }

  const platformData = data.platform_data;

  return (
    <div>
      <div className="section-title" style={{ marginBottom: 16 }}>
        活动图表分析
      </div>
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={6}>
          <BarChartCard title="活动GMV" data={platformData} />
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <BarChartCard title="活动GMV占比" data={platformData} />
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <BarChartCard title="核销金额" data={platformData} />
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <BarChartCard title="费比" data={platformData} />
        </Col>
      </Row>
    </div>
  );
};

export default ActivityCharts;
