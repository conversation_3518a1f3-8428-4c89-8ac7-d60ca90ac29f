import React, { useState, useEffect } from 'react';
import { Card, Table, Select, Spin } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import { fetchSupplyDetail } from '../services/api';

const { Option } = Select;

const SupplyDetailTable = ({ data, selectedDimension, onDimensionChange, loading, selectedWeek, selectedBrand }) => {
  const [localLoading, setLocalLoading] = useState(false);
  const [localData, setLocalData] = useState(data);

  // 当外部数据变化时更新本地数据
  useEffect(() => {
    setLocalData(data);
  }, [data]);

  // 当外部维度变化时，同步更新数据（但不重新加载整个模块）
  useEffect(() => {
    if (selectedDimension && localData && localData.data) {
      // 如果维度变化了，但数据还是旧的，需要重新加载
      const loadNewDimensionData = async () => {
        setLocalLoading(true);
        try {
          const result = await fetchSupplyDetail(selectedDimension, selectedBrand, selectedWeek);
          setLocalData(result);
        } catch (error) {
          console.error('Failed to load supply detail data:', error);
        } finally {
          setLocalLoading(false);
        }
      };

      // 检查当前数据是否匹配选定的维度
      const isDataMismatch = selectedDimension === '整体'
        ? !localData.data.some(item => item.dimension_value && item.dimension_value.includes('第') && item.dimension_value.includes('周'))
        : localData.data.some(item => item.dimension_value && item.dimension_value.includes('第') && item.dimension_value.includes('周'));

      if (isDataMismatch) {
        loadNewDimensionData();
      }
    }
  }, [selectedDimension, selectedBrand, selectedWeek]);

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!localData || !localData.data) return null;

  const dimensionOptions = [
    { value: '整体', label: '整体' },
    { value: '城市', label: '城市' },
    { value: '重点渠道', label: '重点渠道' },
    { value: '重点商品', label: '重点商品' },
    { value: '子品牌', label: '子品牌' }
  ];

  const formatValue = (value, type) => {
    if (type === 'percentage') {
      return `${(value * 100).toFixed(2)}%`;
    } else if (type === 'currency') {
      return `¥${Math.round(value).toLocaleString()}`;
    } else if (type === 'number') {
      return Math.round(value).toLocaleString();
    }
    return value;
  };

  const getWowIcon = (wow) => {
    if (wow > 0) {
      return <ArrowUpOutlined style={{ color: '#52c41a' }} />;
    } else if (wow < 0) {
      return <ArrowDownOutlined style={{ color: '#ff4d4f' }} />;
    }
    return null;
  };

  const getWowColor = (wow) => {
    if (wow > 0) return '#52c41a';
    if (wow < 0) return '#ff4d4f';
    return '#666';
  };

  // 处理维度变化，实现局部刷新
  const handleDimensionChange = async (newDimension) => {
    if (newDimension === selectedDimension) return;

    setLocalLoading(true);
    try {
      // 只刷新当前卡片的数据
      const result = await fetchSupplyDetail(newDimension, selectedBrand, selectedWeek);
      setLocalData(result);
      // 先更新本地数据，再通知父组件
      onDimensionChange(newDimension);
    } catch (error) {
      console.error('Failed to load supply detail data:', error);
    } finally {
      setLocalLoading(false);
    }
  };

  // 处理新的数据结构，转换为表格数据
  const getTableData = () => {
    if (!localData || !localData.data || localData.data.length === 0) return [];

    if (selectedDimension === '整体') {
      // 整体维度：显示8周数据，按周次倒序排列
      return localData.data.map((item, index) => ({
        key: index,
        dimension: item.dimension_value,
        gmv: item.gmv || 0,
        gmv_ratio: 0, // 整体维度没有占比概念
        gmv_wow: item.gmv_wow || 0,
        store_count: item.store_count || 0,
        store_count_wow: 0, // 暂不计算
        store_penetration: item.store_penetration || 0,
        store_penetration_wow: 0, // 暂不计算
        store_activity_rate: item.store_activity_rate || 0,
        store_activity_rate_wow: 0, // 暂不计算
        avg_sku_per_store: item.avg_sku_per_store || 0,
        avg_sku_per_store_wow: 0, // 暂不计算
        sku_sold_out_rate: item.sku_sold_out_rate || 0,
        sku_sold_out_rate_wow: 0 // 暂不计算
      }));
    } else {
      // 其他维度：显示选定周的各维度数据
      // 计算总GMV用于占比计算
      const totalGmv = localData.data.reduce((sum, item) => sum + (item.gmv || 0), 0);

      return localData.data.map((item, index) => {
        const baseData = {
          key: index,
          dimension: item.dimension_value,
          gmv: item.gmv || 0,
          gmv_ratio: totalGmv > 0 ? (item.gmv || 0) / totalGmv : 0,
          gmv_wow: item.gmv_wow || 0,
          store_count: item.store_count || 0,
          store_count_wow: 0, // 暂不计算
          store_penetration: item.store_penetration || 0,
          store_penetration_wow: 0, // 暂不计算
          store_activity_rate: item.store_activity_rate || 0,
          store_activity_rate_wow: 0, // 暂不计算
          avg_sku_per_store: item.avg_sku_per_store || 0,
          avg_sku_per_store_wow: 0, // 暂不计算
          sku_sold_out_rate: item.sku_sold_out_rate || 0,
          sku_sold_out_rate_wow: 0 // 暂不计算
        };

        // 如果是重点商品维度，拆分UPC和商品名称
        if (selectedDimension === '重点商品' && item.dimension_value) {
          const parts = item.dimension_value.split('-');
          if (parts.length >= 2) {
            baseData.upc = parts[0];
            baseData.product_name = parts.slice(1).join('-'); // 处理商品名称中可能包含'-'的情况
          } else {
            baseData.upc = '';
            baseData.product_name = item.dimension_value;
          }
        }

        return baseData;
      });
    }
  };

  // 构建动态列配置
  const getColumns = () => {
    const columns = [];

    // 第一列：根据维度类型决定列配置
    if (selectedDimension === '整体') {
      columns.push({
        title: '周次',
        dataIndex: 'dimension',
        key: 'dimension',
        width: 120,
        fixed: 'left'
      });
    } else if (selectedDimension === '重点商品') {
      // 重点商品维度：拆分为UPC和重点商品两列
      columns.push(
        {
          title: 'UPC',
          dataIndex: 'upc',
          key: 'upc',
          width: 120,
          fixed: 'left'
        },
        {
          title: '重点商品',
          dataIndex: 'product_name',
          key: 'product_name',
          width: 150,
          fixed: 'left'
        }
      );
    } else {
      columns.push({
        title: selectedDimension,
        dataIndex: 'dimension',
        key: 'dimension',
        width: 120,
        fixed: 'left'
      });
    }

    return columns;
  };

  const columns = [
    ...getColumns(),
    {
      title: 'GMV',
      dataIndex: 'gmv',
      key: 'gmv',
      width: 120,
      render: (value) => formatValue(value, 'currency')
    },
    {
      title: 'GMV占比',
      dataIndex: 'gmv_ratio',
      key: 'gmv_ratio',
      width: 100,
      render: (value) => formatValue(value, 'percentage')
    },
    {
      title: 'GMV周环比',
      dataIndex: 'gmv_wow',
      key: 'gmv_wow',
      width: 120,
      render: (value) => (
        <span style={{ color: getWowColor(value) }}>
          {getWowIcon(value)}
          {formatValue(value, 'percentage')}
        </span>
      )
    },
    {
      title: '铺货门店数',
      dataIndex: 'store_count',
      key: 'store_count',
      width: 120,
      render: (value) => formatValue(value, 'number')
    },
    {
      title: '门店数周环比',
      dataIndex: 'store_count_wow',
      key: 'store_count_wow',
      width: 120,
      render: (value) => (
        <span style={{ color: getWowColor(value) }}>
          {getWowIcon(value)}
          {formatValue(value, 'percentage')}
        </span>
      )
    },
    {
      title: '店铺渗透率',
      dataIndex: 'store_penetration',
      key: 'store_penetration',
      width: 120,
      render: (value) => formatValue(value, 'percentage')
    },
    {
      title: '渗透率周环比',
      dataIndex: 'store_penetration_wow',
      key: 'store_penetration_wow',
      width: 120,
      render: (value) => (
        <span style={{ color: getWowColor(value) }}>
          {getWowIcon(value)}
          {formatValue(value, 'percentage')}
        </span>
      )
    },
    {
      title: '店铺动销率',
      dataIndex: 'store_activity_rate',
      key: 'store_activity_rate',
      width: 120,
      render: (value) => formatValue(value, 'percentage')
    },
    {
      title: '动销率周环比',
      dataIndex: 'store_activity_rate_wow',
      key: 'store_activity_rate_wow',
      width: 120,
      render: (value) => (
        <span style={{ color: getWowColor(value) }}>
          {getWowIcon(value)}
          {formatValue(value, 'percentage')}
        </span>
      )
    },
    {
      title: '店均SKU数',
      dataIndex: 'avg_sku_per_store',
      key: 'avg_sku_per_store',
      width: 120,
      render: (value) => formatValue(value, 'number')
    },
    {
      title: 'SKU数周环比',
      dataIndex: 'avg_sku_per_store_wow',
      key: 'avg_sku_per_store_wow',
      width: 120,
      render: (value) => (
        <span style={{ color: getWowColor(value) }}>
          {getWowIcon(value)}
          {formatValue(value, 'percentage')}
        </span>
      )
    },
    {
      title: 'SKU售罄率',
      dataIndex: 'sku_sold_out_rate',
      key: 'sku_sold_out_rate',
      width: 120,
      render: (value) => formatValue(value, 'percentage')
    }
  ];

  return (
    <Card 
      title="供给明细数据" 
      extra={
        <Select
          value={selectedDimension}
          onChange={handleDimensionChange}
          style={{ width: 120 }}
          size="small"
          loading={localLoading}
        >
          {dimensionOptions.map(option => (
            <Option key={option.value} value={option.value}>
              {option.label}
            </Option>
          ))}
        </Select>
      }
      style={{ 
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}
    >
      <Table
        columns={columns}
        dataSource={getTableData()}
        rowKey="key"
        scroll={{ x: 1500 }}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
        }}
        size="small"
        bordered
        loading={localLoading}
      />
    </Card>
  );
};

export default SupplyDetailTable; 