import React, { useRef, useEffect } from 'react';
import { Row, Col, Card, Skeleton } from 'antd';
import * as echarts from 'echarts';

// 平台配色方案
const platformColors = {
  '美团': '#FFD161',
  '饿了么': '#0086FF',
  '京东到家': '#01af00',
  '多点': '#FF6F00',
  '淘鲜达': '#FF5500'
};

// 双柱状图卡片组件
const DualBarChartCard = ({ title, data }) => {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  useEffect(() => {
    if (!chartRef.current || !data || !Array.isArray(data) || data.length === 0) return;

    // 初始化图表
    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
    }

    // 准备数据
    const platforms = data.map(item => item.platform);
    let newUserValues = [];
    let oldUserValues = [];
    let isCount = false;
    let isCurrency = false;
    let isArpu = false;

    // 根据标题确定要显示的数据
    if (title === '新老客数量') {
      newUserValues = data.map(item => item.new_user_count || 0);
      oldUserValues = data.map(item => item.old_user_count || 0);
      isCount = true;
    } else if (title === '新老客销售额') {
      newUserValues = data.map(item => item.new_user_gmv || 0);
      oldUserValues = data.map(item => item.old_user_gmv || 0);
      isCurrency = true;
    } else if (title === '新老客客单价') {
      newUserValues = data.map(item => item.new_user_arpu || 0);
      oldUserValues = data.map(item => item.old_user_arpu || 0);
      isCurrency = true;
      isArpu = true;
    }

    // 配置选项
    const option = {
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '20%',
        containLabel: true
      },
      legend: {
        data: ['新客', '老客'],
        top: '5%',
        textStyle: {
          fontSize: 11,
          color: '#666'
        }
      },
      xAxis: {
        type: 'category',
        data: platforms,
        axisLabel: {
          fontSize: 11,
          color: '#666',
          interval: 0,
          rotate: platforms.some(p => p.length > 3) ? 45 : 0
        },
        axisLine: {
          lineStyle: {
            color: '#e8e8e8'
          }
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          fontSize: 10,
          color: '#666',
          formatter: function(value) {
            if (isCurrency) {
              if (isArpu) {
                // 客单价保留1位小数
                return value.toFixed(1);
              } else if (value >= 10000) {
                return Math.round(value / 10000) + '万';
              }
              return Math.round(value).toLocaleString();
            } else if (isCount) {
              return Math.round(value).toLocaleString();
            }
            return value;
          }
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0'
          }
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function(params) {
          let result = `${params[0].name}<br/>`;
          params.forEach(param => {
            const color = param.color;
            let value = param.value;
            let unit = '';
            
            if (isCurrency) {
              if (isArpu) {
                // 客单价保留1位小数
                value = value.toFixed(1);
              } else {
                value = Math.round(value).toLocaleString();
              }
              unit = '元';
            } else if (isCount) {
              value = Math.round(value).toLocaleString();
              unit = '人';
            }
            
            result += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
            result += `${param.seriesName}: ${value}${unit}<br/>`;
          });
          return result;
        }
      },
      series: [
        {
          name: '新客',
          type: 'bar',
          data: newUserValues,
          itemStyle: {
            color: '#52c41a',
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(82, 196, 26, 0.3)'
            }
          },
          barWidth: '35%'
        },
        {
          name: '老客',
          type: 'bar',
          data: oldUserValues,
          itemStyle: {
            color: '#1890ff',
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(24, 144, 255, 0.3)'
            }
          },
          barWidth: '35%'
        }
      ]
    };

    chartInstance.current.setOption(option);

    // 处理窗口大小变化
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [data, title]);

  useEffect(() => {
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, []);

  return (
    <Card
      title={title}
      style={{
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        height: '300px'
      }}
      bodyStyle={{ padding: '12px', height: 'calc(100% - 57px)' }}
    >
      <div
        ref={chartRef}
        style={{
          width: '100%',
          height: '100%',
          minHeight: '200px'
        }}
      />
    </Card>
  );
};

const UserPlatformCharts = ({ data, loading }) => {
  if (loading) {
    return (
      <div>
        <div className="section-title" style={{ marginBottom: 16 }}>
          新老客图表分析
        </div>
        <Row gutter={[16, 16]}>
          {[1, 2, 3].map(i => (
            <Col xs={24} sm={12} lg={8} key={i}>
              <Card style={{ height: '300px' }}>
                <Skeleton active />
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    );
  }

  if (!data || !data.platform_data || !Array.isArray(data.platform_data) || data.platform_data.length === 0) {
    return (
      <div>
        <div className="section-title" style={{ marginBottom: 16 }}>
          新老客图表分析
        </div>
        <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
          暂无数据
        </div>
      </div>
    );
  }

  const platformData = data.platform_data;

  return (
    <div>
      <div className="section-title" style={{ marginBottom: 16 }}>
        新老客图表分析
      </div>
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={8}>
          <DualBarChartCard title="新老客数量" data={platformData} />
        </Col>
        <Col xs={24} sm={12} lg={8}>
          <DualBarChartCard title="新老客销售额" data={platformData} />
        </Col>
        <Col xs={24} sm={12} lg={8}>
          <DualBarChartCard title="新老客客单价" data={platformData} />
        </Col>
      </Row>
    </div>
  );
};

export default UserPlatformCharts;
