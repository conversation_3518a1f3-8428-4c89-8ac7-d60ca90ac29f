import React, { useState, useRef } from 'react';
import { Row, Col, DatePicker, Button, Space, message, Modal, Select, Progress } from 'antd';
import { FileImageOutlined, FilePdfOutlined, FileExcelOutlined, DownloadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import isoWeek from 'dayjs/plugin/isoWeek';
import 'dayjs/locale/zh-cn';
import { exportExcel, exportAllExcel, exportExcelWithProgress, getExportProgress, downloadExportResult } from '../services/api';

// 配置dayjs
dayjs.extend(weekOfYear);
dayjs.extend(isoWeek);
dayjs.locale('zh-cn');

const HeaderToolbar = ({ selectedWeekMoment, onWeekChange, brand = '', aiInsights = '', activeModule = 'trading' }) => {
  const [excelModalVisible, setExcelModalVisible] = useState(false);
  const [selectedPage, setSelectedPage] = useState('');
  const [exporting, setExporting] = useState(false);
  const [batchExporting, setBatchExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportMessage, setExportMessage] = useState('');
  const [exportTaskId, setExportTaskId] = useState(null);
  const [singleExportProgress, setSingleExportProgress] = useState(0);
  const [singleExportMessage, setSingleExportMessage] = useState('');
  const [singleExportTaskId, setSingleExportTaskId] = useState(null);
  const progressIntervalRef = useRef(null);
  const singleProgressIntervalRef = useRef(null);

  // 将moment对象转换为dayjs对象
  const convertToDayjs = (momentObj) => {
    if (!momentObj || !momentObj.isValid()) {
      return dayjs().subtract(1, 'week').startOf('isoWeek');
    }
    // 如果已经是dayjs对象，直接返回
    if (momentObj.format && momentObj.isoWeek) {
      return momentObj;
    }
    // 如果是moment对象，转换为dayjs
    return dayjs(momentObj.toDate()).startOf('isoWeek');
  };

  const safeDayjs = convertToDayjs(selectedWeekMoment);



  // 格式化周显示文本
  const formatWeekDisplay = (dayjsObj) => {
    if (!dayjsObj || !dayjsObj.isValid()) {
      return '无效日期';
    }
    const startOfWeek = dayjsObj.startOf('isoWeek');
    const endOfWeek = dayjsObj.endOf('isoWeek');
    const weekNumber = dayjsObj.isoWeek();
    const year = dayjsObj.year();

    return `${year}年第${weekNumber}周 (${startOfWeek.format('MM/DD')} - ${endOfWeek.format('MM/DD')})`;
  };

  // 处理周选择器变化
  const handleWeekPickerChange = (date) => {
    if (date) {
      // 确保传递的是dayjs对象的ISO周开始
      const weekStart = date.startOf('isoWeek');
      onWeekChange(weekStart);
    }
  };

  const handleExport = (type) => {
    if (type === 'Excel') {
      setExcelModalVisible(true);
    } else {
      message.info(`正在导出${type}格式...`);
      // 这里后续实现实际的导出逻辑
    }
  };

  // 捕获页面图表截图（当前平台，当前维度）
  const captureChartImages = async () => {
    try {
      const chartImages = {};

      // 首先捕获固定的图表（平台占比图和趋势图）
      await captureFixedCharts(chartImages);

      // 然后捕获当前维度的图表
      const currentDimension = getCurrentDimension();
      const chartData = await captureSingleDimensionChartDirect();
      if (chartData) {
        const chartKey = getDimensionChartKey(currentDimension);
        chartImages[chartKey] = chartData;
        console.log(`捕获当前维度图表: ${chartKey}`);
      }

      console.log('最终捕获的图表:', Object.keys(chartImages));
      return chartImages;
    } catch (error) {
      console.error('捕获图表截图失败:', error);
      return {};
    }
  };

  // 捕获所有平台的图表截图 - 简化版本
  const captureAllPlatformCharts = async () => {
    // 显示导出提示
    showExportProgress('正在准备图表捕获，请耐心等待...');

    try {
      console.log('🎯 开始捕获所有平台的图表截图...');

      // 保存当前状态
      const originalPlatform = getCurrentPlatform();
      const originalDimension = getCurrentDimension();
      console.log(`原始状态 - 平台: ${originalPlatform}, 维度: ${originalDimension}`);

      const allChartImages = {};
      const platforms = ['全平台', '美团', '饿了么', '京东到家', '多点', '淘鲜达'];
      const dimensions = ['子品牌', '品线', '渠道类型', '大区'];

      // 为每个平台捕获所有图表
      for (let i = 0; i < platforms.length; i++) {
        const platform = platforms[i];
        console.log(`\n🏢 开始处理平台: ${platform}`);

        // 更新进度提示
        const progress = Math.round(((i + 1) / platforms.length) * 100);
        showExportProgress(`正在处理 ${platform} 平台 (${i + 1}/${platforms.length})，请勿刷新页面...`);

        // 切换到目标平台
        if (platform !== getCurrentPlatform()) {
          console.log(`🔄 切换平台到: ${platform}`);
          showExportProgress(`正在切换到 ${platform} 平台，请稍候...`);
          await switchToPlatform(platform);

          // 等待平台数据加载完成
          console.log(`  ⏳ 等待平台 ${platform} 数据加载...`);
          showExportProgress(`正在加载 ${platform} 平台数据，请耐心等待...`);
          await waitForPlatformDataLoad(platform);
        }

        // 1. 捕获当前平台的固定图表
        console.log(`  📊 捕获 ${platform} 的固定图表...`);
        const platformFixedCharts = {};
        await captureFixedCharts(platformFixedCharts);

        // 为固定图表添加平台前缀
        Object.keys(platformFixedCharts).forEach(chartKey => {
          const platformKey = `${platform}_${chartKey}`;
          allChartImages[platformKey] = platformFixedCharts[chartKey];
          console.log(`  ✅ 保存固定图表: ${platformKey}`);
        });

        // 2. 为每个维度捕获图表
        for (let j = 0; j < dimensions.length; j++) {
          const dimension = dimensions[j];
          console.log(`  📈 处理维度: ${dimension}`);

          // 更新详细进度
          showExportProgress(`正在处理 ${platform} 平台的 ${dimension} 维度 (${j + 1}/${dimensions.length})...`);

          // 切换到目标维度
          if (dimension !== getCurrentDimension()) {
            console.log(`    🔄 切换维度到: ${dimension}`);
            showExportProgress(`正在切换到 ${dimension} 维度，请稍候...`);
            await switchToDimension(dimension);

            // 等待维度数据加载完成
            console.log(`    ⏳ 等待 ${platform}-${dimension} 数据加载...`);
            showExportProgress(`正在加载 ${platform}-${dimension} 数据，请耐心等待...`);
            await waitForDimensionDataLoad(dimension, platform);
          }

          // 捕获当前维度的图表
          console.log(`    📸 捕获 ${platform}-${dimension} 图表...`);
          showExportProgress(`正在捕获 ${platform}-${dimension} 图表...`);
          const chartData = await captureSingleDimensionChartDirect();

          if (chartData) {
            const chartKey = `${platform}_${getDimensionChartKey(dimension)}`;
            allChartImages[chartKey] = chartData;
            console.log(`    ✅ 保存图表: ${chartKey}`);
          } else {
            console.warn(`    ❌ 未能捕获 ${platform}-${dimension} 图表`);
          }
        }

        console.log(`✅ 完成平台 ${platform} 的所有图表捕获`);
      }

      // 恢复到原始状态
      console.log(`\n🔄 恢复原始状态...`);
      if (originalPlatform !== getCurrentPlatform()) {
        await switchToPlatform(originalPlatform);
      }
      if (originalDimension !== getCurrentDimension()) {
        await switchToDimension(originalDimension);
      }

      console.log(`🎉 所有图表捕获完成，共 ${Object.keys(allChartImages).length} 个图表`);
      console.log('图表列表:', Object.keys(allChartImages));

      // 更新最终状态
      showExportProgress('图表捕获完成，正在生成Excel文件...');

      return allChartImages;
    } catch (error) {
      console.error('❌ 捕获所有平台图表失败:', error);
      hideExportProgress();
      return {};
    }
  };

  // 获取当前选中的维度
  const getCurrentDimension = () => {
    try {
      let currentDimensionElement = document.querySelector('.dimension-section .dimension-header .ant-select-selection-item');
      if (!currentDimensionElement) {
        const multiDimensionCard = Array.from(document.querySelectorAll('.ant-card')).find(card => {
          const title = card.querySelector('.section-title, .ant-card-head-title');
          return title && (title.textContent.includes('多维表现') || title.textContent.includes('维度'));
        });
        currentDimensionElement = multiDimensionCard?.querySelector('.ant-select-selection-item');
      }
      return currentDimensionElement?.textContent?.trim() || '子品牌';
    } catch (error) {
      console.error('获取当前维度失败:', error);
      return '子品牌';
    }
  };

  // 获取维度对应的图表键名
  const getDimensionChartKey = (dimension) => {
    const mapping = {
      '子品牌': 'subbrand_chart',
      '品线': 'productline_chart',
      '渠道类型': 'channel_chart',
      '大区': 'region_chart'
    };
    return mapping[dimension] || 'subbrand_chart';
  };

  // 直接捕获当前维度图表（不切换维度）
  const captureSingleDimensionChartDirect = async () => {
    try {
      // 查找维度分析区域
      const dimensionSection = document.querySelector('.dimension-section');
      if (!dimensionSection) {
        console.warn('未找到维度分析区域');
        return null;
      }

      // 查找图表容器
      let chartContainer = null;

      // 方法1：查找echarts-for-react容器
      chartContainer = dimensionSection.querySelector('.echarts-for-react');

      if (!chartContainer) {
        // 方法2：查找带有data-chart-type属性的容器
        chartContainer = dimensionSection.querySelector('[data-chart-type]');
      }

      if (!chartContainer) {
        // 方法3：查找包含canvas的div
        const canvasParent = dimensionSection.querySelector('canvas')?.parentElement;
        if (canvasParent) {
          chartContainer = canvasParent;
        }
      }

      if (!chartContainer) {
        console.warn('未找到图表容器');
        return null;
      }

      // 使用现有的captureChartElement函数
      const dataUrl = await captureChartElement(chartContainer);
      return dataUrl;
    } catch (error) {
      console.error('直接捕获维度图表失败:', error);
      return null;
    }
  };

  // 显示导出进度提示
  const showExportProgress = (message) => {
    try {
      // 移除已存在的提示
      const existingModal = document.getElementById('export-progress-modal');
      if (existingModal) {
        existingModal.remove();
      }

      // 创建模态框
      const modal = document.createElement('div');
      modal.id = 'export-progress-modal';
      modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      `;

      // 创建内容容器
      const content = document.createElement('div');
      content.style.cssText = `
        background: white;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        text-align: center;
        max-width: 500px;
        min-width: 400px;
      `;

      // 创建加载动画
      const spinner = document.createElement('div');
      spinner.style.cssText = `
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #1890ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px auto;
      `;

      // 添加CSS动画
      if (!document.getElementById('export-progress-styles')) {
        const style = document.createElement('style');
        style.id = 'export-progress-styles';
        style.textContent = `
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `;
        document.head.appendChild(style);
      }

      // 创建标题
      const title = document.createElement('h3');
      title.style.cssText = `
        margin: 0 0 15px 0;
        color: #1890ff;
        font-size: 18px;
        font-weight: 600;
      `;
      title.textContent = 'Excel导出进行中';

      // 创建消息
      const messageEl = document.createElement('p');
      messageEl.id = 'export-progress-message';
      messageEl.style.cssText = `
        margin: 0 0 20px 0;
        color: #666;
        font-size: 14px;
        line-height: 1.5;
      `;
      messageEl.textContent = message;

      // 创建警告提示
      const warning = document.createElement('div');
      warning.style.cssText = `
        background: #fff7e6;
        border: 1px solid #ffd591;
        border-radius: 4px;
        padding: 12px;
        color: #d46b08;
        font-size: 13px;
        margin-top: 15px;
      `;
      warning.innerHTML = `
        <strong>⚠️ 重要提示：</strong><br>
        • 导出过程需要几分钟时间，请耐心等待<br>
        • 请勿刷新页面或关闭浏览器<br>
        • 请勿进行其他操作，以免影响导出结果
      `;

      // 组装模态框
      content.appendChild(spinner);
      content.appendChild(title);
      content.appendChild(messageEl);
      content.appendChild(warning);
      modal.appendChild(content);
      document.body.appendChild(modal);

      console.log(`📢 显示进度提示: ${message}`);
    } catch (error) {
      console.error('显示进度提示失败:', error);
    }
  };

  // 更新导出进度消息
  const updateExportProgress = (message) => {
    try {
      const messageEl = document.getElementById('export-progress-message');
      if (messageEl) {
        messageEl.textContent = message;
        console.log(`📢 更新进度提示: ${message}`);
      }
    } catch (error) {
      console.error('更新进度提示失败:', error);
    }
  };

  // 隐藏导出进度提示
  const hideExportProgress = () => {
    try {
      const modal = document.getElementById('export-progress-modal');
      if (modal) {
        modal.remove();
        console.log('📢 隐藏进度提示');
      }
    } catch (error) {
      console.error('隐藏进度提示失败:', error);
    }
  };

  // 等待维度数据加载完成
  const waitForDimensionDataLoad = async (dimension, platform) => {
    return new Promise((resolve) => {
      let attempts = 0;
      const maxAttempts = 20; // 最多等待10秒
      const checkInterval = 500; // 每500ms检查一次

      const checkDataLoaded = () => {
        attempts++;
        console.log(`      检查数据加载状态 (${attempts}/${maxAttempts})...`);

        try {
          // 检查1：维度选择器是否显示正确的维度
          const currentDimension = getCurrentDimension();
          if (currentDimension !== dimension) {
            console.log(`      维度还未切换完成: 当前=${currentDimension}, 目标=${dimension}`);
            if (attempts < maxAttempts) {
              setTimeout(checkDataLoaded, checkInterval);
              return;
            }
          }

          // 检查2：图表容器是否存在且有内容
          const dimensionSection = document.querySelector('.dimension-section');
          if (!dimensionSection) {
            console.log(`      维度分析区域未找到`);
            if (attempts < maxAttempts) {
              setTimeout(checkDataLoaded, checkInterval);
              return;
            }
          }

          // 检查3：图表是否已渲染（查找canvas元素）
          const chartContainer = dimensionSection.querySelector('.echarts-for-react');
          const canvas = chartContainer?.querySelector('canvas');
          if (!canvas) {
            console.log(`      图表Canvas未找到`);
            if (attempts < maxAttempts) {
              setTimeout(checkDataLoaded, checkInterval);
              return;
            }
          }

          // 检查4：Canvas是否有实际内容（检查图表是否完全渲染）
          if (canvas) {
            try {
              const ctx = canvas.getContext('2d');
              const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
              const data = imageData.data;

              // 检查是否有非透明像素（简单的内容检测）
              let hasContent = false;
              for (let i = 3; i < data.length; i += 4) { // 检查alpha通道
                if (data[i] > 0) {
                  hasContent = true;
                  break;
                }
              }

              if (!hasContent) {
                console.log(`      图表Canvas为空，等待渲染...`);
                if (attempts < maxAttempts) {
                  setTimeout(checkDataLoaded, checkInterval);
                  return;
                }
              } else {
                console.log(`      ✅ 图表Canvas有内容`);
              }
            } catch (error) {
              console.log(`      Canvas内容检查失败: ${error.message}`);
            }
          }

          // 检查5：loading状态是否已结束
          const loadingElement = dimensionSection.querySelector('.ant-spin-spinning');
          if (loadingElement) {
            console.log(`      图表仍在加载中`);
            if (attempts < maxAttempts) {
              setTimeout(checkDataLoaded, checkInterval);
              return;
            }
          }

          // 所有检查通过或超时
          if (attempts >= maxAttempts) {
            console.log(`      ⚠️ 等待超时，继续执行 (${attempts * checkInterval}ms)`);
          } else {
            console.log(`      ✅ 数据加载完成 (${attempts * checkInterval}ms)`);
          }

          // 额外等待更长时间确保图表完全渲染
          console.log(`      ⏳ 额外等待图表渲染完成...`);
          setTimeout(resolve, 1500); // 增加到1.5秒

        } catch (error) {
          console.log(`      检查过程出错: ${error.message}`);
          if (attempts < maxAttempts) {
            setTimeout(checkDataLoaded, checkInterval);
          } else {
            resolve();
          }
        }
      };

      // 开始检查
      checkDataLoaded();
    });
  };

  // 等待平台数据加载完成
  const waitForPlatformDataLoad = async (platform) => {
    return new Promise((resolve) => {
      let attempts = 0;
      const maxAttempts = 15; // 最多等待7.5秒
      const checkInterval = 500; // 每500ms检查一次

      const checkPlatformLoaded = () => {
        attempts++;
        console.log(`    检查平台数据加载状态 (${attempts}/${maxAttempts})...`);

        try {
          // 检查1：平台选择器是否显示正确的平台
          const currentPlatform = getCurrentPlatform();
          if (currentPlatform !== platform) {
            console.log(`    平台还未切换完成: 当前=${currentPlatform}, 目标=${platform}`);
            if (attempts < maxAttempts) {
              setTimeout(checkPlatformLoaded, checkInterval);
              return;
            }
          }

          // 检查2：图表区域是否存在
          const chartsSection = document.querySelector('.charts-section');
          if (!chartsSection) {
            console.log(`    图表区域未找到`);
            if (attempts < maxAttempts) {
              setTimeout(checkPlatformLoaded, checkInterval);
              return;
            }
          }

          // 检查3：是否有loading状态
          const loadingElements = document.querySelectorAll('.ant-spin-spinning');
          if (loadingElements.length > 0) {
            console.log(`    页面仍在加载中 (${loadingElements.length}个loading)`);
            if (attempts < maxAttempts) {
              setTimeout(checkPlatformLoaded, checkInterval);
              return;
            }
          }

          // 所有检查通过或超时
          if (attempts >= maxAttempts) {
            console.log(`    ⚠️ 平台数据等待超时，继续执行 (${attempts * checkInterval}ms)`);
          } else {
            console.log(`    ✅ 平台数据加载完成 (${attempts * checkInterval}ms)`);
          }

          // 额外等待确保数据完全加载
          setTimeout(resolve, 1000);

        } catch (error) {
          console.log(`    平台检查过程出错: ${error.message}`);
          if (attempts < maxAttempts) {
            setTimeout(checkPlatformLoaded, checkInterval);
          } else {
            resolve();
          }
        }
      };

      // 开始检查
      checkPlatformLoaded();
    });
  };

  // 获取当前选中的平台
  const getCurrentPlatform = () => {
    try {
      // 查找平台筛选器 - 正确的Radio Button结构
      const checkedRadio = document.querySelector('.trading-section .ant-radio-button-wrapper-checked');
      if (checkedRadio) {
        const currentPlatform = checkedRadio.textContent.trim();
        console.log(`获取当前平台: ${currentPlatform}`);
        return currentPlatform;
      }

      // 备选方案：查找包含"平台筛选"的区域
      const platformSection = document.querySelector('.platform-filters');
      if (platformSection) {
        const checkedRadio = platformSection.querySelector('.ant-radio-button-wrapper-checked');
        if (checkedRadio) {
          const currentPlatform = checkedRadio.textContent.trim();
          console.log(`通过备选方案获取当前平台: ${currentPlatform}`);
          return currentPlatform;
        }
      }

      console.warn('未找到平台筛选器，默认返回全平台');
      return '全平台';
    } catch (error) {
      console.error('获取当前平台失败:', error);
      return '全平台';
    }
  };

  // 切换到指定平台
  const switchToPlatform = async (targetPlatform) => {
    return new Promise((resolve) => {
      try {
        console.log(`🎯 开始切换到平台: ${targetPlatform}`);

        const currentPlatform = getCurrentPlatform();
        if (currentPlatform === targetPlatform) {
          console.log(`✅ 已经是目标平台: ${targetPlatform}`);
          resolve();
          return;
        }

        // 查找平台Radio按钮组
        const platformRadioGroup = document.querySelector('.trading-section .platform-radio-group');
        if (!platformRadioGroup) {
          console.warn('❌ 未找到平台Radio按钮组');
          resolve();
          return;
        }

        // 查找目标平台的Radio按钮
        const radioButtons = platformRadioGroup.querySelectorAll('.ant-radio-button-wrapper');
        const targetRadio = Array.from(radioButtons).find(radio =>
          radio.textContent.trim() === targetPlatform
        );

        if (!targetRadio) {
          console.warn(`❌ 未找到目标平台按钮: ${targetPlatform}`);
          const availablePlatforms = Array.from(radioButtons).map(r => r.textContent.trim());
          console.log(`可用平台: ${availablePlatforms.join(', ')}`);
          resolve();
          return;
        }

        console.log(`📱 点击平台按钮: ${targetPlatform}`);

        // 直接点击Radio按钮
        targetRadio.click();

        // 验证切换结果
        setTimeout(() => {
          const newPlatform = getCurrentPlatform();
          if (newPlatform === targetPlatform) {
            console.log(`🎉 平台切换成功: ${targetPlatform}`);
          } else {
            console.warn(`⚠️ 平台切换可能失败: 期望=${targetPlatform}, 实际=${newPlatform}`);
          }
          resolve();
        }, 500);

      } catch (error) {
        console.error('❌ 切换平台时出错:', error);
        resolve(); // 不阻塞流程
      }
    });
  };

  // 捕获单个图表元素
  const captureChartElement = async (element) => {
    try {
      let dataUrl;

      console.log(`📸 开始捕获图表元素: ${element.tagName}, 类名: ${element.className}`);

      if (element.tagName === 'CANVAS') {
        console.log('📸 直接捕获Canvas元素');
        dataUrl = element.toDataURL('image/png');
      } else if (element.tagName === 'svg') {
        console.log('📸 捕获SVG元素');
        const svgData = new XMLSerializer().serializeToString(element);
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();

        canvas.width = element.clientWidth || 600;
        canvas.height = element.clientHeight || 400;

        const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
        const url = URL.createObjectURL(svgBlob);

        await new Promise((resolve, reject) => {
          img.onload = () => {
            ctx.drawImage(img, 0, 0);
            dataUrl = canvas.toDataURL('image/png');
            URL.revokeObjectURL(url);
            resolve();
          };
          img.onerror = reject;
          img.src = url;
        });
      } else if (element.tagName === 'DIV') {
        console.log('📸 处理DIV容器，查找内部Canvas或SVG');

        // 查找内部的canvas元素
        const canvas = element.querySelector('canvas');
        if (canvas) {
          console.log('📸 在DIV内找到Canvas元素');
          dataUrl = canvas.toDataURL('image/png');
        } else {
          // 查找内部的svg元素
          const svg = element.querySelector('svg');
          if (svg) {
            console.log('📸 在DIV内找到SVG元素');
            const svgData = new XMLSerializer().serializeToString(svg);
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            canvas.width = svg.clientWidth || 600;
            canvas.height = svg.clientHeight || 400;

            const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
            const url = URL.createObjectURL(svgBlob);

            await new Promise((resolve, reject) => {
              img.onload = () => {
                ctx.drawImage(img, 0, 0);
                dataUrl = canvas.toDataURL('image/png');
                URL.revokeObjectURL(url);
                resolve();
              };
              img.onerror = reject;
              img.src = url;
            });
          } else {
            console.warn('📸 DIV内未找到Canvas或SVG元素，尝试使用html2canvas');

            // 使用html2canvas作为备选方案
            if (window.html2canvas) {
              const canvas = await window.html2canvas(element, {
                backgroundColor: '#ffffff',
                scale: 2,
                logging: false,
                useCORS: true,
                allowTaint: true
              });
              dataUrl = canvas.toDataURL('image/png');
              console.log('📸 使用html2canvas成功捕获');
            } else {
              console.warn('📸 html2canvas不可用');
            }
          }
        }
      }

      if (dataUrl) {
        console.log(`✅ 成功捕获图表，数据长度: ${dataUrl.length}`);
      } else {
        console.warn('❌ 未能生成图表数据');
      }

      return dataUrl;
    } catch (error) {
      console.warn('❌ 捕获图表元素失败:', error);
      return null;
    }
  };

  // 捕获固定图表（平台占比图和趋势图）
  const captureFixedCharts = async (chartImages) => {
    console.log('开始捕获固定图表...');
    const fixedChartElements = document.querySelectorAll('.charts-section .echarts-for-react canvas, .charts-section .recharts-wrapper svg');

    for (let i = 0; i < fixedChartElements.length; i++) {
      const element = fixedChartElements[i];
      const dataUrl = await captureChartElement(element);

      if (dataUrl) {
        // 通过数据属性或位置识别图表类型
        const chartContainer = element.closest('[data-chart-type]');
        const chartType = chartContainer?.getAttribute('data-chart-type');

        if (chartType) {
          chartImages[chartType] = dataUrl;
          console.log(`捕获固定图表: ${chartType}`);
        } else {
          // 基于位置的备选识别
          if (i === 0) {
            chartImages.platform_chart = dataUrl;
            console.log('捕获平台占比图表 (位置识别)');
          } else {
            chartImages.trend_chart = dataUrl;
            console.log('捕获趋势图表 (位置识别)');
          }
        }
      }
    }
  };

  // 捕获所有维度的图表
  const captureDimensionCharts = async (chartImages) => {
    console.log('开始捕获维度图表...');

    // 获取当前选中的维度 - 更精确地定位
    let currentDimensionElement = document.querySelector('.dimension-section .dimension-header .ant-select-selection-item');
    if (!currentDimensionElement) {
      // 备选方案：查找包含"多维表现"的卡片内的选择器
      const multiDimensionCard = Array.from(document.querySelectorAll('.ant-card')).find(card => {
        const title = card.querySelector('.section-title, .ant-card-head-title');
        return title && (title.textContent.includes('多维表现') || title.textContent.includes('维度'));
      });
      currentDimensionElement = multiDimensionCard?.querySelector('.ant-select-selection-item');
    }
    const currentDimension = currentDimensionElement?.textContent?.trim() || '子品牌';

    console.log(`当前选中维度: ${currentDimension}`);

    // 定义所有需要捕获的维度
    const dimensions = ['子品牌', '品线', '渠道类型', '大区'];
    const dimensionMapping = {
      '子品牌': 'subbrand_chart',
      '品线': 'productline_chart',
      '渠道类型': 'channel_chart',
      '大区': 'region_chart'
    };

    // 获取维度选择器
    const dimensionSelect = document.querySelector('.dimension-section .ant-select-selector');

    if (!dimensionSelect) {
      console.warn('未找到维度选择器，只捕获当前维度图表');
      await captureSingleDimensionChart(chartImages, currentDimension, dimensionMapping);
      return;
    }

    // 依次切换到每个维度并捕获图表
    for (const dimension of dimensions) {
      try {
        console.log(`\n=== 开始处理维度: ${dimension} ===`);

        // 如果不是当前维度，需要切换
        if (dimension !== currentDimension) {
          await switchToDimension(dimension);

          // 等待图表重新渲染 - 增加等待时间
          console.log(`等待维度 ${dimension} 的图表重新渲染...`);
          await new Promise(resolve => setTimeout(resolve, 2000));

          // 验证图表是否已经更新
          await waitForChartUpdate(dimension);
        }

        // 捕获当前维度的图表
        await captureSingleDimensionChart(chartImages, dimension, dimensionMapping);

        console.log(`=== 完成维度 ${dimension} 的处理 ===\n`);

      } catch (error) {
        console.error(`捕获维度 ${dimension} 图表失败:`, error);
      }
    }

    // 最后切换回原来的维度
    if (currentDimension !== dimensions[dimensions.length - 1]) {
      console.log(`切换回原维度: ${currentDimension}`);
      await switchToDimension(currentDimension);
    }
  };

  // 等待图表更新完成
  const waitForChartUpdate = async (expectedDimension) => {
    return new Promise((resolve) => {
      let attempts = 0;
      const maxAttempts = 10;

      const checkChart = () => {
        attempts++;

        // 检查维度选择器是否已更新 - 更精确地定位
        let dimensionSelect = document.querySelector('.dimension-section .dimension-header .ant-select-selection-item');
        if (!dimensionSelect) {
          const multiDimensionCard = Array.from(document.querySelectorAll('.ant-card')).find(card => {
            const title = card.querySelector('.section-title, .ant-card-head-title');
            return title && (title.textContent.includes('多维表现') || title.textContent.includes('维度'));
          });
          dimensionSelect = multiDimensionCard?.querySelector('.ant-select-selection-item');
        }
        const currentDimension = dimensionSelect?.textContent?.trim() || '';

        console.log(`检查图表更新 (${attempts}/${maxAttempts}): 当前维度="${currentDimension}", 期望维度="${expectedDimension}"`);

        // 检查维度是否已切换成功
        if (currentDimension === expectedDimension || attempts >= maxAttempts) {
          console.log(`图表更新检查完成: ${currentDimension === expectedDimension ? '成功' : '超时'}`);
          resolve();
        } else {
          setTimeout(checkChart, 200);
        }
      };

      checkChart();
    });
  };

  // 切换到指定维度 - React组件直接操作版本
  const switchToDimension = async (dimension) => {
    return new Promise((resolve) => {
      try {
        console.log(`🎯 开始切换到维度: ${dimension}`);

        // 检查当前维度
        const getCurrentDimension = () => {
          let currentElement = document.querySelector('.dimension-section .dimension-header .ant-select-selection-item');
          if (!currentElement) {
            const cards = document.querySelectorAll('.ant-card');
            for (const card of cards) {
              const title = card.querySelector('.section-title, .ant-card-head-title');
              if (title && (title.textContent.includes('多维') || title.textContent.includes('维度'))) {
                currentElement = card.querySelector('.ant-select-selection-item');
                break;
              }
            }
          }
          return currentElement?.textContent?.trim();
        };

        const currentDimension = getCurrentDimension();
        console.log(`当前维度: ${currentDimension}`);

        if (currentDimension === dimension) {
          console.log(`✅ 已经是目标维度: ${dimension}`);
          resolve();
          return;
        }

        // 尝试直接操作React组件
        console.log(`🔍 查找React组件实例...`);
        const reactSuccess = tryReactComponentChange(dimension);

        if (reactSuccess) {
          console.log(`✅ React组件切换成功`);
          setTimeout(() => {
            const newDimension = getCurrentDimension();
            if (newDimension === dimension) {
              console.log(`🎉 维度切换成功: ${dimension}`);
              resolve();
            } else {
              console.warn(`⚠️ React切换验证失败，尝试模拟用户交互...`);
              tryUserInteractionSimulation(dimension, getCurrentDimension, resolve);
            }
          }, 800);
        } else {
          console.warn(`❌ React组件切换失败，尝试模拟用户交互...`);
          tryUserInteractionSimulation(dimension, getCurrentDimension, resolve);
        }

      } catch (error) {
        console.error('❌ 切换维度时出错:', error);
        resolve(); // 不阻塞流程
      }
    });
  };

  // 尝试直接操作React组件
  const tryReactComponentChange = (dimension) => {
    try {
      console.log(`🔍 查找维度选择器的React组件...`);

      // 查找维度选择器元素
      let selectElement = document.querySelector('.dimension-section .dimension-header .ant-select');
      if (!selectElement) {
        const cards = document.querySelectorAll('.ant-card');
        for (const card of cards) {
          const title = card.querySelector('.section-title, .ant-card-head-title');
          if (title && (title.textContent.includes('多维') || title.textContent.includes('维度'))) {
            selectElement = card.querySelector('.ant-select');
            break;
          }
        }
      }

      if (!selectElement) {
        console.warn('❌ 未找到Select元素');
        return false;
      }

      console.log(`✅ 找到Select元素`);

      // 尝试多种方式获取React实例
      const reactInstance = getReactInstance(selectElement);

      if (!reactInstance) {
        console.warn('❌ 未找到React实例');
        return false;
      }

      console.log(`✅ 找到React实例`);

      // 尝试调用onChange
      if (reactInstance.props && typeof reactInstance.props.onChange === 'function') {
        console.log(`🎯 调用onChange方法: ${dimension}`);
        reactInstance.props.onChange(dimension);
        return true;
      }

      // 尝试通过memoizedProps调用
      if (reactInstance.memoizedProps && typeof reactInstance.memoizedProps.onChange === 'function') {
        console.log(`🎯 调用memoizedProps.onChange方法: ${dimension}`);
        reactInstance.memoizedProps.onChange(dimension);
        return true;
      }

      // 尝试查找父级组件的onChange
      let currentFiber = reactInstance;
      let attempts = 0;
      while (currentFiber && attempts < 10) {
        if (currentFiber.memoizedProps && typeof currentFiber.memoizedProps.onChange === 'function') {
          console.log(`🎯 在父级组件中找到onChange，调用: ${dimension}`);
          currentFiber.memoizedProps.onChange(dimension);
          return true;
        }
        currentFiber = currentFiber.return;
        attempts++;
      }

      console.warn('❌ 未找到可用的onChange方法');
      return false;

    } catch (error) {
      console.error('❌ React组件操作失败:', error);
      return false;
    }
  };

  // 获取React实例的通用方法
  const getReactInstance = (element) => {
    try {
      // React 18+
      if (element._reactInternals) {
        return element._reactInternals;
      }

      // React 17
      if (element._reactInternalFiber) {
        return element._reactInternalFiber;
      }

      // 旧版本React
      const keys = Object.keys(element);
      const reactKey = keys.find(key =>
        key.startsWith('__reactInternalInstance') ||
        key.startsWith('__reactFiber')
      );

      if (reactKey) {
        return element[reactKey];
      }

      // 尝试从父元素查找
      let parent = element.parentElement;
      let depth = 0;
      while (parent && depth < 5) {
        const parentInstance = getReactInstance(parent);
        if (parentInstance) {
          return parentInstance;
        }
        parent = parent.parentElement;
        depth++;
      }

      return null;
    } catch (error) {
      console.error('获取React实例失败:', error);
      return null;
    }
  };

  // 模拟完整的用户交互流程
  const tryUserInteractionSimulation = (dimension, getCurrentDimension, resolve) => {
    console.log(`🎭 开始模拟用户交互切换到: ${dimension}`);

    try {
      // 查找选择器
      let selector = document.querySelector('.dimension-section .dimension-header .ant-select-selector');
      if (!selector) {
        const cards = document.querySelectorAll('.ant-card');
        for (const card of cards) {
          const title = card.querySelector('.section-title, .ant-card-head-title');
          if (title && (title.textContent.includes('多维') || title.textContent.includes('维度'))) {
            selector = card.querySelector('.ant-select-selector');
            break;
          }
        }
      }

      if (!selector) {
        console.warn('❌ 未找到选择器，放弃切换');
        resolve();
        return;
      }

      // 模拟完整的鼠标交互序列
      console.log('🖱️ 模拟鼠标交互序列...');

      // 1. 鼠标移入
      const mouseEnterEvent = new MouseEvent('mouseenter', {
        bubbles: true,
        cancelable: true,
        view: window
      });
      selector.dispatchEvent(mouseEnterEvent);

      setTimeout(() => {
        // 2. 鼠标按下
        const mouseDownEvent = new MouseEvent('mousedown', {
          bubbles: true,
          cancelable: true,
          view: window,
          button: 0
        });
        selector.dispatchEvent(mouseDownEvent);

        setTimeout(() => {
          // 3. 鼠标抬起
          const mouseUpEvent = new MouseEvent('mouseup', {
            bubbles: true,
            cancelable: true,
            view: window,
            button: 0
          });
          selector.dispatchEvent(mouseUpEvent);

          setTimeout(() => {
            // 4. 点击事件
            const clickEvent = new MouseEvent('click', {
              bubbles: true,
              cancelable: true,
              view: window,
              button: 0
            });
            selector.dispatchEvent(clickEvent);

            // 5. 等待下拉菜单并处理选项
            setTimeout(() => {
              handleDropdownInteraction(dimension, getCurrentDimension, resolve);
            }, 500);

          }, 50);
        }, 50);
      }, 50);

    } catch (error) {
      console.error('❌ 用户交互模拟失败:', error);
      resolve();
    }
  };

  // 处理下拉菜单交互
  const handleDropdownInteraction = (dimension, getCurrentDimension, resolve) => {
    console.log('🔍 查找下拉菜单...');

    const dropdowns = document.querySelectorAll('.ant-select-dropdown:not(.ant-select-dropdown-hidden)');
    console.log(`找到 ${dropdowns.length} 个下拉菜单`);

    if (dropdowns.length === 0) {
      console.warn('⚠️ 下拉菜单仍未出现，可能是组件被禁用或有其他限制');

      // 最后的尝试：直接修改DOM
      console.log('🔧 尝试直接修改DOM值...');
      tryDirectDOMModification(dimension, getCurrentDimension, resolve);
      return;
    }

    const dropdown = dropdowns[dropdowns.length - 1];
    const options = dropdown.querySelectorAll('.ant-select-item');
    const optionTexts = Array.from(options).map(o => o.textContent.trim());
    console.log(`下拉选项: ${optionTexts.join(', ')}`);

    const targetOption = Array.from(options).find(option =>
      option.textContent.trim() === dimension
    );

    if (targetOption) {
      console.log(`✅ 找到目标选项，模拟点击: ${dimension}`);

      // 模拟完整的选项点击
      const events = ['mouseenter', 'mousedown', 'mouseup', 'click'];
      let eventIndex = 0;

      const triggerNextEvent = () => {
        if (eventIndex < events.length) {
          const eventType = events[eventIndex];
          const event = new MouseEvent(eventType, {
            bubbles: true,
            cancelable: true,
            view: window
          });
          targetOption.dispatchEvent(event);
          eventIndex++;
          setTimeout(triggerNextEvent, 50);
        } else {
          // 验证切换结果
          setTimeout(() => {
            const newDimension = getCurrentDimension();
            if (newDimension === dimension) {
              console.log(`🎉 用户交互模拟成功: ${dimension}`);
            } else {
              console.warn(`⚠️ 用户交互模拟失败: 期望=${dimension}, 实际=${newDimension}`);
            }
            resolve();
          }, 600);
        }
      };

      triggerNextEvent();
    } else {
      console.warn(`❌ 未找到目标选项: ${dimension}`);
      document.body.click(); // 关闭下拉菜单
      resolve();
    }
  };

  // 直接修改DOM作为最后手段
  const tryDirectDOMModification = (dimension, getCurrentDimension, resolve) => {
    console.log('🔧 尝试直接修改DOM值...');

    try {
      const selectionItem = document.querySelector('.dimension-section .ant-select-selection-item');
      if (selectionItem) {
        console.log(`📝 直接修改显示文本为: ${dimension}`);
        selectionItem.textContent = dimension;

        // 触发change事件
        const changeEvent = new Event('change', { bubbles: true });
        selectionItem.dispatchEvent(changeEvent);

        setTimeout(() => {
          const newDimension = getCurrentDimension();
          console.log(`DOM修改结果: ${newDimension}`);
          resolve();
        }, 300);
      } else {
        console.warn('❌ 未找到选择项元素');
        resolve();
      }
    } catch (error) {
      console.error('❌ DOM修改失败:', error);
      resolve();
    }
  };




  // 捕获单个维度的图表
  const captureSingleDimensionChart = async (chartImages, dimension, dimensionMapping) => {
    try {
      console.log(`开始捕获维度 ${dimension} 的图表...`);

      // 查找维度分析区域
      const dimensionSection = document.querySelector('.dimension-section');
      if (!dimensionSection) {
        console.warn('未找到维度分析区域');
        return;
      }

      // 验证当前选中的维度 - 更精确地定位
      let dimensionSelect = dimensionSection.querySelector('.dimension-header .ant-select-selection-item');
      if (!dimensionSelect) {
        // 备选方案：在整个维度区域内查找
        dimensionSelect = dimensionSection.querySelector('.ant-select-selection-item');
      }
      const currentDimension = dimensionSelect?.textContent?.trim() || '';
      console.log(`当前选中维度: "${currentDimension}"`);

      // 检查维度是否匹配
      if (currentDimension !== dimension) {
        console.warn(`⚠️ 当前维度不匹配目标维度 ${dimension}, 当前: ${currentDimension}`);
      }

      // 查找ECharts图表容器 - 改进查找逻辑
      let chartContainer = null;

      // 方法1：查找echarts-for-react容器
      chartContainer = dimensionSection.querySelector('.echarts-for-react');
      console.log(`方法1 - echarts-for-react: ${chartContainer ? '找到' : '未找到'}`);

      if (!chartContainer) {
        // 方法2：查找带有data-chart-type属性的容器
        chartContainer = dimensionSection.querySelector('[data-chart-type]');
        console.log(`方法2 - data-chart-type: ${chartContainer ? '找到' : '未找到'}`);
      }

      if (!chartContainer) {
        // 方法3：查找包含canvas的div
        const canvasParent = dimensionSection.querySelector('canvas')?.parentElement;
        if (canvasParent) {
          chartContainer = canvasParent;
          console.log(`方法3 - canvas父元素: 找到`);
        }
      }

      if (!chartContainer) {
        // 方法4：查找高度大于300px的div（可能是图表容器）
        const divs = dimensionSection.querySelectorAll('div');
        for (const div of divs) {
          const rect = div.getBoundingClientRect();
          if (rect.height > 300 && rect.width > 400) {
            chartContainer = div;
            console.log(`方法4 - 大尺寸div: 找到 (${rect.width}x${rect.height})`);
            break;
          }
        }
      }

      if (!chartContainer) {
        console.warn('❌ 未找到ECharts图表容器');
        return;
      }

      console.log(`✅ 找到图表容器: ${chartContainer.tagName}, 类名: ${chartContainer.className}, 开始截图...`);

      // 使用现有的captureChartElement函数
      const dataUrl = await captureChartElement(chartContainer);

      if (dataUrl) {
        const chartKey = dimensionMapping[dimension] || 'subbrand_chart';
        chartImages[chartKey] = dataUrl;
        console.log(`✅ 成功捕获维度 ${dimension} 的图表，存储为 ${chartKey}`);
      } else {
        console.warn(`❌ 未能获取维度 ${dimension} 的图表数据`);
      }

    } catch (error) {
      console.error(`❌ 捕获维度 ${dimension} 图表失败:`, error);
    }
  };

  // 单页面导出进度轮询
  const startSingleExportProgressPolling = (taskId) => {
    singleProgressIntervalRef.current = setInterval(async () => {
      try {
        const progressData = await getExportProgress(taskId);

        if (progressData.success) {
          setSingleExportProgress(progressData.progress);
          setSingleExportMessage(progressData.message);

          if (progressData.status === 'completed') {
            // 导出完成，下载文件
            clearInterval(singleProgressIntervalRef.current);

            try {
              const downloadResult = await downloadExportResult(taskId);
              message.success(`${selectedPage}导出完成！文件名: ${downloadResult.filename}`);
              setExcelModalVisible(false);
              setSelectedPage('');
            } catch (downloadError) {
              message.error('下载文件失败，请稍后重试');
            }

            setExporting(false);
            setSingleExportProgress(0);
            setSingleExportMessage('');
            setSingleExportTaskId(null);

          } else if (progressData.status === 'error') {
            // 导出失败
            clearInterval(singleProgressIntervalRef.current);
            message.error(`${selectedPage}导出失败: ${progressData.error}`);
            setExporting(false);
            setSingleExportProgress(0);
            setSingleExportMessage('');
            setSingleExportTaskId(null);
          }
        }
      } catch (error) {
        console.error('获取单页面导出进度失败:', error);
      }
    }, 1000);
  };

  // 导出单个页面Excel
  const handleExportSinglePage = async () => {
    if (!selectedPage) {
      message.error('请选择要导出的页面');
      return;
    }

    setExporting(true);
    setSingleExportProgress(0);
    setSingleExportMessage('正在准备导出...');

    try {
      const week = safeDayjs.isoWeek();

      // 如果是交易表现页面，准备AI洞察和图表截图
      let aiContent = '';
      let chartImages = {};

      if (selectedPage === '交易表现') {
        if (aiInsights) {
          aiContent = aiInsights;
        }

        // 捕获所有平台的图表截图
        setSingleExportMessage('正在捕获图表截图...');
        showExportProgress('正在准备图表数据，这可能需要几分钟时间...');
        chartImages = await captureAllPlatformCharts();
        hideExportProgress();
      }

      // 使用进度跟踪的导出API
      const result = await exportExcelWithProgress(selectedPage, week, brand, aiContent, chartImages);

      if (result.success && result.task_id) {
        setSingleExportTaskId(result.task_id);
        // 开始轮询进度
        startSingleExportProgressPolling(result.task_id);
      } else {
        message.error('启动导出失败');
        setExporting(false);
      }
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败，请稍后重试');
      setExporting(false);
      setSingleExportProgress(0);
      setSingleExportMessage('');
      hideExportProgress();
    }
  };

  // 导出所有页面Excel
  const handleExportAllPages = async () => {
    setBatchExporting(true);
    setExportProgress(0);
    setExportMessage('正在启动批量导出...');

    try {
      const week = safeDayjs.isoWeek();

      // 如果是交易表现页面，捕获图表截图
      let chartImages = {};
      if (activeModule === 'trading') {
        showExportProgress('正在准备图表数据，这可能需要几分钟时间...');
        chartImages = await captureAllPlatformCharts();
      }

      // 隐藏图表捕获进度，显示后端处理进度
      hideExportProgress();

      const result = await exportAllExcel(week, brand, chartImages);

      if (result.success && result.task_id) {
        setExportTaskId(result.task_id);
        // 开始轮询进度
        startExportProgressPolling(result.task_id);
      } else {
        message.error('启动批量导出失败');
        setBatchExporting(false);
      }
    } catch (error) {
      console.error('批量导出失败:', error);
      message.error('批量导出失败，请稍后重试');
      setBatchExporting(false);
      hideExportProgress();
    }
  };

  const startExportProgressPolling = (taskId) => {
    // 清除之前的轮询
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
    }

    progressIntervalRef.current = setInterval(async () => {
      try {
        const progressData = await getExportProgress(taskId);

        if (progressData.success) {
          setExportProgress(progressData.progress);
          setExportMessage(progressData.message);

          if (progressData.status === 'completed') {
            // 导出完成，下载文件
            clearInterval(progressIntervalRef.current);

            try {
              const downloadResult = await downloadExportResult(taskId);
              message.success(`批量导出完成！文件名: ${downloadResult.filename}`);
              setExcelModalVisible(false);
            } catch (downloadError) {
              message.error('下载文件失败，请稍后重试');
            }

            setBatchExporting(false);
            setExportProgress(0);
            setExportMessage('');
            setExportTaskId(null);

          } else if (progressData.status === 'error') {
            // 导出失败
            clearInterval(progressIntervalRef.current);
            message.error(`批量导出失败: ${progressData.error}`);
            setBatchExporting(false);
            setExportProgress(0);
            setExportMessage('');
            setExportTaskId(null);
          }
        }
      } catch (error) {
        console.error('获取导出进度失败:', error);
        clearInterval(progressIntervalRef.current);
        message.error('获取导出进度失败');
        setBatchExporting(false);
      }
    }, 1000); // 每秒轮询一次
  };

  // 组件卸载时清理轮询
  React.useEffect(() => {
    return () => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, []);

  // 页面选项
  const pageOptions = [
    { label: '交易表现', value: '交易表现' },
    { label: '用户表现', value: '用户表现' },
    { label: '活动表现', value: '活动表现' },
    { label: 'RTB表现', value: 'RTB表现' },
    { label: '供给表现', value: '供给表现' }
  ];

  return (
    <div className="header-toolbar">
      <Row justify="space-between" align="middle">
        <Col>
          <Space align="center">
            <span style={{ fontWeight: 600, fontSize: '16px', color: '#262626' }}>
              周报自动化看板
            </span>
            <DatePicker.WeekPicker
              value={safeDayjs}
              onChange={handleWeekPickerChange}
              style={{ width: 320 }}
              placeholder="选择周"
              format={(value) => formatWeekDisplay(value)}
              allowClear={false}
            />
          </Space>
        </Col>
        <Col>
          <Space>
            <Button
              icon={<FileImageOutlined />}
              onClick={() => handleExport('长图')}
            >
              导出长图
            </Button>
            <Button
              icon={<FilePdfOutlined />}
              onClick={() => handleExport('PDF')}
            >
              导出PDF
            </Button>
            <Button
              icon={<FileExcelOutlined />}
              onClick={() => handleExport('Excel')}
              type="primary"
            >
              导出Excel
            </Button>
          </Space>
        </Col>
      </Row>

      {/* Excel导出选择弹窗 */}
      <Modal
        title="导出Excel文件"
        open={excelModalVisible}
        onCancel={() => {
          setExcelModalVisible(false);
          setSelectedPage('');
        }}
        footer={null}
        width={500}
      >
        <div style={{ padding: '20px 0' }}>
          <div style={{ marginBottom: '20px' }}>
            <h4 style={{ marginBottom: '10px' }}>选择导出方式：</h4>

            {/* 导出所有页面 */}
            <div style={{
              border: '1px solid #d9d9d9',
              borderRadius: '8px',
              padding: '16px',
              marginBottom: '16px',
              backgroundColor: '#fafafa'
            }}>
              <div style={{ marginBottom: '12px' }}>
                <strong>导出所有页面</strong>
                <div style={{ color: '#666', fontSize: '12px', marginTop: '4px' }}>
                  包含：交易表现、用户表现、活动表现、RTB表现、供给表现
                </div>
              </div>
              <Button
                type="primary"
                icon={<DownloadOutlined />}
                onClick={handleExportAllPages}
                loading={batchExporting}
                disabled={batchExporting}
                block
              >
                {batchExporting ? '正在导出...' : '导出所有页面（ZIP压缩包）'}
              </Button>

              {/* 批量导出进度条 */}
              {batchExporting && (
                <div style={{ marginTop: '12px' }}>
                  <div style={{
                    fontSize: '12px',
                    color: '#1890ff',
                    marginBottom: '8px',
                    textAlign: 'center'
                  }}>
                    {exportMessage}
                  </div>
                  <Progress
                    percent={exportProgress}
                    status="active"
                    strokeColor={{
                      '0%': '#108ee9',
                      '100%': '#87d068',
                    }}
                    size="small"
                  />
                </div>
              )}
            </div>

            {/* 导出单个页面 */}
            <div style={{
              border: '1px solid #d9d9d9',
              borderRadius: '8px',
              padding: '16px'
            }}>
              <div style={{ marginBottom: '12px' }}>
                <strong>导出单个页面</strong>
              </div>
              <Select
                placeholder="请选择要导出的页面"
                style={{ width: '100%', marginBottom: '12px' }}
                value={selectedPage}
                onChange={setSelectedPage}
                options={pageOptions}
              />

              {/* AI洞察和图表截图提示 */}
              {selectedPage === '交易表现' && (
                <div style={{
                  backgroundColor: aiInsights ? '#f6ffed' : '#fff7e6',
                  border: `1px solid ${aiInsights ? '#b7eb8f' : '#ffd591'}`,
                  borderRadius: '4px',
                  padding: '8px',
                  marginBottom: '12px',
                  fontSize: '12px'
                }}>
                  {aiInsights ? (
                    <div>
                      <div style={{ color: '#52c41a', marginBottom: '4px' }}>
                        ✓ 将包含AI洞察内容到Excel中
                      </div>
                      <div style={{ color: '#1890ff' }}>
                        📊 将自动捕获页面图表并插入Excel中
                      </div>
                    </div>
                  ) : (
                    <div>
                      <div style={{ color: '#fa8c16', marginBottom: '4px' }}>
                        💡 提示：可在交易表现页面生成AI洞察后导出，Excel将包含AI洞察内容
                      </div>
                      <div style={{ color: '#1890ff' }}>
                        📊 导出时将自动捕获页面图表并插入Excel中
                      </div>
                    </div>
                  )}
                </div>
              )}

              <Button
                type="default"
                icon={<FileExcelOutlined />}
                onClick={handleExportSinglePage}
                loading={exporting}
                disabled={!selectedPage || batchExporting}
                block
              >
                导出选中页面
              </Button>

              {/* 单页面导出进度条 */}
              {exporting && singleExportTaskId && (
                <div style={{ marginTop: '12px' }}>
                  <div style={{
                    fontSize: '12px',
                    color: '#1890ff',
                    marginBottom: '8px',
                    textAlign: 'center'
                  }}>
                    {singleExportMessage}
                  </div>
                  <Progress
                    percent={singleExportProgress}
                    status="active"
                    strokeColor={{
                      '0%': '#108ee9',
                      '100%': '#87d068',
                    }}
                    size="small"
                  />
                </div>
              )}
            </div>
          </div>

          <div style={{
            fontSize: '12px',
            color: '#999',
            textAlign: 'center',
            marginTop: '16px'
          }}>
            文件将以"页面名称第{safeDayjs.isoWeek()}周(日期范围)周报.xlsx"格式命名
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default HeaderToolbar; 