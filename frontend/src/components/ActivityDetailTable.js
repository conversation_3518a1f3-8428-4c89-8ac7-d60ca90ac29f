import React from 'react';
import { Table, Select, Card, Skeleton } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';

const { Option } = Select;

const formatNumber = (num) => {
  // 统一使用千分位符格式化整数金额
  return Math.round(num).toLocaleString();
};

const formatPercent = (num) => {
  const percent = (num * 100).toFixed(2);
  return `${percent >= 0 ? '+' : ''}${percent}%`;
};

const formatPP = (num) => {
  const pp = (num * 100).toFixed(2);
  return `${pp >= 0 ? '+' : ''}${pp}pp`;
};

const ChangeCell = ({ value, isPercent = false }) => {
  const isPositive = value >= 0;
  const formatValue = isPercent ? formatPP(value) : formatPercent(value);
  
  return (
    <span className={isPositive ? 'change-positive' : 'change-negative'}>
      {isPositive ? (
        <ArrowUpOutlined style={{ marginRight: 4 }} />
      ) : (
        <ArrowDownOutlined style={{ marginRight: 4 }} />
      )}
      {formatValue}
    </span>
  );
};

const ActivityDetailTable = ({ data, selectedDimension, onDimensionChange, loading, onPageChange }) => {
  // 定义表格列
  const getColumns = (dimensionType) => {
    const baseColumns = [
      {
        title: dimensionType === '整体' ? '周次' : dimensionType,
        dataIndex: 'dimension',
        key: 'dimension',
        fixed: 'left',
        width: 120,
        sorter: (a, b) => a.dimension.localeCompare(b.dimension),
      },
      {
        title: '活动GMV',
        dataIndex: 'activity_gmv',
        key: 'activity_gmv',
        width: 120,
        sorter: (a, b) => a.activity_gmv - b.activity_gmv,
        render: (value) => `¥${formatNumber(value)}`,
      },
      {
        title: 'GMV年同比',
        dataIndex: 'gmv_yoy',
        key: 'gmv_yoy',
        width: 100,
        sorter: (a, b) => a.gmv_yoy - b.gmv_yoy,
        render: (value) => <ChangeCell value={value} />,
      }
    ];

    // 整体维度不显示GMV占比，其他维度显示
    if (dimensionType !== '整体') {
      baseColumns.push({
        title: 'GMV占比',
        dataIndex: 'gmv_ratio',
        key: 'gmv_ratio',
        width: 100,
        sorter: (a, b) => a.gmv_ratio - b.gmv_ratio,
        render: (value) => `${(value * 100).toFixed(2)}%`,
      });
    }

    // 活动机制不显示活动占比相关字段，整体维度显示活动占比
    if (dimensionType !== '活动机制') {
      baseColumns.push(
        {
          title: '活动占比',
          dataIndex: 'activity_ratio',
          key: 'activity_ratio',
          width: 100,
          sorter: (a, b) => a.activity_ratio - b.activity_ratio,
          render: (value) => value ? `${(value * 100).toFixed(2)}%` : '-',
        }
      );

      // 整体维度不显示活动占比同比
      if (dimensionType !== '整体') {
        baseColumns.push({
          title: '活动占比同比',
          dataIndex: 'activity_ratio_yoy',
          key: 'activity_ratio_yoy',
          width: 120,
          sorter: (a, b) => a.activity_ratio_yoy - b.activity_ratio_yoy,
          render: (value) => value ? <ChangeCell value={value} isPercent={true} /> : '-',
        });
      }
    }

    // 添加核销相关字段
    baseColumns.push(
      {
        title: '核销金额',
        dataIndex: 'verification_amount',
        key: 'verification_amount',
        width: 120,
        sorter: (a, b) => a.verification_amount - b.verification_amount,
        render: (value) => `¥${formatNumber(value)}`,
      },
      {
        title: '核销年同比',
        dataIndex: 'verification_yoy',
        key: 'verification_yoy',
        width: 100,
        sorter: (a, b) => a.verification_yoy - b.verification_yoy,
        render: (value) => <ChangeCell value={value} />,
      }
    );

    // 整体维度不显示核销占比
    if (dimensionType !== '整体') {
      baseColumns.push({
        title: '核销占比',
        dataIndex: 'verification_ratio',
        key: 'verification_ratio',
        width: 100,
        sorter: (a, b) => a.verification_ratio - b.verification_ratio,
        render: (value) => `${(value * 100).toFixed(2)}%`,
      });
    }

    baseColumns.push(
      {
        title: '活动费比',
        dataIndex: 'activity_cost_ratio',
        key: 'activity_cost_ratio',
        width: 100,
        sorter: (a, b) => a.activity_cost_ratio - b.activity_cost_ratio,
        render: (value) => `${(value * 100).toFixed(2)}%`,
      },
      {
        title: '活动费比同比',
        dataIndex: 'activity_cost_ratio_yoy',
        key: 'activity_cost_ratio_yoy',
        width: 120,
        sorter: (a, b) => a.activity_cost_ratio_yoy - b.activity_cost_ratio_yoy,
        render: (value) => <ChangeCell value={value} isPercent={true} />,
      }
    );

    // 活动机制不显示全量费比
    if (dimensionType !== '活动机制') {
      baseColumns.push(
        {
          title: '全量费比',
          dataIndex: 'total_cost_ratio',
          key: 'total_cost_ratio',
          width: 100,
          sorter: (a, b) => a.total_cost_ratio - b.total_cost_ratio,
          render: (value) => value ? `${(value * 100).toFixed(2)}%` : '-',
        },
        {
          title: '全量费比同比',
          dataIndex: 'total_cost_ratio_yoy',
          key: 'total_cost_ratio_yoy',
          width: 120,
          sorter: (a, b) => a.total_cost_ratio_yoy - b.total_cost_ratio_yoy,
          render: (value) => value ? <ChangeCell value={value} isPercent={true} /> : '-',
        }
      );
    }

    return baseColumns;
  };

  if (loading || !data) {
    return (
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <div className="section-title">活动详细数据</div>
        </div>
        <Skeleton active paragraph={{ rows: 8 }} />
      </Card>
    );
  }

  const { dimension_type, data: tableData } = data;

  return (
    <Card>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <div className="section-title">活动详细数据</div>
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <span>多维分析:</span>
          <Select
            value={selectedDimension}
            onChange={onDimensionChange}
            style={{ width: 120 }}
          >
            <Option value="整体">整体</Option>
            <Option value="城市">城市</Option>
            <Option value="商品">商品</Option>
            <Option value="零售商">零售商</Option>
            <Option value="活动机制">活动机制</Option>
          </Select>
        </div>
      </div>

      <Table
        columns={getColumns(dimension_type)}
        dataSource={tableData.map((item, index) => ({
          ...item,
          key: index
        }))}
        scroll={{ x: 'max-content', y: 400 }}
        size="small"
        pagination={{
          current: data?.page || 1,
          pageSize: data?.page_size || 20,
          total: data?.total || 0,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          onChange: (page, pageSize) => {
            if (onPageChange) {
              onPageChange(page, pageSize);
            }
          },
          onShowSizeChange: (current, size) => {
            if (onPageChange) {
              onPageChange(1, size); // 改变页面大小时回到第一页
            }
          }
        }}
        bordered
      />
    </Card>
  );
};

export default ActivityDetailTable; 