import React from 'react';
import { Row, Col, Card, Progress, Skeleton } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';

const formatNumber = (num) => {
  // 统一使用千分位符格式化整数金额
  return Math.round(num).toLocaleString();
};

const formatPercent = (num) => {
  const percent = (num * 100).toFixed(2);
  return `${percent >= 0 ? '+' : ''}${percent}%`;
};

const ChangeIndicator = ({ value, label }) => {
  const isPositive = value >= 0;
  // 红跌绿涨：正值显示绿色（上涨），负值显示红色（下跌）
  const colorClass = isPositive ? 'change-positive' : 'change-negative';

  return (
    <div className="change-item">
      {isPositive ? (
        <ArrowUpOutlined className={colorClass} />
      ) : (
        <ArrowDownOutlined className={colorClass} />
      )}
      <span className={colorClass}>
        {label}: {formatPercent(value)}
      </span>
    </div>
  );
};

const RTBSummaryCards = ({ data, loading, onCardClick, selectedCard }) => {
  if (loading || !data) {
    return (
      <div className="summary-cards">
        <Row gutter={[16, 16]}>
          {[1, 2, 3].map(i => (
            <Col xs={24} sm={12} lg={8} key={i}>
              <Card>
                <Skeleton active />
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    );
  }

  const { consumption, t1_guided_amount, roi } = data;

  // 卡片配置
  const cardConfigs = [
    {
      key: 'consumption',
      title: '消耗',
      value: `¥${formatNumber(consumption.amount)}`,
      wow: consumption.wow,
      progress: consumption.progress,
      hasProgress: true
    },
    {
      key: 't1_guided_amount',
      title: 'T+1引导成交金额',
      value: `¥${formatNumber(t1_guided_amount.amount)}`,
      wow: t1_guided_amount.wow,
      hasProgress: false
    },
    {
      key: 'roi',
      title: 'ROI',
      value: roi.value.toFixed(2),
      wow: roi.wow,
      hasProgress: false
    }
  ];

  return (
    <div>
      <h3 style={{ marginBottom: '16px', color: '#1890ff' }}>RTB投放概览</h3>
      <Row gutter={[16, 16]}>
        {cardConfigs.map((config) => (
          <Col xs={24} sm={12} lg={8} key={config.key}>
            <Card
              hoverable
              onClick={() => onCardClick && onCardClick(config.key)}
              style={{
                cursor: onCardClick ? 'pointer' : 'default',
                border: selectedCard === config.key ? '2px solid #1890ff' : '1px solid #d9d9d9',
                boxShadow: selectedCard === config.key ? '0 4px 12px rgba(24, 144, 255, 0.3)' : '0 2px 8px rgba(0,0,0,0.1)',
                width: '100%'
              }}
            >
              <div className="card-container">
                <div className="card-header">
                  {config.title}
                </div>
                <div className="metric-value">
                  {config.value}
                </div>
                <div className="metric-label">
                  {config.key === 'consumption' && '消耗金额'}
                  {config.key === 't1_guided_amount' && '引导成交金额'}
                  {config.key === 'roi' && '投资回报率'}
                </div>
                <div className="change-indicators">
                  <ChangeIndicator value={config.wow} label="周环比" />
                </div>
                {config.hasProgress && (
                  <div className="progress-container">
                    <div className="progress-text">
                      <span>消耗进度</span>
                      <span>{config.progress ? `${(config.progress * 100).toFixed(2)}%` : '0.00%'}</span>
                    </div>
                    <Progress
                      percent={config.progress ? config.progress * 100 : 0}
                      showInfo={false}
                      strokeColor={{
                        '0%': '#1890ff',
                        '100%': '#40a9ff',
                      }}
                    />
                  </div>
                )}
              </div>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default RTBSummaryCards; 