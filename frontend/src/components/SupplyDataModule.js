import React, { useState, useEffect } from 'react';
import { Space } from 'antd';
import PlatformFilter from './PlatformFilter';
import SupplySummaryCards from './SupplySummaryCards';
import SupplyInteractiveTrendChart from './SupplyInteractiveTrendChart';
import SupplyDetailTable from './SupplyDetailTable';
import { fetchSupplySummary, fetchSupplyTrends, fetchSupplyDetail } from '../services/api';

const SupplyDataModule = ({ selectedWeek, selectedBrand }) => {
  const [selectedPlatform, setSelectedPlatform] = useState('美团');
  const [selectedDimension, setSelectedDimension] = useState('整体');
  const [summaryData, setSummaryData] = useState(null);
  const [trendsData, setTrendsData] = useState(null);
  const [detailData, setDetailData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedCard, setSelectedCard] = useState('store_count'); // 默认选中第1个卡片（铺货店铺数）

  // 获取数据
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        const [summary, trends] = await Promise.all([
          fetchSupplySummary(selectedWeek, selectedBrand),
          fetchSupplyTrends(selectedBrand)
        ]);

        setSummaryData(summary);
        setTrendsData(trends);

        // 初始加载明细数据
        const detail = await fetchSupplyDetail(selectedDimension, selectedBrand, selectedWeek);
        setDetailData(detail);
      } catch (error) {
        console.error('Failed to load supply data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [selectedWeek, selectedBrand, selectedPlatform]); // 添加selectedPlatform依赖

  const handleDimensionChange = (dimension) => {
    // 只更新维度状态，不触发整个模块重新加载
    // 具体的数据刷新由SupplyDetailTable组件内部处理
    setSelectedDimension(dimension);
  };

  // 处理平台筛选变化
  const handlePlatformChange = (platform) => {
    setSelectedPlatform(platform);
  };

  // 处理卡片点击
  const handleCardClick = (cardKey) => {
    setSelectedCard(cardKey);
  };

  return (
    <div style={{ padding: '16px' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 平台筛选器 */}
        <div className="trading-section">
          <PlatformFilter
            selectedPlatform={selectedPlatform}
            onPlatformChange={handlePlatformChange}
            singlePlatform={true}
          />
        </div>

        {/* 概览卡片 */}
        <div className="trading-section">
          <SupplySummaryCards
            data={summaryData}
            loading={loading}
            onCardClick={handleCardClick}
            selectedCard={selectedCard}
          />
        </div>

        {/* 供给趋势分析 - 联动图表 */}
        <div className="trading-section">
          <SupplyInteractiveTrendChart
            data={trendsData}
            loading={loading}
            selectedMetric={selectedCard}
          />
        </div>

        {/* 供给明细数据表格 */}
        <div className="trading-section">
          <SupplyDetailTable
            data={detailData}
            selectedDimension={selectedDimension}
            onDimensionChange={handleDimensionChange}
            loading={loading}
            selectedWeek={selectedWeek}
            selectedBrand={selectedBrand}
          />
        </div>
      </Space>
    </div>
  );
};

export default SupplyDataModule; 