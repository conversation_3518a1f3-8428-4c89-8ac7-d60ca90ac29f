import React, { useState, useEffect } from 'react';
import { Space } from 'antd';
import PlatformFilter from './PlatformFilter';
import AIInsights from './AIInsights';
import SummaryCards from './SummaryCards';
import ChartsSection from './ChartsSection';
import DimensionAnalysisWithDetail from './DimensionAnalysisWithDetail';
import Top10Tables from './Top10Tables';
import { fetchTradingSummary, fetchTradingTrends, fetchDimensionAnalysis, fetchDimensionTrends, fetchTop10Data } from '../services/api';

const TradingDataModule = ({ selectedWeek, brand = '', onAiInsightsChange }) => {
  const [selectedPlatform, setSelectedPlatform] = useState('全平台');
  const [selectedDimension, setSelectedDimension] = useState('子品牌');
  const [summaryData, setSummaryData] = useState(null);
  const [trendsData, setTrendsData] = useState(null);
  const [dimensionData, setDimensionData] = useState(null);
  const [dimensionTrendsData, setDimensionTrendsData] = useState(null);
  const [top10Data, setTop10Data] = useState(null);
  const [loading, setLoading] = useState(true);
  const [dimensionLoading, setDimensionLoading] = useState(true);
  const [detailTableLoading, setDetailTableLoading] = useState(true);

  // 获取主要数据（不包括维度数据）
  useEffect(() => {
    const loadMainData = async () => {
      setLoading(true);
      try {
        const [summary, trends, top10] = await Promise.all([
          fetchTradingSummary(selectedWeek, selectedPlatform, brand),
          fetchTradingTrends(selectedPlatform, brand),
          fetchTop10Data('商品', brand, selectedPlatform)
        ]);

        setSummaryData(summary);
        setTrendsData(trends);
        setTop10Data(top10);
      } catch (error) {
        console.error('Failed to load main data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadMainData();
  }, [selectedWeek, selectedPlatform, brand]);

  // 单独获取维度数据
  useEffect(() => {
    const loadDimensionData = async () => {
      setDimensionLoading(true);
      try {
        const dimension = await fetchDimensionAnalysis(selectedDimension, brand, selectedPlatform);
        setDimensionData(dimension);
      } catch (error) {
        console.error('Failed to load dimension data:', error);
      } finally {
        setDimensionLoading(false);
      }
    };

    loadDimensionData();
  }, [selectedDimension, brand, selectedPlatform]);

  // 单独获取DetailTable的趋势数据
  useEffect(() => {
    const loadDetailTableData = async () => {
      setDetailTableLoading(true);
      try {
        const dimensionTrends = await fetchDimensionTrends(selectedDimension, brand, selectedWeek, selectedPlatform);
        setDimensionTrendsData(dimensionTrends);
      } catch (error) {
        console.error('Failed to load dimension trends data:', error);
      } finally {
        setDetailTableLoading(false);
      }
    };

    loadDetailTableData();
  }, [selectedDimension, brand, selectedWeek, selectedPlatform]);

  const handlePlatformChange = (platform) => {
    setSelectedPlatform(platform);
  };

  const handleDimensionChange = (dimension) => {
    setSelectedDimension(dimension);
  };

  return (
    <div style={{ padding: '16px' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 平台筛选器 */}
        <div className="trading-section">
          <PlatformFilter
            selectedPlatform={selectedPlatform}
            onPlatformChange={handlePlatformChange}
          />
        </div>

        {/* AI洞察分析 */}
        <div className="trading-section">
          <AIInsights
            selectedWeek={selectedWeek}
            brand={brand}
            selectedPlatform={selectedPlatform}
            onInsightsChange={onAiInsightsChange}
          />
        </div>

        {/* 概览卡片 */}
        <div className="trading-section">
          <SummaryCards
            data={summaryData}
            loading={loading}
          />
        </div>

        {/* 图表区域 */}
        <div className="trading-section">
          <ChartsSection 
            data={trendsData}
            selectedPlatform={selectedPlatform}
            loading={loading}
          />
        </div>

        {/* 多维表现与明细数据 */}
        <div className="trading-section">
          <DimensionAnalysisWithDetail
            dimensionData={dimensionData}
            dimensionTrendsData={dimensionTrendsData}
            selectedDimension={selectedDimension}
            onDimensionChange={handleDimensionChange}
            dimensionLoading={dimensionLoading}
            detailTableLoading={detailTableLoading}
          />
        </div>

        {/* Top10表格 */}
        <div className="trading-section">
          <Top10Tables
            data={top10Data}
            selectedWeek={selectedWeek}
            loading={loading}
            brand={brand}
            platform={selectedPlatform}
          />
        </div>
      </Space>
    </div>
  );
};

export default TradingDataModule; 