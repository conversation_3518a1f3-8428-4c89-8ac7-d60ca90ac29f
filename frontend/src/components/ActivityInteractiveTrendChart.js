import React, { useRef, useEffect, useState } from 'react';
import { Card, Skeleton } from 'antd';
import * as echarts from 'echarts';

const ActivityInteractiveTrendChart = ({ data, loading, selectedMetric }) => {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);
  const [forceUpdate, setForceUpdate] = useState(0);

  // 添加组件级别的调试信息
  console.log('ActivityInteractiveTrendChart - 组件渲染:', {
    hasData: !!data,
    loading,
    selectedMetric,
    dataKeys: data ? Object.keys(data) : [],
    trendDataLength: data && data.trend_data ? data.trend_data.length : 0,
    forceUpdate
  });

  useEffect(() => {
    console.log('ActivityInteractiveTrendChart - useEffect 触发:', {
      hasChartRef: !!chartRef.current,
      hasChartInstance: !!chartInstance.current,
      loading,
      hasData: !!data,
      selectedMetric
    });

    if (!chartRef.current) {
      console.log('ActivityInteractiveTrendChart - chartRef.current 为空，退出');
      return;
    }

    if (loading) {
      console.log('ActivityInteractiveTrendChart - 正在加载中');
      return;
    }

    if (!data || !data.trend_data || !selectedMetric) {
      console.log('ActivityInteractiveTrendChart - 数据不完整:', {
        hasData: !!data,
        hasTrendData: !!(data && data.trend_data),
        selectedMetric
      });
      return;
    }

    // 强制清理现有实例 - 关键修复点
    if (chartInstance.current) {
      console.log('ActivityInteractiveTrendChart - 清理现有图表实例');
      try {
        chartInstance.current.dispose();
        chartInstance.current = null;
      } catch (error) {
        console.error('清理实例时出错:', error);
      }
    }

    // 检查DOM上的现有实例 - 关键修复点
    const existingInstance = echarts.getInstanceByDom(chartRef.current);
    if (existingInstance) {
      console.log('ActivityInteractiveTrendChart - 清理DOM上的现有实例');
      try {
        existingInstance.dispose();
      } catch (error) {
        console.error('清理DOM实例时出错:', error);
      }
    }

    // 确保DOM元素已经渲染并有尺寸
    const rect = chartRef.current.getBoundingClientRect();
    console.log('ActivityInteractiveTrendChart - DOM元素尺寸:', {
      width: rect.width,
      height: rect.height
    });

    // 如果元素没有尺寸，延迟初始化
    if (rect.width === 0 || rect.height === 0) {
      console.log('ActivityInteractiveTrendChart - DOM元素尺寸为0，延迟初始化');
      setTimeout(() => {
        if (chartRef.current && !chartInstance.current) {
          console.log('ActivityInteractiveTrendChart - 延迟重试初始化');
          setForceUpdate(prev => prev + 1);
        }
      }, 100);
      return;
    }

    try {
      console.log('ActivityInteractiveTrendChart - 开始初始化ECharts');
      chartInstance.current = echarts.init(chartRef.current);
      console.log('ActivityInteractiveTrendChart - ECharts 实例初始化成功');
    } catch (error) {
      console.error('ActivityInteractiveTrendChart - ECharts 初始化失败:', error);
      return;
    }

    // 数据已在useEffect开头检查过，这里直接处理数据

    const trendData = data.trend_data;
    console.log('ActivityInteractiveTrendChart - 开始处理数据:', {
      trendDataLength: trendData.length,
      selectedMetric,
      firstItem: trendData[0]
    });

    const weeks = trendData.map(item => `第${item.week}周`);

    // 根据selectedMetric动态选择数据和配置
    const getMetricConfig = (metric) => {
      const configs = {
        'activity_gmv': {
          title: '活动GMV趋势分析',
          dataKey: 'activity_gmv',
          yoyKey: 'activity_gmv_yoy',
          seriesName: '活动GMV',
          yoySeriesName: '活动GMV年同比',
          yAxisName: '活动GMV (元)',
          unit: '元',
          isPercentage: false
        },
        'activity_gmv_ratio': {
          title: '活动GMV占比趋势分析',
          dataKey: 'activity_gmv_ratio',
          yoyKey: 'activity_gmv_ratio_yoy',
          seriesName: '活动GMV占比',
          yoySeriesName: '活动GMV占比年同比',
          yAxisName: '活动GMV占比 (%)',
          unit: '%',
          isPercentage: true
        },
        'verification_amount': {
          title: '核销金额趋势分析',
          dataKey: 'verification_amount',
          yoyKey: 'verification_amount_yoy',
          seriesName: '核销金额',
          yoySeriesName: '核销金额年同比',
          yAxisName: '核销金额 (元)',
          unit: '元',
          isPercentage: false
        },
        'activity_cost_ratio': {
          title: '活动费比趋势分析',
          dataKey: 'activity_cost_ratio',
          yoyKey: 'activity_cost_ratio_yoy',
          seriesName: '活动费比',
          yoySeriesName: '活动费比年同比',
          yAxisName: '活动费比 (%)',
          unit: '%',
          isPercentage: true
        },
        'total_cost_ratio': {
          title: '全量费比趋势分析',
          dataKey: 'total_cost_ratio',
          yoyKey: 'total_cost_ratio_yoy',
          seriesName: '全量费比',
          yoySeriesName: '全量费比年同比',
          yAxisName: '全量费比 (%)',
          unit: '%',
          isPercentage: true
        }
      };
      return configs[metric] || configs['activity_gmv']; // 默认使用活动GMV
    };

    const config = getMetricConfig(selectedMetric);

    // 根据配置处理数据
    const mainData = trendData.map(item => {
      const value = item[config.dataKey] || 0;
      return config.isPercentage ? (value * 100).toFixed(2) : Math.round(value);
    });

    // 处理年同比数据（转换为百分比）
    const yoyData = trendData.map(item => (item[config.yoyKey] * 100).toFixed(2));

    console.log('ActivityInteractiveTrendChart - 数据处理完成:', {
      weeks: weeks.slice(0, 3),
      mainData: mainData.slice(0, 3),
      yoyData: yoyData.slice(0, 3),
      config: config
    });

    // 完整的图表配置（包含柱状图和折线图）
    const option = {
      title: {
        text: config.title,
        left: 'left',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params) {
          let result = `${params[0].name}<br/>`;
          params.forEach(param => {
            const color = param.color;
            let value = param.value;
            let unit = '';

            if (param.seriesName.includes('年同比')) {
              unit = '%';
            } else {
              unit = config.unit;
              if (!config.isPercentage) {
                value = Math.round(value).toLocaleString();
              } else {
                value = parseFloat(value).toFixed(2);
              }
            }

            result += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
            result += `${param.seriesName}: ${value}${unit}<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: [config.seriesName, config.yoySeriesName],
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: true,
        data: weeks,
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: [
        {
          type: 'value',
          name: config.yAxisName,
          position: 'left',
          axisLabel: {
            formatter: function(value) {
              if (config.isPercentage) {
                return value + '%';
              } else {
                return Math.round(value).toLocaleString();
              }
            }
          }
        },
        {
          type: 'value',
          name: '年同比 (%)',
          position: 'right',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      series: [
        {
          name: config.seriesName,
          type: 'bar',
          data: mainData,
          itemStyle: {
            color: '#1890ff',
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: '#1890ff33'
            }
          },
          yAxisIndex: 0,
          barWidth: '50%'
        },
        {
          name: config.yoySeriesName,
          type: 'line',
          yAxisIndex: 1,
          data: yoyData,
          itemStyle: {
            color: '#f5222d'
          },
          lineStyle: {
            width: 3,
            shadowBlur: 5,
            shadowColor: 'rgba(245, 34, 45, 0.3)'
          },
          symbol: 'circle',
          symbolSize: 6,
          smooth: true
        }
      ]
    };

    console.log('ActivityInteractiveTrendChart - 设置图表配置');
    chartInstance.current.setOption(option, true);
    console.log('ActivityInteractiveTrendChart - 图表渲染完成!');

    // 响应式处理
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [data, loading, selectedMetric, forceUpdate]);

  // 添加一个effect来强制重新渲染当数据变化时
  useEffect(() => {
    if (data && data.trend_data && selectedMetric && !loading) {
      console.log('ActivityInteractiveTrendChart - 数据变化，强制重新渲染');
      setForceUpdate(prev => prev + 1);
    }
  }, [data, selectedMetric, loading]);

  useEffect(() => {
    return () => {
      console.log('ActivityInteractiveTrendChart - 组件卸载，清理ECharts实例');
      if (chartInstance.current) {
        try {
          chartInstance.current.dispose();
          chartInstance.current = null;
          console.log('ActivityInteractiveTrendChart - ECharts实例已清理');
        } catch (error) {
          console.error('ActivityInteractiveTrendChart - 清理ECharts实例时出错:', error);
        }
      }

      // 额外检查：清理可能残留在DOM上的实例
      if (chartRef.current) {
        const existingInstance = echarts.getInstanceByDom(chartRef.current);
        if (existingInstance) {
          console.log('ActivityInteractiveTrendChart - 清理DOM上的残留实例');
          try {
            existingInstance.dispose();
          } catch (error) {
            console.error('ActivityInteractiveTrendChart - 清理残留实例时出错:', error);
          }
        }
      }
    };
  }, []);

  if (loading) {
    return (
      <Card>
        <Skeleton active paragraph={{ rows: 8 }} />
      </Card>
    );
  }

  // 添加数据检查，确保有有效数据才渲染图表
  if (!data || !data.trend_data || !selectedMetric) {
    console.log('ActivityInteractiveTrendChart - 数据不完整，显示占位符:', {
      hasData: !!data,
      hasTrendData: !!(data && data.trend_data),
      selectedMetric
    });
    return (
      <Card>
        <div
          style={{
            width: '100%',
            height: '400px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#999',
            fontSize: '16px'
          }}
        >
          {!selectedMetric ? '请选择指标' : '暂无数据'}
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <div
        ref={chartRef}
        style={{
          width: '100%',
          height: '400px',
          minHeight: '400px'
        }}
      />
    </Card>
  );
};

export default ActivityInteractiveTrendChart;
