import React from 'react';
import { Card, Select, Skeleton } from 'antd';
import ReactECharts from 'echarts-for-react';

const { Option } = Select;

const dimensions = [
  { label: '子品牌', value: '子品牌' },
  { label: '品线', value: '品线' },
  { label: '渠道类型', value: '渠道类型' },
  { label: '大区', value: '大区' }
];

const formatNumber = (num) => {
  // 统一使用千分位符格式化整数金额
  return Math.round(num).toLocaleString();
};

const DimensionAnalysis = ({ data, selectedDimension, onDimensionChange, loading }) => {
  if (loading || !data) {
    return (
      <div className="dimension-section">
        <Card>
          <div className="dimension-header">
            <div className="section-title">多维表现</div>
            <Select
              value={selectedDimension}
              onChange={onDimensionChange}
              style={{ width: 120 }}
              placeholder="选择维度"
            >
              {dimensions.map(dim => (
                <Option key={dim.value} value={dim.value}>
                  {dim.label}
                </Option>
              ))}
            </Select>
          </div>
          <Skeleton active />
        </Card>
      </div>
    );
  }

  // 生成颜色数组
  const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2', '#eb2f96', '#fa8c16'];

  // 准备图表数据 - 柱状图显示最后一个完整周的GMV分布
  const chartData = data.data || [];
  const xAxisData = chartData.map(item => item.name);
  const seriesData = chartData.map((item, index) => ({
    value: item.value,
    itemStyle: {
      color: colors[index % colors.length],
      borderRadius: [4, 4, 0, 0]
    }
  }));

  const option = {
    title: {
      text: `${selectedDimension}维度GMV分布（第${data.week || '26'}周）`,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 600,
        color: '#333'
      }
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e6e6e6',
      borderWidth: 1,
      borderRadius: 8,
      textStyle: {
        color: '#333'
      },
      formatter: function(params) {
        return `${params.name}<br/>GMV: ¥${formatNumber(params.value)}`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      axisLabel: {
        color: '#666',
        fontSize: 12,
        interval: 0,
        rotate: xAxisData.length > 6 ? 45 : 0
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      name: 'GMV',
      nameTextStyle: {
        color: '#666',
        fontSize: 12
      },
      axisLabel: {
        formatter: function(value) {
          return formatNumber(value);
        },
        color: '#666',
        fontSize: 11
      },
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    series: [{
      name: 'GMV',
      type: 'bar',
      data: seriesData,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        }
      },
      label: {
        show: true,
        position: 'top',
        formatter: function(params) {
          return formatNumber(params.value);
        },
        fontSize: 11,
        color: '#666'
      }
    }]
  };

  return (
    <div className="dimension-section">
      <Card>
        <div className="dimension-header">
          <div className="section-title">多维表现</div>
          <Select
            value={selectedDimension}
            onChange={onDimensionChange}
            style={{ width: 120 }}
            placeholder="选择维度"
          >
            {dimensions.map(dim => (
              <Option key={dim.value} value={dim.value}>
                {dim.label}
              </Option>
            ))}
          </Select>
        </div>
        <div data-chart-type={`${selectedDimension.replace('渠道类型', 'channel').replace('子品牌', 'subbrand').replace('品线', 'productline').replace('大区', 'region')}_chart`}>
          <ReactECharts
            option={option}
            style={{ height: '400px' }}
            notMerge={true}
          />
        </div>
      </Card>
    </div>
  );
};

export default DimensionAnalysis; 