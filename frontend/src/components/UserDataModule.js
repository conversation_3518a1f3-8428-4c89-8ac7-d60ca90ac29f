import React, { useState, useEffect } from 'react';
import { message, Space } from 'antd';
import PlatformFilter from './PlatformFilter';
import UserSummaryCards from './UserSummaryCards';
import UserTrendChart from './UserTrendChart';
import UserDetailTable from './UserDetailTable';
import { buildApiUrl, API_ENDPOINTS } from '../config/api';

const UserDataModule = ({ selectedWeek, brand }) => {
  const [selectedPlatform, setSelectedPlatform] = useState('美团');
  const [summaryData, setSummaryData] = useState(null);
  const [trendData, setTrendData] = useState(null);
  const [detailData, setDetailData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSummaryData();
    fetchTrendData();
    fetchDetailData();
  }, [selectedWeek, brand, selectedPlatform]);

  const fetchSummaryData = async () => {
    try {
      setLoading(true);
      const week = selectedWeek || 45;
      const params = { week };
      if (brand) params.brand = brand;
      // 用户表现页面只有美团平台，不需要添加platform参数

      const url = buildApiUrl(API_ENDPOINTS.USER.SUMMARY, params);
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      const data = await response.json();
      setSummaryData(data);
    } catch (error) {
      console.error('Error fetching user summary data:', error);
      message.error('获取用户数据失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchTrendData = async () => {
    try {
      const params = {};
      if (brand) params.brand = brand;

      const url = buildApiUrl(API_ENDPOINTS.USER.TRENDS, params);
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      const data = await response.json();
      setTrendData(data);
    } catch (error) {
      console.error('Error fetching user trend data:', error);
      message.error('获取用户趋势数据失败');
    }
  };

  const fetchDetailData = async () => {
    try {
      const params = {};
      if (brand) params.brand = brand;

      const url = buildApiUrl(API_ENDPOINTS.USER.DETAIL, params);
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      const data = await response.json();
      setDetailData(data);
    } catch (error) {
      console.error('Error fetching user detail data:', error);
      message.error('获取用户明细数据失败');
    }
  };

  // 处理平台筛选变化
  const handlePlatformChange = (platform) => {
    setSelectedPlatform(platform);
  };

  return (
    <div style={{ padding: '16px' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 平台筛选器 */}
        <div className="trading-section">
          <PlatformFilter
            selectedPlatform={selectedPlatform}
            onPlatformChange={handlePlatformChange}
            singlePlatform={true}
          />
        </div>

        {/* 用户数据内容 */}
        <div className="trading-section">
          <UserSummaryCards data={summaryData} loading={loading} />
        </div>
        <div className="trading-section">
          <UserTrendChart data={trendData} loading={loading} />
        </div>
        <div className="trading-section">
          <UserDetailTable data={detailData} loading={loading} />
        </div>
      </Space>
    </div>
  );
};

export default UserDataModule; 