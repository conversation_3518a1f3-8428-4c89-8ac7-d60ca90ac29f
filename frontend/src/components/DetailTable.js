import React from 'react';
import { Card, Table, Skeleton } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';

const formatNumber = (num) => {
  // 统一使用千分位符格式化整数金额
  return Math.round(num).toLocaleString();
};

const formatPercent = (num) => {
  const percent = (num * 100).toFixed(2);
  const isPositive = num >= 0;
  return (
    <span style={{ color: isPositive ? '#52c41a' : '#ff4d4f' }}>
      {isPositive ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
      {percent >= 0 ? '+' : ''}{percent}%
    </span>
  );
};

const DetailTable = ({ data, selectedDimension, loading }) => {
  if (loading) {
    return (
      <div className="table-container">
        <Card title="明细数据详情">
          <Skeleton active />
        </Card>
      </div>
    );
  }

  if (!data || !data.data || !Array.isArray(data.data) || data.data.length === 0) {
    return (
      <div className="table-container">
        <Card title="明细数据详情">
          <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
            暂无数据
          </div>
        </Card>
      </div>
    );
  }

  // 准备表格数据 - 按8周展示
  const weeks = data.data[0]?.trend || [];
  const tableData = weeks.map((weekData, weekIndex) => {
    const rowData = {
      key: weekIndex,
      week: `第${weekData.week}周`
    };

    // 计算该周所有维度的GMV总和（用于计算占比）
    const weekTotalGmv = data.data.reduce((sum, dimensionItem) => {
      return sum + (dimensionItem.trend[weekIndex]?.gmv || 0);
    }, 0);

    // 为每个维度添加数据
    data.data.forEach(dimensionItem => {
      const weekGmv = dimensionItem.trend[weekIndex]?.gmv || 0;
      const percentage = weekTotalGmv > 0 ? (weekGmv / weekTotalGmv * 100).toFixed(2) : 0;
      const yoy = dimensionItem.trend[weekIndex]?.yoy || 0;
      const wow = dimensionItem.trend[weekIndex]?.wow || 0;

      rowData[`${dimensionItem.dimension}_gmv`] = weekGmv;
      rowData[`${dimensionItem.dimension}_percentage`] = percentage;
      rowData[`${dimensionItem.dimension}_yoy`] = yoy;
      rowData[`${dimensionItem.dimension}_wow`] = wow;
    });

    return rowData;
  });

  // 构建动态列配置
  const columns = [
    {
      title: selectedDimension === '整体' ? '周次' : '时间',
      dataIndex: 'week',
      key: 'week',
      fixed: 'left',
      width: 100
    }
  ];

  // 为每个维度添加列组
  data.data.forEach(dimensionItem => {
    const dimensionKey = dimensionItem.dimension;
    columns.push({
      title: dimensionKey,
      children: [
        {
          title: 'GMV',
          dataIndex: `${dimensionKey}_gmv`,
          key: `${dimensionKey}_gmv`,
          width: 120,
          sorter: (a, b) => a[`${dimensionKey}_gmv`] - b[`${dimensionKey}_gmv`],
          render: (value) => `¥${formatNumber(value)}`
        },
        {
          title: 'GMV占比',
          dataIndex: `${dimensionKey}_percentage`,
          key: `${dimensionKey}_percentage`,
          width: 100,
          sorter: (a, b) => a[`${dimensionKey}_percentage`] - b[`${dimensionKey}_percentage`],
          render: (value) => `${value}%`
        },
        {
          title: '年同比',
          dataIndex: `${dimensionKey}_yoy`,
          key: `${dimensionKey}_yoy`,
          width: 100,
          sorter: (a, b) => a[`${dimensionKey}_yoy`] - b[`${dimensionKey}_yoy`],
          render: (value) => formatPercent(value)
        },
        {
          title: '周环比',
          dataIndex: `${dimensionKey}_wow`,
          key: `${dimensionKey}_wow`,
          width: 100,
          sorter: (a, b) => a[`${dimensionKey}_wow`] - b[`${dimensionKey}_wow`],
          render: (value) => formatPercent(value)
        }
      ]
    });
  });

  return (
    <div className="table-container">
      <Card title={`明细数据详情 - ${selectedDimension}维度`}>
        <Table
          columns={columns}
          dataSource={tableData}
          scroll={{ x: 'max-content' }}
          pagination={false}
          size="small"
          bordered
        />
      </Card>
    </div>
  );
};

export default DetailTable; 