import React from 'react';
import { Row, Col, Card, Progress, Skeleton } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';

const formatNumber = (num) => {
  // 使用千分位符格式化数字，显示整数（无小数）
  return Math.round(num).toLocaleString('zh-CN');
};

const formatPercent = (num) => {
  const percent = (num * 100).toFixed(2);
  return `${percent >= 0 ? '+' : ''}${percent}%`;
};

const ChangeIndicator = ({ value, label }) => {
  const isPositive = value >= 0;
  return (
    <div className="change-item">
      {isPositive ? (
        <ArrowUpOutlined className="change-positive" />
      ) : (
        <ArrowDownOutlined className="change-negative" />
      )}
      <span className={isPositive ? 'change-positive' : 'change-negative'}>
        {label}: {formatPercent(value)}
      </span>
    </div>
  );
};

const SummaryCards = ({ data, loading }) => {
  if (loading || !data) {
    return (
      <div className="summary-cards">
        <Row gutter={[16, 16]} style={{ height: '100%' }}>
          {[1, 2, 3].map(i => (
            <Col xs={24} sm={8} key={i} style={{ display: 'flex' }}>
              <Card style={{ width: '100%', minHeight: '200px' }}>
                <Skeleton active />
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    );
  }

  const { week_data, mtd_data, ytd_data } = data;

  return (
    <div className="summary-cards">
      <Row gutter={[16, 16]} style={{ height: '100%' }}>
        {/* Week GMV */}
        <Col xs={24} sm={8} style={{ display: 'flex' }}>
          <Card style={{ width: '100%' }}>
            <div className="card-container">
              <div className="card-header">
                Week {week_data.week}
              </div>
              <div className="metric-value">
                ¥{formatNumber(week_data.gmv)}
              </div>
              <div className="metric-label">
                GMV总和
              </div>
              <div className="change-indicators">
                <ChangeIndicator value={week_data.yoy} label="年同比" />
                <ChangeIndicator value={week_data.wow} label="周环比" />
              </div>
            </div>
          </Card>
        </Col>

        {/* MTD GMV */}
        <Col xs={24} sm={8} style={{ display: 'flex' }}>
          <Card style={{ width: '100%' }}>
            <div className="card-container">
              <div className="card-header">
                GMV MTD
              </div>
              <div className="metric-value">
                ¥{formatNumber(mtd_data.current)}
              </div>
              <div className="metric-label">
                当前月度达成总额
              </div>
              <div className="change-indicators">
                <ChangeIndicator value={mtd_data.yoy} label="年同比" />
                <ChangeIndicator value={mtd_data.mom} label="月环比" />
              </div>
              <div className="progress-container">
                <div className="progress-text">
                  <span>MTD达成率</span>
                  <span>{formatPercent(mtd_data.rate)}</span>
                </div>
                <Progress
                  percent={mtd_data.rate * 100}
                  showInfo={false}
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />
              </div>
            </div>
          </Card>
        </Col>

        {/* YTD GMV */}
        <Col xs={24} sm={8} style={{ display: 'flex' }}>
          <Card style={{ width: '100%' }}>
            <div className="card-container">
              <div className="card-header">
                GMV YTD
              </div>
              <div className="metric-value">
                ¥{formatNumber(ytd_data.current)}
              </div>
              <div className="metric-label">
                当前年度已达成总额
              </div>
              <div className="change-indicators">
                <ChangeIndicator value={ytd_data.yoy} label="年同比" />
              </div>
              <div className="progress-container">
                <div className="progress-text">
                  <span>YTD达成率</span>
                  <span>{formatPercent(ytd_data.rate)}</span>
                </div>
                <Progress
                  percent={ytd_data.rate * 100}
                  showInfo={false}
                  strokeColor={{
                    '0%': '#722ed1',
                    '100%': '#eb2f96',
                  }}
                />
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SummaryCards; 