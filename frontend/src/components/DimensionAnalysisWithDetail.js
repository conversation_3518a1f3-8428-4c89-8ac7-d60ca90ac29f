import React from 'react';
import { Card, Select, Skeleton, Table } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';

const { Option } = Select;

const dimensions = [
  { label: '子品牌', value: '子品牌' },
  { label: '品线', value: '品线' },
  { label: '渠道类型', value: '渠道类型' },
  { label: '大区', value: '大区' }
];

const formatNumber = (num) => {
  // 统一使用千分位符格式化整数金额
  if (num == null) return num;
  return Math.round(num).toLocaleString();
};

const formatPercent = (num) => {
  const percent = (num * 100).toFixed(2);
  const isPositive = num >= 0;
  return (
    <span style={{ color: isPositive ? '#52c41a' : '#ff4d4f' }}>
      {isPositive ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
      {percent >= 0 ? '+' : ''}{percent}%
    </span>
  );
};

const DimensionAnalysisWithDetail = ({ 
  dimensionData, 
  dimensionTrendsData, 
  selectedDimension, 
  onDimensionChange, 
  dimensionLoading, 
  detailTableLoading 
}) => {
  // 生成颜色数组
  const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2', '#eb2f96', '#fa8c16'];

  // 图表数据
  const chartData = dimensionData?.data || [];
  const chartDimensionList = chartData.map(item => item.dimension || item.name); // 统一取名为dimension
  const xAxisData = chartDimensionList;
  const seriesData = chartData.map((item, index) => ({
    value: item.value,
    itemStyle: {
      color: colors[index % colors.length],
      borderRadius: [4, 4, 0, 0]
    }
  }));

  const chartOption = {
    title: {
      text: `${selectedDimension}维度GMV分布（第${dimensionData?.week || '26'}周）`,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 600,
        color: '#333'
      }
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e6e6e6',
      borderWidth: 1,
      borderRadius: 8,
      textStyle: {
        color: '#333'
      },
      formatter: function(params) {
        return `${params.name}<br/>GMV: ¥${formatNumber(params.value)}`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      axisLabel: {
        color: '#666',
        fontSize: 12,
        interval: 0,
        rotate: xAxisData.length > 6 ? 45 : 0
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      name: 'GMV',
      nameTextStyle: {
        color: '#666',
        fontSize: 12
      },
      axisLabel: {
        formatter: function(value) {
          return formatNumber(value);
        },
        color: '#666',
        fontSize: 11
      },
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    series: [{
      name: 'GMV',
      type: 'bar',
      data: seriesData,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        }
      },
      label: {
        show: true,
        position: 'top',
        formatter: function(params) {
          return formatNumber(params.value);
        },
        fontSize: 11,
        color: '#666'
      }
    }]
  };

  // 明细表格
  const renderDetailTable = () => {
    if (detailTableLoading) {
      return <Skeleton active style={{ marginTop: 32 }} />;
    }

    if (!dimensionTrendsData || !dimensionTrendsData.data || !Array.isArray(dimensionTrendsData.data)) {
      return (
        <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
          暂无数据
        </div>
      );
    }

    // 以图表维度为基准，补全表格维度
    // 1. 先构建一个map，key为dimension，value为trend数组
    const trendMap = {};
    dimensionTrendsData.data.forEach(item => {
      const key = item.dimension;
      trendMap[key] = item.trend;
    });

    // 2. 以trendMap和chartDimensionList为基准，补全trend
    // 假设每个trend数组长度一致（按周）
    const weeks = (trendMap[chartDimensionList[0]] || []).map((_, idx) => idx);
    // 如果没有数据，直接返回空表
    if (weeks.length === 0) {
      return (
        <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
          暂无数据
        </div>
      );
    }

    // 3. 组装表格数据
    const tableData = weeks.map((weekIdx) => {
      // 取某一周的week编号（假设所有trend的week字段一致）
      const weekNo = trendMap[chartDimensionList[0]]?.[weekIdx]?.week || weekIdx + 1;
      const rowData = {
        key: weekIdx,
        week: `第${weekNo}周`
      };
      // 计算该周所有维度的GMV总和（用于占比）
      let weekTotalGmv = 0;
      chartDimensionList.forEach(dim => {
        weekTotalGmv += trendMap[dim]?.[weekIdx]?.gmv || 0;
      });
      // 补全每个维度的数据
      chartDimensionList.forEach(dim => {
        const trend = trendMap[dim]?.[weekIdx];
        rowData[`${dim}_gmv`] = trend ? trend.gmv : 0;
        rowData[`${dim}_percentage`] = weekTotalGmv > 0 ? ((trend ? trend.gmv : 0) / weekTotalGmv * 100).toFixed(2) : 0;
        rowData[`${dim}_yoy`] = trend && trend.yoy !== undefined ? trend.yoy : null;
        rowData[`${dim}_wow`] = trend && trend.wow !== undefined ? trend.wow : null;
      });
      return rowData;
    });

    // 4. 构建动态列配置
    const columns = [
      {
        title: selectedDimension === '整体' ? '周次' : '时间',
        dataIndex: 'week',
        key: 'week',
        fixed: 'left',
        width: 100
      }
    ];
    chartDimensionList.forEach(dim => {
      columns.push({
        title: dim,
        children: [
          {
            title: 'GMV',
            dataIndex: `${dim}_gmv`,
            key: `${dim}_gmv`,
            width: 120,
            sorter: (a, b) => a[`${dim}_gmv`] - b[`${dim}_gmv`],
            render: (value) => `¥${formatNumber(value)}`
          },
          {
            title: 'GMV占比',
            dataIndex: `${dim}_percentage`,
            key: `${dim}_percentage`,
            width: 100,
            sorter: (a, b) => a[`${dim}_percentage`] - b[`${dim}_percentage`],
            render: (value) => `${value}%`
          },
          {
            title: '年同比',
            dataIndex: `${dim}_yoy`,
            key: `${dim}_yoy`,
            width: 100,
            sorter: (a, b) => (a[`${dim}_yoy`] || 0) - (b[`${dim}_yoy`] || 0),
            render: (value) => value !== null && value !== undefined ? formatPercent(value) : '-'
          },
          {
            title: '周环比',
            dataIndex: `${dim}_wow`,
            key: `${dim}_wow`,
            width: 100,
            sorter: (a, b) => (a[`${dim}_wow`] || 0) - (b[`${dim}_wow`] || 0),
            render: (value) => value !== null && value !== undefined ? formatPercent(value) : '-'
          }
        ]
      });
    });
    return (
      <Table
        columns={columns}
        dataSource={tableData}
        scroll={{ x: 'max-content' }}
        pagination={false}
        size="small"
        bordered
        style={{ marginTop: 32 }}
      />
    );
  };

  return (
    <div className="dimension-section">
      <Card>
        <div className="dimension-header">
          <div className="section-title">多维表现与明细数据</div>
          <Select
            value={selectedDimension}
            onChange={onDimensionChange}
            style={{ width: 120 }}
            placeholder="选择维度"
          >
            {dimensions.map(dim => (
              <Option key={dim.value} value={dim.value}>
                {dim.label}
              </Option>
            ))}
          </Select>
        </div>
        {/* 上图 */}
        {dimensionLoading ? (
          <Skeleton active style={{ marginBottom: 32 }} />
        ) : (
          <ReactECharts 
            option={chartOption}
            style={{ height: '400px', marginBottom: 32 }}
            notMerge={true}
          />
        )}
        {/* 下表 */}
        {renderDetailTable()}
      </Card>
    </div>
  );
};

export default DimensionAnalysisWithDetail; 