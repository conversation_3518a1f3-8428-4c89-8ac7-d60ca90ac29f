import React from 'react';
import { Card, Table, Spin } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';

const UserDetailTable = ({ data, loading }) => {
  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!data || !data.data) return null;

  const formatValue = (value, type, isArpu = false) => {
    if (type === 'percentage') {
      return `${(value * 100).toFixed(2)}%`;
    } else if (type === 'yoy_percentage') {
      // 年同比：如果值为0，显示为"--"（表示无历史数据）
      if (value === 0) {
        return '--';
      }
      return `${(value * 100).toFixed(2)}%`;
    } else if (type === 'currency') {
      if (isArpu) {
        // 客单价保留1位小数
        return `¥${value.toFixed(1)}`;
      } else {
        // 其他金额保留整数，添加千分位分隔符
        return `¥${Math.round(value).toLocaleString()}`;
      }
    } else if (type === 'number') {
      return Math.round(value).toLocaleString();
    }
    return value;
  };

  const getWowIcon = (wow) => {
    if (wow > 0) {
      return <ArrowUpOutlined style={{ color: '#52c41a' }} />;
    } else if (wow < 0) {
      return <ArrowDownOutlined style={{ color: '#ff4d4f' }} />;
    }
    return null;
  };

  const getWowColor = (wow) => {
    if (wow > 0) return '#52c41a';
    if (wow < 0) return '#ff4d4f';
    return '#666';
  };

  // 通用的列样式，防止换行
  const columnStyle = {
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis'
  };

  const columns = [
    {
      title: '周次',
      dataIndex: 'week',
      key: 'week',
      width: 80,
      fixed: 'left',
      render: (value) => <span style={columnStyle}>第{value}周</span>
    },
    {
      title: '新客数量',
      dataIndex: 'new_user_count',
      key: 'new_user_count',
      width: 100,
      render: (value) => <span style={columnStyle}>{formatValue(value, 'number')}</span>
    },
    {
      title: '新客占比',
      dataIndex: 'new_user_ratio',
      key: 'new_user_ratio',
      width: 100,
      render: (value) => <span style={columnStyle}>{formatValue(value, 'percentage')}</span>
    },
    {
      title: '新客年同比',
      dataIndex: 'new_user_yoy',
      key: 'new_user_yoy',
      width: 110,
      render: (value) => (
        <span style={{ ...columnStyle, color: value === 0 ? '#999' : getWowColor(value) }}>
          {value !== 0 && getWowIcon(value)}
          {formatValue(value, 'yoy_percentage')}
        </span>
      )
    },
    {
      title: '新客周环比',
      dataIndex: 'new_user_wow',
      key: 'new_user_wow',
      width: 110,
      render: (value) => (
        <span style={{ ...columnStyle, color: getWowColor(value) }}>
          {getWowIcon(value)}
          {formatValue(value, 'percentage')}
        </span>
      )
    },
    {
      title: '新客销售额',
      dataIndex: 'new_user_gmv',
      key: 'new_user_gmv',
      width: 120,
      render: (value) => <span style={columnStyle}>{formatValue(value, 'currency')}</span>
    },
    {
      title: '新客销售额占比',
      dataIndex: 'new_user_gmv_ratio',
      key: 'new_user_gmv_ratio',
      width: 130,
      render: (value) => formatValue(value, 'percentage')
    },
    {
      title: '新客销售额年同比',
      dataIndex: 'new_user_gmv_yoy',
      key: 'new_user_gmv_yoy',
      width: 140,
      render: (value) => (
        <span style={{ ...columnStyle, color: value === 0 ? '#999' : getWowColor(value) }}>
          {value !== 0 && getWowIcon(value)}
          {formatValue(value, 'yoy_percentage')}
        </span>
      )
    },
    {
      title: '新客销售额周环比',
      dataIndex: 'new_user_gmv_wow',
      key: 'new_user_gmv_wow',
      width: 140,
      render: (value) => (
        <span style={{ color: getWowColor(value) }}>
          {getWowIcon(value)}
          {formatValue(value, 'percentage')}
        </span>
      )
    },
    {
      title: '新客客单价',
      dataIndex: 'new_user_arpu',
      key: 'new_user_arpu',
      width: 120,
      render: (value) => formatValue(value, 'currency', true)
    },
    {
      title: '新客客单价年同比',
      dataIndex: 'new_user_arpu_yoy',
      key: 'new_user_arpu_yoy',
      width: 140,
      render: (value) => (
        <span style={{ ...columnStyle, color: value === 0 ? '#999' : getWowColor(value) }}>
          {value !== 0 && getWowIcon(value)}
          {formatValue(value, 'yoy_percentage')}
        </span>
      )
    },
    {
      title: '新客客单价周环比',
      dataIndex: 'new_user_arpu_wow',
      key: 'new_user_arpu_wow',
      width: 140,
      render: (value) => (
        <span style={{ color: getWowColor(value) }}>
          {getWowIcon(value)}
          {formatValue(value, 'percentage')}
        </span>
      )
    },
    {
      title: '老客数量',
      dataIndex: 'old_user_count',
      key: 'old_user_count',
      width: 100,
      render: (value) => formatValue(value, 'number')
    },
    {
      title: '老客占比',
      dataIndex: 'old_user_ratio',
      key: 'old_user_ratio',
      width: 100,
      render: (value) => formatValue(value, 'percentage')
    },
    {
      title: '老客年同比',
      dataIndex: 'old_user_yoy',
      key: 'old_user_yoy',
      width: 110,
      render: (value) => (
        <span style={{ ...columnStyle, color: value === 0 ? '#999' : getWowColor(value) }}>
          {value !== 0 && getWowIcon(value)}
          {formatValue(value, 'yoy_percentage')}
        </span>
      )
    },
    {
      title: '老客周环比',
      dataIndex: 'old_user_wow',
      key: 'old_user_wow',
      width: 110,
      render: (value) => (
        <span style={{ color: getWowColor(value) }}>
          {getWowIcon(value)}
          {formatValue(value, 'percentage')}
        </span>
      )
    },
    {
      title: '老客销售额',
      dataIndex: 'old_user_gmv',
      key: 'old_user_gmv',
      width: 120,
      render: (value) => formatValue(value, 'currency')
    },
    {
      title: '老客销售额占比',
      dataIndex: 'old_user_gmv_ratio',
      key: 'old_user_gmv_ratio',
      width: 130,
      render: (value) => formatValue(value, 'percentage')
    },
    {
      title: '老客销售额年同比',
      dataIndex: 'old_user_gmv_yoy',
      key: 'old_user_gmv_yoy',
      width: 140,
      render: (value) => (
        <span style={{ ...columnStyle, color: value === 0 ? '#999' : getWowColor(value) }}>
          {value !== 0 && getWowIcon(value)}
          {formatValue(value, 'yoy_percentage')}
        </span>
      )
    },
    {
      title: '老客销售额周环比',
      dataIndex: 'old_user_gmv_wow',
      key: 'old_user_gmv_wow',
      width: 140,
      render: (value) => (
        <span style={{ color: getWowColor(value) }}>
          {getWowIcon(value)}
          {formatValue(value, 'percentage')}
        </span>
      )
    },
    {
      title: '老客客单价',
      dataIndex: 'old_user_arpu',
      key: 'old_user_arpu',
      width: 120,
      render: (value) => formatValue(value, 'currency', true)
    },
    {
      title: '老客客单价年同比',
      dataIndex: 'old_user_arpu_yoy',
      key: 'old_user_arpu_yoy',
      width: 140,
      render: (value) => (
        <span style={{ ...columnStyle, color: value === 0 ? '#999' : getWowColor(value) }}>
          {value !== 0 && getWowIcon(value)}
          {formatValue(value, 'yoy_percentage')}
        </span>
      )
    },
    {
      title: '老客客单价周环比',
      dataIndex: 'old_user_arpu_wow',
      key: 'old_user_arpu_wow',
      width: 140,
      render: (value) => (
        <span style={{ color: getWowColor(value) }}>
          {getWowIcon(value)}
          {formatValue(value, 'percentage')}
        </span>
      )
    },
    {
      title: '总用户数',
      dataIndex: 'total_user_count',
      key: 'total_user_count',
      width: 100,
      render: (value) => formatValue(value, 'number')
    },
    {
      title: '总销售额',
      dataIndex: 'total_gmv',
      key: 'total_gmv',
      width: 120,
      render: (value) => formatValue(value, 'currency')
    }
  ];

  return (
    <Card
      title="用户明细数据"
      style={{
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        marginTop: '16px'
      }}
    >
      <Table
        columns={columns}
        dataSource={data.data}
        rowKey="week"
        scroll={{ x: 2800 }}
        pagination={{
          pageSize: 8,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
        }}
        size="small"
        bordered
        style={{
          whiteSpace: 'nowrap'
        }}
        components={{
          body: {
            cell: (props) => (
              <td {...props} style={{ ...props.style, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }} />
            )
          }
        }}
      />
    </Card>
  );
};

export default UserDetailTable; 