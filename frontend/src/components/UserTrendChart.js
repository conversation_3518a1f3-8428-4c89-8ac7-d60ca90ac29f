import React from 'react';
import { Card, Spin, Row, Col } from 'antd';
import ReactECharts from 'echarts-for-react';

const UserTrendChart = ({ data, loading }) => {
  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!data) return null;

  const getDistributionChartOption = () => {
    const weeks = data.distribution.map(item => `第${item.week}周`);
    const newUserData = data.distribution.map(item => item.new_user_count);
    const oldUserData = data.distribution.map(item => item.old_user_count);

    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function(params) {
          let result = `${params[0].name}<br/>`;
          params.forEach(param => {
            const color = param.color;
            const value = param.value.toLocaleString();
            result += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
            result += `${param.seriesName}: ${value}人<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: ['新客数量', '老客数量'],
        top: 10
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: weeks,
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        name: '用户数量（人）',
        axisLabel: {
          formatter: '{value}'
        }
      },
      series: [
        {
          name: '新客数量',
          type: 'bar',
          data: newUserData,
          itemStyle: {
            color: '#52c41a',
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(82, 196, 26, 0.3)'
            }
          },
          barWidth: '30%'
        },
        {
          name: '老客数量',
          type: 'bar',
          data: oldUserData,
          itemStyle: {
            color: '#1890ff',
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(24, 144, 255, 0.3)'
            }
          },
          barWidth: '30%'
        }
      ]
    };
  };

  const getSalesChartOption = () => {
    const weeks = data.sales.map(item => `第${item.week}周`);
    const newUserGmv = data.sales.map(item => item.new_user_gmv);
    const oldUserGmv = data.sales.map(item => item.old_user_gmv);
    const newUserArpu = data.sales.map(item => item.new_user_arpu);
    const oldUserArpu = data.sales.map(item => item.old_user_arpu);

    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params) {
          let result = `${params[0].name}<br/>`;
          params.forEach(param => {
            const color = param.color;
            let value = param.value;
            let unit = '';
            
            if (param.seriesName.includes('销售额')) {
              value = `¥${value.toLocaleString()}`;
              unit = '';
            } else if (param.seriesName.includes('客单价')) {
              value = `¥${value.toFixed(1)}`;
              unit = '';
            }
            
            result += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
            result += `${param.seriesName}: ${value}${unit}<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: ['新客销售额', '老客销售额', '新客客单价', '老客客单价'],
        top: 10
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: weeks,
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '销售额（元）',
          position: 'left',
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          type: 'value',
          name: '客单价（元）',
          position: 'right',
          axisLabel: {
            formatter: function(value) {
              return value.toFixed(1);
            }
          }
        }
      ],
      series: [
        {
          name: '新客销售额',
          type: 'bar',
          data: newUserGmv,
          itemStyle: {
            color: '#52c41a',
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(82, 196, 26, 0.3)'
            }
          },
          yAxisIndex: 0,
          barWidth: '20%'
        },
        {
          name: '老客销售额',
          type: 'bar',
          data: oldUserGmv,
          itemStyle: {
            color: '#1890ff',
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(24, 144, 255, 0.3)'
            }
          },
          yAxisIndex: 0,
          barWidth: '20%'
        },
        {
          name: '新客客单价',
          type: 'line',
          yAxisIndex: 1,
          data: newUserArpu,
          itemStyle: {
            color: '#fa8c16'
          },
          lineStyle: {
            width: 3,
            shadowBlur: 5,
            shadowColor: 'rgba(250, 140, 22, 0.3)'
          },
          symbol: 'circle',
          symbolSize: 6,
          smooth: true
        },
        {
          name: '老客客单价',
          type: 'line',
          yAxisIndex: 1,
          data: oldUserArpu,
          itemStyle: {
            color: '#722ed1'
          },
          lineStyle: {
            width: 3,
            shadowBlur: 5,
            shadowColor: 'rgba(114, 46, 209, 0.3)'
          },
          symbol: 'circle',
          symbolSize: 6,
          smooth: true
        }
      ]
    };
  };

  return (
    <div style={{ marginTop: '16px' }}>
      <h3 style={{ marginBottom: '16px', color: '#1890ff' }}>用户趋势分析</h3>

      {/* 第一排：新老客分布趋势 */}
      <div style={{ marginBottom: '16px' }}>
        <Card
          title="新老客分布趋势（近8周）"
          style={{
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
          }}
        >
          <ReactECharts
            option={getDistributionChartOption()}
            style={{ height: '400px' }}
            opts={{ renderer: 'canvas' }}
          />
        </Card>
      </div>

      {/* 第二排：新老客销售趋势 */}
      <div>
        <Card
          title="新老客销售趋势（近8周）"
          style={{
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
          }}
        >
          <ReactECharts
            option={getSalesChartOption()}
            style={{ height: '400px' }}
            opts={{ renderer: 'canvas' }}
          />
        </Card>
      </div>
    </div>
  );
};

export default UserTrendChart; 