import React from 'react';
import { Radio } from 'antd';

const platforms = [
  { label: '全平台', value: '全平台' },
  { label: '美团', value: '美团' },
  { label: '饿了么', value: '饿了么' },
  { label: '京东到家', value: '京东到家' },
  { label: '多点', value: '多点' },
  { label: '淘鲜达', value: '淘鲜达' }
];

const singlePlatforms = [
  { label: '美团', value: '美团' }
];

const PlatformFilter = ({ selectedPlatform, onPlatformChange, singlePlatform = false }) => {
  const platformOptions = singlePlatform ? singlePlatforms : platforms;

  return (
    <div className="platform-filters">
      <div className="section-title" style={{ marginBottom: 16 }}>
        平台筛选
      </div>
      <Radio.Group
        value={selectedPlatform}
        onChange={(e) => onPlatformChange(e.target.value)}
        buttonStyle="solid"
        size="large"
        className={`platform-radio-group ${singlePlatform ? 'single-platform' : ''}`}
      >
        {platformOptions.map(platform => (
          <Radio.Button
            key={platform.value}
            value={platform.value}
            className="platform-radio-button"
          >
            {platform.label}
          </Radio.Button>
        ))}
      </Radio.Group>
    </div>
  );
};

export default PlatformFilter;