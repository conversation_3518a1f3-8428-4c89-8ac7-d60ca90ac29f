import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Table, Skeleton } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined, TrophyOutlined } from '@ant-design/icons';
import { fetchTop10Data } from '../services/api';

const formatNumber = (num) => {
  // 统一使用千分位符格式化整数金额
  return Math.round(num).toLocaleString();
};

const formatPercent = (num) => {
  const percent = (num * 100).toFixed(2);
  const isPositive = num >= 0;
  return (
    <span style={{ color: isPositive ? '#52c41a' : '#ff4d4f' }}>
      {isPositive ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
      {percent >= 0 ? '+' : ''}{percent}%
    </span>
  );
};

const getRankIcon = (rank) => {
  if (rank <= 3) {
    const colors = ['#ffd700', '#c0c0c0', '#cd7f32']; // 金银铜
    return <TrophyOutlined style={{ color: colors[rank - 1], marginRight: 4 }} />;
  }
  return <span style={{ marginRight: 8, color: '#8c8c8c' }}>{rank}</span>;
};

const TopTable = ({ title, category, selectedWeek, loading, brand = '', platform = '全平台' }) => {
  const [data, setData] = useState([]);
  const [tableLoading, setTableLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      setTableLoading(true);
      try {
        const result = await fetchTop10Data(category, brand, platform);
        setData(result.data || []);
      } catch (error) {
        console.error(`Failed to load ${category} data:`, error);
      } finally {
        setTableLoading(false);
      }
    };

    loadData();
  }, [category, selectedWeek, brand, platform]);

  const columns = [
    {
      title: '排名',
      dataIndex: 'rank',
      key: 'rank',
      width: 50,
      align: 'center',
      render: (rank) => getRankIcon(rank)
    },
    {
      title: title === 'Top10商品' ? '商品名称' : title === 'Top10城市' ? '城市名称' : '零售商名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      ellipsis: true
    },
    {
      title: 'GMV',
      dataIndex: 'gmv',
      key: 'gmv',
      width: 120,
      render: (value) => `¥${formatNumber(value)}`,
      sorter: (a, b) => a.gmv - b.gmv
    },
    {
      title: '年同比',
      dataIndex: 'yoy',
      key: 'yoy',
      width: 100,
      render: (value) => formatPercent(value),
      sorter: (a, b) => a.yoy - b.yoy
    },
    {
      title: '周环比',
      dataIndex: 'wow',
      key: 'wow',
      width: 100,
      render: (value) => formatPercent(value),
      sorter: (a, b) => a.wow - b.wow
    }
  ];

  return (
    <Card 
      title={
        <span>
          <TrophyOutlined style={{ marginRight: 8, color: '#faad14' }} />
          {title}
        </span>
      }
      size="small"
    >
      {loading || tableLoading ? (
        <Skeleton active />
      ) : (
        <Table
          columns={columns}
          dataSource={data}
          pagination={false}
          size="small"
          rowKey="rank"
          scroll={{ x: 570 }}
        />
      )}
    </Card>
  );
};

const Top10Tables = ({ selectedWeek, loading, brand = '', platform = '全平台' }) => {
  return (
    <div className="top10-section">
      <div className="section-title" style={{ marginBottom: 16 }}>
        第{selectedWeek}周 TOP10排行榜
      </div>
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={8}>
          <TopTable
            title="Top10商品"
            category="商品"
            selectedWeek={selectedWeek}
            loading={loading}
            brand={brand}
            platform={platform}
          />
        </Col>
        <Col xs={24} lg={8}>
          <TopTable
            title="Top10城市"
            category="城市"
            selectedWeek={selectedWeek}
            loading={loading}
            brand={brand}
            platform={platform}
          />
        </Col>
        <Col xs={24} lg={8}>
          <TopTable
            title="Top10零售商"
            category="零售商"
            selectedWeek={selectedWeek}
            loading={loading}
            brand={brand}
            platform={platform}
          />
        </Col>
      </Row>
    </div>
  );
};

export default Top10Tables; 