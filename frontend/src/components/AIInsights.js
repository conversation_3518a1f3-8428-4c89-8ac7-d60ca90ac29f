import React, { useState, useRef } from 'react';
import { Button, Card, Input, message, Spin, Progress } from 'antd';
import { BulbOutlined, EditOutlined, SaveOutlined } from '@ant-design/icons';
import { generateAIInsights, saveAIInsights, getInsightsProgress } from '../services/api';

const { TextArea } = Input;

// 简单的Markdown渲染组件
const MarkdownRenderer = ({ content }) => {
  const renderContent = (text) => {
    // 处理标题
    let html = text
      .replace(/^### (.*$)/gim, '<h3 style="color: #262626; font-size: 16px; font-weight: 600; margin: 16px 0 8px 0; border-bottom: 1px solid #e8e8e8; padding-bottom: 4px;">$1</h3>')
      .replace(/^## (.*$)/gim, '<h2 style="color: #262626; font-size: 18px; font-weight: 600; margin: 20px 0 12px 0; border-bottom: 2px solid #1890ff; padding-bottom: 6px;">$1</h2>')
      .replace(/^# (.*$)/gim, '<h1 style="color: #262626; font-size: 20px; font-weight: 600; margin: 24px 0 16px 0;">$1</h1>');

    // 处理列表
    html = html
      .replace(/^\* (.*$)/gim, '<li style="margin: 4px 0; padding-left: 8px;">$1</li>')
      .replace(/^- (.*$)/gim, '<li style="margin: 4px 0; padding-left: 8px;">$1</li>');

    // 包装连续的li标签
    html = html.replace(/(<li[^>]*>.*?<\/li>\s*)+/gs, '<ul style="margin: 8px 0; padding-left: 20px;">$&</ul>');

    // 处理百分比数字的样式（智能绿涨红跌）
    html = html.replace(/([^%]*?)([+-]?\d+\.?\d*%)/g, (match, context, percentage) => {
      const value = parseFloat(percentage.replace('%', ''));
      const contextLower = context.toLowerCase();

      let color = '#666';
      let backgroundColor = 'transparent';

      // 判断是否为占比指标（不需要红绿色）
      const isRatioIndicator = contextLower.includes('占比') ||
                              contextLower.includes('渗透率') ||
                              contextLower.includes('活跃度') ||
                              contextLower.includes('转化率') ||
                              contextLower.includes('完成率') ||
                              contextLower.includes('覆盖率');

      if (!isRatioIndicator) {
        // 判断上下文是否表示下跌/负面
        const isNegativeContext = contextLower.includes('下跌') ||
                                 contextLower.includes('下降') ||
                                 contextLower.includes('减少') ||
                                 contextLower.includes('降低') ||
                                 contextLower.includes('缺货') ||
                                 contextLower.includes('成本上升') ||
                                 contextLower.includes('费比');

        // 判断上下文是否表示上涨/正面
        const isPositiveContext = contextLower.includes('增长') ||
                                 contextLower.includes('上涨') ||
                                 contextLower.includes('提升') ||
                                 contextLower.includes('改善') ||
                                 contextLower.includes('优化');

        if (isNegativeContext || value < 0) {
          color = '#ff4d4f';
          backgroundColor = '#fff2f0';
        } else if (isPositiveContext || value > 0) {
          color = '#52c41a';
          backgroundColor = '#f6ffed';
        }
      }

      return context + `<span style="color: ${color}; background-color: ${backgroundColor}; padding: 2px 4px; border-radius: 3px; font-weight: bold;">${percentage}</span>`;
    });

    // 处理粗体
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong style="font-weight: 600;">$1</strong>');

    // 处理换行
    html = html.replace(/\n/g, '<br>');

    return html;
  };

  return (
    <div
      dangerouslySetInnerHTML={{ __html: renderContent(content) }}
      style={{
        wordBreak: 'break-word',
        lineHeight: '1.6'
      }}
    />
  );
};

const AIInsights = ({ selectedWeek, brand, selectedPlatform = '全平台', onInsightsChange }) => {
  const [loading, setLoading] = useState(false);
  const [insights, setInsights] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [editedInsights, setEditedInsights] = useState('');
  const [progress, setProgress] = useState(0);
  const [progressMessage, setProgressMessage] = useState('');
  const [taskId, setTaskId] = useState(null);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const progressIntervalRef = useRef(null);

  const handleGenerateInsights = async () => {
    setLoading(true);
    setProgress(0);
    setProgressMessage('正在启动AI洞察生成...');

    try {
      // 启动AI洞察生成任务
      const response = await generateAIInsights(selectedWeek, brand, selectedPlatform);
      if (response.success && response.task_id) {
        setTaskId(response.task_id);
        // 开始轮询进度
        startProgressPolling(response.task_id);
      } else {
        message.error('AI洞察生成失败：' + response.error);
        setLoading(false);
      }
    } catch (error) {
      console.error('Error generating AI insights:', error);
      message.error('AI洞察生成失败，请稍后重试');
      setLoading(false);
    }
  };

  const startProgressPolling = (taskId) => {
    // 清除之前的轮询
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
    }

    // 开始新的轮询
    progressIntervalRef.current = setInterval(async () => {
      try {
        const progressResponse = await getInsightsProgress(taskId);
        if (progressResponse.success) {
          setProgress(progressResponse.progress);
          setProgressMessage(progressResponse.message);

          if (progressResponse.status === 'completed' && progressResponse.result) {
            // 任务完成
            clearInterval(progressIntervalRef.current);
            const newInsights = progressResponse.result.insights;
            setInsights(newInsights);
            setEditedInsights(newInsights);
            setLoading(false);
            setProgress(100);
            setProgressMessage('AI洞察生成完成！');
            message.success('AI洞察生成成功！');

            // 向上传递洞察内容
            if (onInsightsChange) {
              onInsightsChange(newInsights);
            }
          } else if (progressResponse.status === 'error') {
            // 任务失败
            clearInterval(progressIntervalRef.current);
            setLoading(false);
            setProgress(0);
            setProgressMessage('');
            message.error('AI洞察生成失败：' + progressResponse.error);
          }
        }
      } catch (error) {
        console.error('Error polling progress:', error);
        clearInterval(progressIntervalRef.current);
        setLoading(false);
        setProgress(0);
        setProgressMessage('');
        message.error('获取进度失败，请稍后重试');
      }
    }, 1000); // 每秒轮询一次
  };

  // 组件卸载时清理轮询
  React.useEffect(() => {
    return () => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, []);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = async () => {
    try {
      const response = await saveAIInsights(editedInsights, selectedWeek, brand);
      if (response.success) {
        setInsights(editedInsights);
        setIsEditing(false);
        message.success('洞察内容保存成功！');

        // 向上传递更新后的洞察内容
        if (onInsightsChange) {
          onInsightsChange(editedInsights);
        }
      } else {
        message.error('保存失败：' + response.error);
      }
    } catch (error) {
      console.error('Error saving AI insights:', error);
      message.error('保存失败，请稍后重试');
    }
  };

  const handleCancel = () => {
    setEditedInsights(insights);
    setIsEditing(false);
  };

  return (
    <Card 
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <BulbOutlined style={{ color: '#1890ff' }} />
          <span>AI洞察分析</span>
        </div>
      }
      style={{ marginBottom: '16px' }}
      extra={
        <div style={{ display: 'flex', gap: '8px' }}>
          {!insights && (
            <Button 
              type="primary" 
              icon={<BulbOutlined />}
              onClick={handleGenerateInsights}
              loading={loading}
            >
              生成AI洞察
            </Button>
          )}
          {insights && !isEditing && (
            <>
              <Button
                icon={isCollapsed ? <span>📖</span> : <span>📕</span>}
                onClick={() => setIsCollapsed(!isCollapsed)}
              >
                {isCollapsed ? '展开' : '收起'}
              </Button>
              <Button
                icon={<EditOutlined />}
                onClick={handleEdit}
              >
                编辑
              </Button>
              <Button
                type="primary"
                icon={<BulbOutlined />}
                onClick={handleGenerateInsights}
                loading={loading}
              >
                重新生成
              </Button>
            </>
          )}
          {isEditing && (
            <>
              <Button onClick={handleCancel}>
                取消
              </Button>
              <Button 
                type="primary" 
                icon={<SaveOutlined />}
                onClick={handleSave}
              >
                保存
              </Button>
            </>
          )}
        </div>
      }
    >
      <Spin spinning={false}>
        {!insights && !loading && (
          <div style={{
            textAlign: 'center',
            padding: '40px 0',
            color: '#999',
            fontSize: '14px'
          }}>
            点击"生成AI洞察"按钮，对选定自然周下的所有5个页面数据进行智能分析
          </div>
        )}

        {loading && (
          <div style={{ padding: '20px' }}>
            <div style={{
              textAlign: 'center',
              marginBottom: '16px',
              color: '#1890ff',
              fontSize: '14px'
            }}>
              {progressMessage}
            </div>
            <Progress
              percent={progress}
              status="active"
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
              style={{ marginBottom: '16px' }}
            />
            <div style={{
              textAlign: 'center',
              fontSize: '12px',
              color: '#666'
            }}>
              正在处理中，请稍候...
            </div>
          </div>
        )}

        {insights && !isEditing && (
          <div style={{
            minHeight: isCollapsed ? '60px' : '200px',
            maxHeight: isCollapsed ? '60px' : 'none',
            padding: '16px',
            backgroundColor: '#fafafa',
            borderRadius: '6px',
            border: '1px solid #f0f0f0',
            fontSize: '14px',
            lineHeight: '1.6',
            overflow: isCollapsed ? 'hidden' : 'auto',
            position: 'relative'
          }}>
            <MarkdownRenderer content={insights} />
            {isCollapsed && (
              <div style={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                height: '20px',
                background: 'linear-gradient(transparent, #fafafa)',
                pointerEvents: 'none'
              }} />
            )}
          </div>
        )}

        {isEditing && (
          <TextArea
            value={editedInsights}
            onChange={(e) => setEditedInsights(e.target.value)}
            placeholder="请输入洞察内容..."
            autoSize={{ minRows: 10, maxRows: 30 }}
            style={{ fontSize: '14px', lineHeight: '1.6' }}
          />
        )}
      </Spin>
    </Card>
  );
};

export default AIInsights;
