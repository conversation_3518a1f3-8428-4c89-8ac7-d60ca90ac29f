import React, { useRef, useEffect } from 'react';
import { Card, Skeleton } from 'antd';
import * as echarts from 'echarts';

const RTBTrendChart = ({ data, loading }) => {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  useEffect(() => {
    if (!chartRef.current) return;

    // 初始化图表
    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
    }

    if (loading || !data || !data.trend_data) {
      chartInstance.current.showLoading('default', {
        text: '加载中...',
        color: '#1890ff',
        textColor: '#000',
        maskColor: 'rgba(255, 255, 255, 0.8)',
        zlevel: 0
      });
      return;
    }

    chartInstance.current.hideLoading();

    const trendData = data.trend_data;
    const weeks = trendData.map(item => `第${item.week}周`);

    const formatNumber = (num) => {
      // 统一使用千分位符格式化整数金额
      return Math.round(num).toLocaleString();
    };

    const option = {
      title: {
        text: 'RTB趋势分析',
        left: 'left',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params) {
          let tooltipText = `<div style="padding: 8px;">${params[0].axisValue}<br/>`;
          params.forEach((param, index) => {
            const seriesName = param.seriesName;
            let value = param.value;
            
            if (seriesName.includes('消耗') || seriesName.includes('引导金额')) {
              value = '¥' + formatNumber(value);
            } else if (seriesName.includes('ROI')) {
              value = value.toFixed(2);
            } else if (seriesName.includes('环比')) {
              value = (value * 100).toFixed(2) + '%';
            }
            
            tooltipText += `
              <div style="display: flex; align-items: center; margin: 4px 0;">
                <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
                <span style="flex: 1;">${seriesName}: </span>
                <span style="font-weight: bold;">${value}</span>
              </div>
            `;
          });
          tooltipText += '</div>';
          return tooltipText;
        }
      },
      legend: {
        top: 35,
        data: ['消耗', 'T+1引导金额', 'ROI', '消耗周环比', '引导金额周环比', 'ROI周环比']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '80px',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: weeks,
        axisLabel: {
          fontSize: 12
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '金额/ROI',
          position: 'left',
          axisLabel: {
            formatter: function(value) {
              if (value >= 10000) {
                return formatNumber(value);
              }
              return value.toFixed(2);
            }
          }
        },
        {
          type: 'value',
          name: '周环比(%)',
          position: 'right',
          axisLabel: {
            formatter: function(value) {
              return (value * 100).toFixed(0) + '%';
            }
          }
        }
      ],
      series: [
        {
          name: '消耗',
          type: 'bar',
          yAxisIndex: 0,
          data: trendData.map(item => item.consumption),
          itemStyle: {
            color: '#1890ff',
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(24, 144, 255, 0.3)'
            }
          },
          barWidth: '15%'
        },
        {
          name: 'T+1引导金额',
          type: 'bar',
          yAxisIndex: 0,
          data: trendData.map(item => item.t1_guided_amount),
          itemStyle: {
            color: '#52c41a',
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(82, 196, 26, 0.3)'
            }
          },
          barWidth: '15%'
        },
        {
          name: 'ROI',
          type: 'bar',
          yAxisIndex: 0,
          data: trendData.map(item => item.roi),
          itemStyle: {
            color: '#fa8c16',
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(250, 140, 22, 0.3)'
            }
          },
          barWidth: '15%'
        },
        {
          name: '消耗周环比',
          type: 'line',
          yAxisIndex: 1,
          data: trendData.map(item => item.consumption_wow),
          itemStyle: {
            color: '#722ed1'
          },
          lineStyle: {
            width: 3,
            shadowBlur: 5,
            shadowColor: 'rgba(114, 46, 209, 0.3)'
          },
          symbol: 'circle',
          symbolSize: 6,
          smooth: true
        },
        {
          name: '引导金额周环比',
          type: 'line',
          yAxisIndex: 1,
          data: trendData.map(item => item.t1_guided_amount_wow),
          itemStyle: {
            color: '#eb2f96'
          },
          lineStyle: {
            width: 3,
            shadowBlur: 5,
            shadowColor: 'rgba(235, 47, 150, 0.3)'
          },
          symbol: 'circle',
          symbolSize: 6,
          smooth: true
        },
        {
          name: 'ROI周环比',
          type: 'line',
          yAxisIndex: 1,
          data: trendData.map(item => item.roi_wow),
          itemStyle: {
            color: '#f5222d'
          },
          lineStyle: {
            width: 3,
            shadowBlur: 5,
            shadowColor: 'rgba(245, 34, 45, 0.3)'
          },
          symbol: 'circle',
          symbolSize: 6,
          smooth: true
        }
      ]
    };

    chartInstance.current.setOption(option);

    // 响应式处理
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [data, loading]);

  useEffect(() => {
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, []);

  if (loading) {
    return (
      <Card>
        <Skeleton active paragraph={{ rows: 8 }} />
      </Card>
    );
  }

  return (
    <Card>
      <div 
        ref={chartRef} 
        style={{ 
          width: '100%', 
          height: '400px',
          minHeight: '400px'
        }} 
      />
    </Card>
  );
};

export default RTBTrendChart; 