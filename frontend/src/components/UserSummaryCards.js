import React from 'react';
import { Card, Row, Col, Statistic, Spin, Progress } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';

const UserSummaryCards = ({ data, loading }) => {
  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!data) return null;

  const formatValue = (value, type, isArpu = false) => {
    if (type === 'percentage') {
      return `${(value * 100).toFixed(2)}%`;
    } else if (type === 'currency') {
      if (isArpu) {
        // 客单价保留1位小数
        return `¥${value.toFixed(1)}`;
      } else {
        // 其他金额保留整数，添加千分位分隔符
        return `¥${Math.round(value).toLocaleString()}`;
      }
    } else {
      return Math.round(value).toLocaleString();
    }
  };

  const getWowIcon = (wow) => {
    if (wow > 0) {
      return <ArrowUpOutlined style={{ color: '#52c41a' }} />;
    } else if (wow < 0) {
      return <ArrowDownOutlined style={{ color: '#ff4d4f' }} />;
    }
    return null;
  };

  const getWowColor = (wow) => {
    // 红跌绿涨：正数显示绿色（上涨），负数显示红色（下跌）
    if (wow > 0) return '#52c41a';  // 绿色
    if (wow < 0) return '#ff4d4f';  // 红色
    return '#666';
  };

  // 按照新的排列方式重新组织卡片数据
  // 上方：新客数量、新客销售额、新客客单价
  // 下方：老客数量、老客销售额、老客客单价
  const topRowCards = [
    {
      title: '新客数量',
      value: data.new_user_count.value,
      wow: data.new_user_count.wow,
      ratio: data.new_user_count.ratio,
      progress: data.new_user_count.progress,
      type: 'number',
      suffix: '人',
      color: '#52c41a'
    },
    {
      title: '新客销售额',
      value: data.new_user_gmv.value,
      wow: data.new_user_gmv.wow,
      ratio: data.new_user_gmv.ratio,
      progress: data.new_user_gmv.progress,
      type: 'currency',
      suffix: '',
      color: '#52c41a'
    },
    {
      title: '新客客单价',
      value: data.new_user_arpu.value,
      wow: data.new_user_arpu.wow,
      ratio: data.new_user_arpu.ratio,
      progress: data.new_user_arpu.progress,
      type: 'currency',
      suffix: '',
      color: '#52c41a'
    }
  ];

  const bottomRowCards = [
    {
      title: '老客数量',
      value: data.old_user_count.value,
      wow: data.old_user_count.wow,
      ratio: data.old_user_count.ratio,
      progress: data.old_user_count.progress,
      type: 'number',
      suffix: '人',
      color: '#1890ff'
    },
    {
      title: '老客销售额',
      value: data.old_user_gmv.value,
      wow: data.old_user_gmv.wow,
      ratio: data.old_user_gmv.ratio,
      progress: data.old_user_gmv.progress,
      type: 'currency',
      suffix: '',
      color: '#1890ff'
    },
    {
      title: '老客客单价',
      value: data.old_user_arpu.value,
      wow: data.old_user_arpu.wow,
      ratio: data.old_user_arpu.ratio,
      progress: data.old_user_arpu.progress,
      type: 'currency',
      suffix: '',
      color: '#1890ff'
    }
  ];

  const renderCard = (card, index) => {
    // 判断是否为客单价卡片（新客客单价或老客客单价）
    const isArpuCard = card.title.includes('客单价');

    return (
      <Col xs={24} sm={12} lg={8} key={index}>
        <Card
          hoverable
          style={{
            height: '180px',
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            borderLeft: `4px solid ${card.color}`
          }}
        >
          <Statistic
            title={
              <span style={{ fontSize: '14px', color: '#666' }}>
                {card.title}
              </span>
            }
            value={formatValue(card.value, card.type, isArpuCard)}
            suffix={card.suffix}
            valueStyle={{
              fontSize: '22px',
              fontWeight: 'bold',
              color: card.color
            }}
          />
          <div style={{ marginTop: '12px' }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '8px',
              fontSize: '12px'
            }}>
              <span style={{
                color: getWowColor(card.wow),
                fontWeight: 'bold'
              }}>
                {getWowIcon(card.wow)}
                周环比: {(card.wow * 100).toFixed(2)}%
              </span>
            </div>
            {/* 只有非客单价卡片才显示占比和进度条 */}
            {!isArpuCard && (
              <div style={{ marginTop: '12px' }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '4px',
                  fontSize: '12px',
                  color: '#666'
                }}>
                  <span>占比</span>
                  <span>{(card.ratio * 100).toFixed(2)}%</span>
                </div>
                <Progress
                  percent={Math.round(card.ratio * 100)}
                  showInfo={false}
                  strokeColor={{
                    '0%': card.color,
                    '100%': card.color === '#52c41a' ? '#73d13d' : '#40a9ff',
                  }}
                  strokeWidth={8}
                  style={{ width: '100%' }}
                />
              </div>
            )}
          </div>
        </Card>
      </Col>
    );
  };

  return (
    <div>
      <h3 style={{ marginBottom: '16px', color: '#1890ff' }}>用户数据概览</h3>

      {/* 第一排：新客数量、新客销售额、新客客单价 */}
      <div style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          {topRowCards.map((card, index) => renderCard(card, index))}
        </Row>
      </div>

      {/* 第二排：老客数量、老客销售额、老客客单价 */}
      <div>
        <Row gutter={[16, 16]}>
          {bottomRowCards.map((card, index) => renderCard(card, index + 3))}
        </Row>
      </div>
    </div>
  );
};

export default UserSummaryCards; 