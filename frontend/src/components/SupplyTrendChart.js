import React, { useState } from 'react';
import { Card, Select, Spin } from 'antd';
import ReactECharts from 'echarts-for-react';

const { Option } = Select;

const SupplyTrendChart = ({ data, loading }) => {
  const [selectedMetric, setSelectedMetric] = useState('store_count');

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!data || !data.trend_data) return null;

  const metricOptions = [
    { value: 'gmv', label: 'GMV', unit: '元' },
    { value: 'store_count', label: '铺货店铺数', unit: '家' },
    { value: 'store_penetration', label: '店铺渗透率', unit: '%' },
    { value: 'store_activity_rate', label: '店铺动销率', unit: '%' },
    { value: 'avg_sku_per_store', label: '店均SKU数', unit: '个' },
    { value: 'sku_sold_out_rate', label: 'SKU售罄率', unit: '%' }
  ];

  const selectedMetricInfo = metricOptions.find(opt => opt.value === selectedMetric);

  const getChartOption = () => {
    const weeks = data.trend_data.map(item => `第${item.week}周`);
    const mainValues = data.trend_data.map(item => {
      const value = item[selectedMetric];
      if (selectedMetric === 'gmv') {
        return Math.round(value); // 使用整数金额，不转换为万元
      } else if (selectedMetric.includes('rate') || selectedMetric.includes('penetration')) {
        return (value * 100).toFixed(2);
      } else {
        return value;
      }
    });
    const wowValues = data.trend_data.map(item => {
      const wow = item[`${selectedMetric}_wow`];
      // 对于pp格式的指标（渗透率、动销率、售罄率），直接显示百分点差异
      if (selectedMetric === 'store_penetration' ||
          selectedMetric === 'store_activity_rate' ||
          selectedMetric === 'sku_sold_out_rate') {
        return (wow * 100).toFixed(2);
      } else {
        return (wow * 100).toFixed(2);
      }
    });

    return {
      title: {
        text: `${selectedMetricInfo.label}趋势分析`,
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params) {
          let result = `${params[0].name}<br/>`;
          params.forEach(param => {
            const color = param.color;
            let value = param.value;
            const unit = param.seriesName.includes('环比') ? '%' : selectedMetricInfo.unit;

            // 对于GMV，使用千分位符格式化
            if (selectedMetric === 'gmv' && !param.seriesName.includes('环比')) {
              value = Math.round(value).toLocaleString();
            }

            result += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
            result += `${param.seriesName}: ${value}${unit}<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: [selectedMetricInfo.label, '周环比'],
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: weeks,
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: [
        {
          type: 'value',
          name: selectedMetricInfo.unit,
          position: 'left',
          axisLabel: {
            formatter: function(value) {
              if (selectedMetric === 'gmv') {
                return Math.round(value).toLocaleString();
              }
              return value;
            }
          }
        },
        {
          type: 'value',
          name: '环比(%)',
          position: 'right',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      series: [
        {
          name: selectedMetricInfo.label,
          type: 'bar',
          data: mainValues,
          itemStyle: {
            color: '#1890ff',
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(24, 144, 255, 0.3)'
            }
          },
          yAxisIndex: 0
        },
        {
          name: '周环比',
          type: 'line',
          yAxisIndex: 1,
          data: wowValues,
          itemStyle: {
            color: '#52c41a'
          },
          lineStyle: {
            width: 3,
            shadowBlur: 5,
            shadowColor: 'rgba(82, 196, 26, 0.3)'
          },
          symbol: 'circle',
          symbolSize: 6,
          smooth: true
        }
      ]
    };
  };

  return (
    <Card 
      title="供给趋势分析" 
      extra={
        <Select
          value={selectedMetric}
          onChange={setSelectedMetric}
          style={{ width: 150 }}
          size="small"
        >
          {metricOptions.map(option => (
            <Option key={option.value} value={option.value}>
              {option.label}
            </Option>
          ))}
        </Select>
      }
      style={{ 
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}
    >
      <ReactECharts 
        option={getChartOption()} 
        style={{ height: '400px' }}
        opts={{ renderer: 'canvas' }}
      />
    </Card>
  );
};

export default SupplyTrendChart; 