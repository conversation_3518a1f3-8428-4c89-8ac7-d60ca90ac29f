import React, { useRef, useEffect, useState } from 'react';
import { Card } from 'antd';
import * as echarts from 'echarts';

const ActivityTrendChartDebug = ({ data, loading, selectedMetric, platform }) => {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);
  const [renderCount, setRenderCount] = useState(0);

  // 每次渲染时增加计数
  useEffect(() => {
    setRenderCount(prev => prev + 1);
  });

  console.log('🔍 ActivityTrendChartDebug - 渲染信息:', {
    renderCount,
    platform,
    loading,
    hasData: !!data,
    hasTrendData: !!(data && data.trend_data),
    trendDataLength: data && data.trend_data ? data.trend_data.length : 0,
    selectedMetric,
    chartRefCurrent: !!chartRef.current,
    chartInstanceCurrent: !!chartInstance.current
  });

  useEffect(() => {
    console.log('🔄 ActivityTrendChartDebug - useEffect 触发:', {
      platform,
      hasChartRef: !!chartRef.current,
      hasData: !!data,
      loading,
      selectedMetric
    });

    if (!chartRef.current) {
      console.log('❌ chartRef.current 为空');
      return;
    }

    if (loading) {
      console.log('⏳ 正在加载中');
      return;
    }

    if (!data || !data.trend_data || !selectedMetric) {
      console.log('❌ 数据不完整:', {
        hasData: !!data,
        hasTrendData: !!(data && data.trend_data),
        selectedMetric
      });
      return;
    }

    // 强制清理现有实例
    if (chartInstance.current) {
      console.log('🧹 清理现有图表实例');
      try {
        chartInstance.current.dispose();
        chartInstance.current = null;
      } catch (error) {
        console.error('清理实例时出错:', error);
      }
    }

    // 检查DOM上的现有实例
    const existingInstance = echarts.getInstanceByDom(chartRef.current);
    if (existingInstance) {
      console.log('🧹 清理DOM上的现有实例');
      try {
        existingInstance.dispose();
      } catch (error) {
        console.error('清理DOM实例时出错:', error);
      }
    }

    // 检查容器尺寸
    const rect = chartRef.current.getBoundingClientRect();
    console.log('📏 容器尺寸:', {
      width: rect.width,
      height: rect.height
    });

    if (rect.width === 0 || rect.height === 0) {
      console.log('⚠️ 容器尺寸为0，延迟初始化');
      setTimeout(() => {
        if (chartRef.current && !chartInstance.current) {
          console.log('🔄 延迟重试初始化');
          // 递归调用自己
          setRenderCount(prev => prev + 1);
        }
      }, 100);
      return;
    }

    try {
      console.log('🎨 开始初始化ECharts');
      chartInstance.current = echarts.init(chartRef.current);
      console.log('✅ ECharts初始化成功');

      // 处理数据
      const trendData = data.trend_data;
      const weeks = trendData.map(item => `第${item.week}周`);
      const activityGmv = trendData.map(item => Math.round(item.activity_gmv));

      console.log('📊 数据处理完成:', {
        weeks: weeks.slice(0, 3),
        values: activityGmv.slice(0, 3)
      });

      // 简化的图表配置
      const option = {
        title: {
          text: `活动GMV趋势 - ${platform} (调试版)`,
          left: 'left'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: weeks
        },
        yAxis: {
          type: 'value',
          name: '活动GMV (元)'
        },
        series: [{
          name: '活动GMV',
          type: 'bar',
          data: activityGmv,
          itemStyle: {
            color: '#1890ff'
          }
        }]
      };

      console.log('⚙️ 设置图表配置');
      chartInstance.current.setOption(option, true);
      console.log('🎉 图表渲染完成!');

    } catch (error) {
      console.error('❌ 图表初始化失败:', error);
    }

    // 清理函数
    return () => {
      console.log('🧹 useEffect 清理函数执行');
    };

  }, [data, loading, selectedMetric, platform, renderCount]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      console.log('🗑️ 组件卸载，清理资源');
      if (chartInstance.current) {
        try {
          chartInstance.current.dispose();
          chartInstance.current = null;
        } catch (error) {
          console.error('组件卸载清理时出错:', error);
        }
      }
    };
  }, []);

  if (loading) {
    return (
      <Card title="活动趋势图 (调试版) - 加载中">
        <div style={{ height: '400px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          加载中...
        </div>
      </Card>
    );
  }

  if (!data || !data.trend_data || !selectedMetric) {
    return (
      <Card title="活动趋势图 (调试版) - 无数据">
        <div style={{ height: '400px', display: 'flex', alignItems: 'center', justifyContent: 'center', flexDirection: 'column' }}>
          <div>暂无数据</div>
          <div style={{ fontSize: '12px', color: '#999', marginTop: '10px' }}>
            hasData: {String(!!data)}, hasTrendData: {String(!!(data && data.trend_data))}, selectedMetric: {selectedMetric || 'null'}
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card title={`活动趋势图 (调试版) - ${platform} - 渲染次数: ${renderCount}`}>
      <div 
        ref={chartRef} 
        style={{ 
          width: '100%', 
          height: '400px',
          minHeight: '400px',
          border: '1px dashed #ccc'
        }} 
      />
    </Card>
  );
};

export default ActivityTrendChartDebug;
