import React, { useState, useEffect } from 'react';
import { Space } from 'antd';
import PlatformFilter from './PlatformFilter';
import RTBSummaryCards from './RTBSummaryCards';
import RTBInteractiveTrendChart from './RTBInteractiveTrendChart';
import RTBDetailTable from './RTBDetailTable';
import { fetchRTBSummary, fetchRTBTrends, fetchRTBDetail } from '../services/api';

const RTBDataModule = ({ selectedWeek, brand = '' }) => {
  const [selectedPlatform, setSelectedPlatform] = useState('美团');
  const [selectedDimension, setSelectedDimension] = useState('整体');
  const [summaryData, setSummaryData] = useState(null);
  const [trendsData, setTrendsData] = useState(null);
  const [detailData, setDetailData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [detailLoading, setDetailLoading] = useState(false);
  const [selectedCard, setSelectedCard] = useState('consumption'); // 默认选中第1个卡片

  // 获取概览和趋势数据（不依赖维度）
  useEffect(() => {
    const loadSummaryAndTrends = async () => {
      setLoading(true);
      try {
        const [summary, trends] = await Promise.all([
          fetchRTBSummary(selectedWeek, brand),
          fetchRTBTrends(brand)
        ]);

        setSummaryData(summary);
        setTrendsData(trends);
      } catch (error) {
        console.error('Failed to load RTB summary and trends data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadSummaryAndTrends();
  }, [selectedWeek, brand, selectedPlatform]);

  // 单独加载明细数据（依赖维度）
  useEffect(() => {
    const loadDetailData = async () => {
      setDetailLoading(true);
      try {
        const detail = await fetchRTBDetail(selectedDimension, selectedWeek, brand);
        setDetailData(detail);
      } catch (error) {
        console.error('Failed to load RTB detail data:', error);
      } finally {
        setDetailLoading(false);
      }
    };

    loadDetailData();
  }, [selectedDimension, selectedWeek, brand, selectedPlatform]);

  const handleDimensionChange = (dimension) => {
    setSelectedDimension(dimension);
  };

  // 处理平台筛选变化
  const handlePlatformChange = (platform) => {
    setSelectedPlatform(platform);
  };

  // 处理卡片点击
  const handleCardClick = (cardKey) => {
    setSelectedCard(cardKey);
  };

  return (
    <div style={{ padding: '16px' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 平台筛选器 */}
        <div className="trading-section">
          <PlatformFilter
            selectedPlatform={selectedPlatform}
            onPlatformChange={handlePlatformChange}
            singlePlatform={true}
          />
        </div>

        {/* 概览卡片 */}
        <div className="trading-section">
          <RTBSummaryCards
            data={summaryData}
            loading={loading}
            onCardClick={handleCardClick}
            selectedCard={selectedCard}
          />
        </div>

        {/* RTB趋势分析 - 联动图表 */}
        <div className="trading-section">
          <RTBInteractiveTrendChart
            data={trendsData}
            loading={loading}
            selectedMetric={selectedCard}
          />
        </div>

        {/* RTB明细数据表格 */}
        <div className="trading-section">
          <RTBDetailTable
            data={detailData}
            selectedDimension={selectedDimension}
            onDimensionChange={handleDimensionChange}
            loading={detailLoading}
          />
        </div>
      </Space>
    </div>
  );
};

export default RTBDataModule; 