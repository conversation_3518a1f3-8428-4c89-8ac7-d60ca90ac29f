import React from 'react';
import { Row, Col, Card, Progress, Skeleton } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';

const formatNumber = (num) => {
  // 统一使用千分位符格式化整数金额
  return Math.round(num).toLocaleString();
};

const formatPercent = (num) => {
  const percent = (num * 100).toFixed(2);
  return `${percent >= 0 ? '+' : ''}${percent}%`;
};

const formatPP = (num) => {
  const pp = (num * 100).toFixed(2);
  return `${pp >= 0 ? '+' : ''}${pp}pp`;
};

const ChangeIndicator = ({ value, label, isPercent = false }) => {
  const isPositive = value >= 0;
  const formatValue = isPercent ? formatPP(value) : formatPercent(value);
  
  return (
    <div className="change-item">
      {isPositive ? (
        <ArrowUpOutlined className="change-positive" />
      ) : (
        <ArrowDownOutlined className="change-negative" />
      )}
      <span className={isPositive ? 'change-positive' : 'change-negative'}>
        {label}: {formatValue}
      </span>
    </div>
  );
};

const ActivitySummaryCards = ({ data, loading, onCardClick, selectedCard }) => {
  if (loading || !data) {
    return (
      <div>
        <h3 style={{ marginBottom: '16px', color: '#1890ff' }}>活动数据概览</h3>
        <div style={{
          display: 'flex',
          gap: '16px',
          width: '100%',
          flexWrap: 'nowrap'
        }}>
          {[1, 2, 3, 4, 5].map(i => (
            <div
              key={i}
              style={{
                flex: '1',
                minWidth: '0'
              }}
            >
              <Card style={{ height: '140px' }}>
                <Skeleton active />
              </Card>
            </div>
          ))}
        </div>
      </div>
    );
  }

  const {
    activity_gmv,
    activity_gmv_ratio,
    verification_amount,
    activity_cost_ratio,
    total_cost_ratio
  } = data;

  // 卡片配置
  const cardConfigs = [
    {
      key: 'activity_gmv',
      title: '活动GMV',
      value: `¥${formatNumber(activity_gmv.value)}`,
      yoy: activity_gmv.yoy,
      isPercent: false
    },
    {
      key: 'activity_gmv_ratio',
      title: '活动GMV占比',
      value: `${(activity_gmv_ratio.value * 100).toFixed(2)}%`,
      yoy: activity_gmv_ratio.yoy,
      isPercent: true
    },
    {
      key: 'verification_amount',
      title: '核销金额',
      value: `¥${formatNumber(verification_amount.value)}`,
      yoy: verification_amount.yoy,
      isPercent: false
    },
    {
      key: 'activity_cost_ratio',
      title: '活动费比',
      value: `${(activity_cost_ratio.value * 100).toFixed(2)}%`,
      yoy: activity_cost_ratio.yoy,
      isPercent: true
    },
    {
      key: 'total_cost_ratio',
      title: '全量费比',
      value: `${(total_cost_ratio.value * 100).toFixed(2)}%`,
      yoy: total_cost_ratio.yoy,
      isPercent: true
    }
  ];

  return (
    <div>
      <h3 style={{ marginBottom: '16px', color: '#1890ff' }}>活动数据概览</h3>
      <div style={{
        display: 'flex',
        gap: '16px',
        width: '100%',
        flexWrap: 'nowrap' // 确保不换行
      }}>
        {cardConfigs.map((config) => (
          <div
            key={config.key}
            style={{
              flex: '1', // 每个卡片占用相等的空间
              minWidth: '0' // 允许卡片收缩
            }}
          >
            <Card
              hoverable
              onClick={() => onCardClick && onCardClick(config.key)}
              style={{
                textAlign: 'center',
                borderRadius: '8px',
                cursor: onCardClick ? 'pointer' : 'default',
                border: selectedCard === config.key ? '2px solid #1890ff' : '1px solid #d9d9d9',
                boxShadow: selectedCard === config.key ? '0 4px 12px rgba(24, 144, 255, 0.3)' : '0 2px 8px rgba(0,0,0,0.1)',
                height: '100%' // 确保所有卡片高度一致
              }}
            >
              <div style={{ padding: '8px 0' }}>
                <div style={{
                  fontSize: '14px',
                  color: '#666',
                  marginBottom: '8px',
                  fontWeight: '500'
                }}>
                  {config.title}
                </div>
                <div style={{
                  fontSize: '24px',
                  fontWeight: 'bold',
                  color: '#1890ff',
                  marginBottom: '8px'
                }}>
                  {config.value}
                </div>
                <div style={{ fontSize: '12px' }}>
                  <ChangeIndicator value={config.yoy} label="年同比" isPercent={config.isPercent} />
                </div>
              </div>
            </Card>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ActivitySummaryCards; 