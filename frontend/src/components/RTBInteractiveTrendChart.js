import React, { useRef, useEffect } from 'react';
import { Card, Skeleton } from 'antd';
import * as echarts from 'echarts';

const RTBInteractiveTrendChart = ({ data, loading, selectedMetric }) => {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  useEffect(() => {
    if (!chartRef.current) return;

    // 初始化图表
    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
    }

    if (loading || !data || !data.trend_data || !selectedMetric) {
      chartInstance.current.showLoading('default', {
        text: '加载中...',
        color: '#1890ff',
        textColor: '#000',
        maskColor: 'rgba(255, 255, 255, 0.8)',
        zlevel: 0
      });
      return;
    }

    chartInstance.current.hideLoading();

    const trendData = data.trend_data;
    const weeks = trendData.map(item => `第${item.week}周`);

    // 指标配置
    const metricConfigs = {
      consumption: {
        title: '消耗趋势分析',
        mainLabel: '消耗',
        yoyLabel: '消耗周环比',
        mainField: 'consumption',
        yoyField: 'consumption_wow',
        unit: '元',
        color: '#1890ff'
      },
      t1_guided_amount: {
        title: 'T+1引导成交金额趋势分析',
        mainLabel: 'T+1引导成交金额',
        yoyLabel: '引导金额周环比',
        mainField: 't1_guided_amount',
        yoyField: 't1_guided_amount_wow',
        unit: '元',
        color: '#1890ff'
      },
      roi: {
        title: 'ROI趋势分析',
        mainLabel: 'ROI',
        yoyLabel: 'ROI周环比',
        mainField: 'roi',
        yoyField: 'roi_wow',
        unit: '',
        color: '#1890ff'
      }
    };

    const config = metricConfigs[selectedMetric];
    if (!config) return;

    // 格式化数据
    const mainValues = trendData.map(item => {
      const value = item[config.mainField];
      if (config.mainField === 'roi') {
        return Number(value).toFixed(2);
      }
      return Math.round(value);
    });

    const yoyValues = trendData.map(item => {
      const value = item[config.yoyField];
      return (value * 100).toFixed(2);
    });

    const formatNumber = (num) => {
      if (config.mainField === 'roi') {
        return Number(num).toFixed(2);
      }
      return Math.round(num).toLocaleString();
    };

    const option = {
      title: {
        text: config.title,
        left: 'left',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params) {
          let result = `${params[0].name}<br/>`;
          params.forEach(param => {
            const color = param.color;
            let value = param.value;
            let unit = '';
            
            if (param.seriesName.includes('周环比')) {
              unit = '%';
            } else {
              unit = config.unit;
              if (config.mainField === 'roi') {
                value = Number(value).toFixed(2);
              } else {
                value = Math.round(value).toLocaleString();
              }
            }
            
            result += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
            result += `${param.seriesName}: ${value}${unit}<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: [config.mainLabel, config.yoyLabel],
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: true,
        data: weeks,
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: [
        {
          type: 'value',
          name: config.unit,
          position: 'left',
          axisLabel: {
            formatter: function(value) {
              if (config.mainField === 'roi') {
                return Number(value).toFixed(2);
              }
              return Math.round(value).toLocaleString();
            }
          }
        },
        {
          type: 'value',
          name: '周环比(%)',
          position: 'right',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      series: [
        {
          name: config.mainLabel,
          type: 'bar',
          data: mainValues,
          itemStyle: {
            color: config.color,
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: `${config.color}33`
            }
          },
          yAxisIndex: 0,
          barWidth: '50%'
        },
        {
          name: config.yoyLabel,
          type: 'line',
          yAxisIndex: 1,
          data: yoyValues,
          itemStyle: {
            color: '#f5222d'
          },
          lineStyle: {
            width: 3,
            shadowBlur: 5,
            shadowColor: 'rgba(245, 34, 45, 0.3)'
          },
          symbol: 'circle',
          symbolSize: 6,
          smooth: true
        }
      ]
    };

    chartInstance.current.setOption(option);

    // 响应式处理
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [data, loading, selectedMetric]);

  useEffect(() => {
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, []);

  if (loading) {
    return (
      <Card>
        <Skeleton active paragraph={{ rows: 8 }} />
      </Card>
    );
  }

  // 移除这个判断，因为现在默认会选中第一个卡片

  return (
    <Card>
      <div 
        ref={chartRef} 
        style={{ 
          width: '100%', 
          height: '400px',
          minHeight: '400px'
        }} 
      />
    </Card>
  );
};

export default RTBInteractiveTrendChart;
