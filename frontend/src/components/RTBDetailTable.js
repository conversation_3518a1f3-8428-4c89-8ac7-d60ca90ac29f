import React from 'react';
import { Table, Select, Card, Skeleton } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';

const { Option } = Select;

// 安全的数值排序函数
const safeSorter = (field) => (a, b) => {
  const aVal = a[field] || 0;
  const bVal = b[field] || 0;
  return aVal - bVal;
};

const formatNumber = (num) => {
  if (num === null || num === undefined || isNaN(num)) {
    return '0';
  }
  const numValue = Number(num);
  // 统一使用千分位符格式化整数金额
  return Math.round(numValue).toLocaleString();
};

const formatPercent = (num) => {
  if (num === null || num === undefined || isNaN(num)) {
    return '0.00%';
  }
  const percent = (Number(num) * 100).toFixed(2);
  return `${percent >= 0 ? '+' : ''}${percent}%`;
};

// 红跌绿涨样式组件
const ChangeIndicator = ({ value, showIcon = true }) => {
  if (value === null || value === undefined || isNaN(value)) {
    return <span className="change-neutral">0.0%</span>;
  }

  const numValue = Number(value);
  const isPositive = numValue >= 0;
  const colorClass = isPositive ? 'change-positive' : 'change-negative';

  return (
    <span className={colorClass}>
      {showIcon && (isPositive ? <ArrowUpOutlined /> : <ArrowDownOutlined />)}
      {formatPercent(numValue)}
    </span>
  );
};

const RTBDetailTable = ({ data, selectedDimension, onDimensionChange, loading }) => {
  // 根据维度类型动态设置列标题和宽度
  const getDimensionColumnConfig = () => {
    if (selectedDimension === '整体') {
      return {
        title: '周次',
        width: 120,
      };
    } else if (selectedDimension === '计划') {
      return {
        title: '计划',
        width: 200, // 增加宽度避免换行
      };
    } else if (selectedDimension === '商品') {
      return {
        title: '商品',
        width: 180,
      };
    } else {
      return {
        title: selectedDimension,
        width: 120,
      };
    }
  };

  const dimensionConfig = getDimensionColumnConfig();

  // 基础列定义
  const baseColumns = [
    {
      title: dimensionConfig.title,
      dataIndex: 'dimension',
      key: 'dimension',
      fixed: 'left',
      width: dimensionConfig.width,
      sorter: (a, b) => a.dimension.localeCompare(b.dimension),
    },
    {
      title: '消耗',
      dataIndex: 'consumption',
      key: 'consumption',
      width: 120,
      sorter: safeSorter('consumption'),
      render: (value) => `¥${formatNumber(value)}`,
    },
    {
      title: '消耗周环比',
      dataIndex: 'consumption_wow',
      key: 'consumption_wow',
      width: 120,
      sorter: safeSorter('consumption_wow'),
      render: (value) => <ChangeIndicator value={value} />,
    },
    {
      title: '消耗年同比',
      dataIndex: 'consumption_yoy',
      key: 'consumption_yoy',
      width: 120,
      sorter: safeSorter('consumption_yoy'),
      render: (value) => <ChangeIndicator value={value} />,
    },
    {
      title: '消耗进度',
      dataIndex: 'consumption_progress',
      key: 'consumption_progress',
      width: 100,
      sorter: safeSorter('consumption_progress'),
      render: (value) => {
        if (value === null || value === undefined || isNaN(value)) {
          return '0.00%';
        }
        return `${(Number(value) * 100).toFixed(2)}%`;
      },
    },
    {
      title: 'T+1引导成交金额',
      dataIndex: 'guided_amount',
      key: 'guided_amount',
      width: 150,
      sorter: safeSorter('guided_amount'),
      render: (value) => `¥${formatNumber(value)}`,
    },
    {
      title: '引导GMV周环比',
      dataIndex: 'guided_amount_wow',
      key: 'guided_amount_wow',
      width: 140,
      sorter: safeSorter('guided_amount_wow'),
      render: (value) => <ChangeIndicator value={value} />,
    },
    {
      title: '引导GMV年同比',
      dataIndex: 'guided_amount_yoy',
      key: 'guided_amount_yoy',
      width: 140,
      sorter: safeSorter('guided_amount_yoy'),
      render: (value) => <ChangeIndicator value={value} />,
    },
    {
      title: 'ROI',
      dataIndex: 'roi',
      key: 'roi',
      width: 80,
      sorter: safeSorter('roi'),
      render: (value) => {
        if (value === null || value === undefined || isNaN(value)) {
          return '0.00';
        }
        return Number(value).toFixed(2);
      },
    },
    {
      title: 'ROI周环比',
      dataIndex: 'roi_wow',
      key: 'roi_wow',
      width: 120,
      sorter: safeSorter('roi_wow'),
      render: (value) => <ChangeIndicator value={value} />,
    },
    {
      title: 'ROI年同比',
      dataIndex: 'roi_yoy',
      key: 'roi_yoy',
      width: 120,
      sorter: safeSorter('roi_yoy'),
      render: (value) => <ChangeIndicator value={value} />,
    },
    {
      title: '曝光数',
      dataIndex: 'exposure',
      key: 'exposure',
      width: 100,
      sorter: safeSorter('exposure'),
      render: (value) => formatNumber(value),
    },
    {
      title: '曝光周环比',
      dataIndex: 'exposure_wow',
      key: 'exposure_wow',
      width: 120,
      sorter: safeSorter('exposure_wow'),
      render: (value) => <ChangeIndicator value={value} />,
    },
    {
      title: '曝光年同比',
      dataIndex: 'exposure_yoy',
      key: 'exposure_yoy',
      width: 120,
      sorter: safeSorter('exposure_yoy'),
      render: (value) => <ChangeIndicator value={value} />,
    },
    {
      title: '点击数',
      dataIndex: 'click',
      key: 'click',
      width: 100,
      sorter: safeSorter('click'),
      render: (value) => formatNumber(value),
    },
    {
      title: '点击周环比',
      dataIndex: 'click_wow',
      key: 'click_wow',
      width: 120,
      sorter: safeSorter('click_wow'),
      render: (value) => <ChangeIndicator value={value} />,
    },
    {
      title: '点击年同比',
      dataIndex: 'click_yoy',
      key: 'click_yoy',
      width: 120,
      sorter: safeSorter('click_yoy'),
      render: (value) => <ChangeIndicator value={value} />,
    },
    {
      title: '点击率',
      dataIndex: 'ctr',
      key: 'ctr',
      width: 100,
      sorter: safeSorter('ctr'),
      render: (value) => {
        if (value === null || value === undefined || isNaN(value)) {
          return '0.000%';
        }
        return `${(Number(value) * 100).toFixed(3)}%`;
      },
    },
    {
      title: '订单量',
      dataIndex: 'order_volume',
      key: 'order_volume',
      width: 100,
      sorter: safeSorter('order_volume'),
      render: (value) => formatNumber(value),
    },
    {
      title: '订单周环比',
      dataIndex: 'order_volume_wow',
      key: 'order_volume_wow',
      width: 120,
      sorter: safeSorter('order_volume_wow'),
      render: (value) => <ChangeIndicator value={value} />,
    },
    {
      title: '订单年同比',
      dataIndex: 'order_volume_yoy',
      key: 'order_volume_yoy',
      width: 120,
      sorter: safeSorter('order_volume_yoy'),
      render: (value) => <ChangeIndicator value={value} />,
    },
    {
      title: '订单转化率',
      dataIndex: 'order_conversion_rate',
      key: 'order_conversion_rate',
      width: 120,
      sorter: safeSorter('order_conversion_rate'),
      render: (value) => {
        if (value === null || value === undefined || isNaN(value)) {
          return '0.000%';
        }
        return `${(Number(value) * 100).toFixed(3)}%`;
      },
    },
    {
      title: '预算金额',
      dataIndex: 'budget',
      key: 'budget',
      width: 120,
      sorter: safeSorter('budget'),
      render: (value) => `¥${formatNumber(value)}`,
    },
    {
      title: '千次曝光成本',
      dataIndex: 'cpm',
      key: 'cpm',
      width: 120,
      sorter: safeSorter('cpm'),
      render: (value) => {
        if (value === null || value === undefined || isNaN(value)) {
          return '¥0.00';
        }
        return `¥${Number(value).toFixed(2)}`;
      },
    },
    {
      title: '点击成本',
      dataIndex: 'cpc',
      key: 'cpc',
      width: 100,
      sorter: safeSorter('cpc'),
      render: (value) => {
        if (value === null || value === undefined || isNaN(value)) {
          return '¥0.00';
        }
        return `¥${Number(value).toFixed(2)}`;
      },
    }
  ];

  // 只有计划维度才显示新客成本
  if (selectedDimension === '计划') {
    baseColumns.push(
      {
        title: '新客成本',
        dataIndex: 'new_user_cost',
        key: 'new_user_cost',
        width: 100,
        sorter: safeSorter('new_user_cost'),
        render: (value) => {
          if (value === null || value === undefined || isNaN(value)) {
            return '¥0.00';
          }
          return `¥${Number(value).toFixed(2)}`;
        },
      },
      {
        title: '新客成本周环比',
        dataIndex: 'new_user_cost_wow',
        key: 'new_user_cost_wow',
        width: 140,
        sorter: safeSorter('new_user_cost_wow'),
        render: (value) => <ChangeIndicator value={value} />,
      },
      {
        title: '新客成本年同比',
        dataIndex: 'new_user_cost_yoy',
        key: 'new_user_cost_yoy',
        width: 140,
        sorter: safeSorter('new_user_cost_yoy'),
        render: (value) => <ChangeIndicator value={value} />,
      }
    );
  }

  const columns = baseColumns;

  if (loading || !data) {
    return (
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <div className="section-title">RTB明细数据</div>
        </div>
        <Skeleton active paragraph={{ rows: 8 }} />
      </Card>
    );
  }

  const { data: tableData } = data;

  return (
    <Card>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <div className="section-title">RTB明细数据</div>
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <span>数据维度:</span>
          <Select
            value={selectedDimension}
            onChange={onDimensionChange}
            style={{ width: 120 }}
          >
            <Option value="整体">整体</Option>
            <Option value="计划">计划</Option>
            <Option value="商品">商品</Option>
          </Select>
        </div>
      </div>

      <Table
        columns={columns}
        dataSource={tableData.map((item, index) => ({
          ...item,
          key: index
        }))}
        scroll={{ x: 'max-content', y: 400 }}
        size="small"
        pagination={{
          pageSize: 20,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => 
            `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
        }}
        bordered
      />
    </Card>
  );
};

export default RTBDetailTable; 