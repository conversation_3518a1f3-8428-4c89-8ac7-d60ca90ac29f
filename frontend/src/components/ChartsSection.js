import React from 'react';
import { Row, Col, Card, Skeleton } from 'antd';
import ReactECharts from 'echarts-for-react';
import { getPlatformColor, sortPlatformData } from '../utils/platformColors';

const formatNumber = (num) => {
  // 统一使用千分位符格式化整数金额
  return Math.round(num).toLocaleString();
};

const ChartsSection = ({ data, selectedPlatform, loading }) => {
  if (loading || !data) {
    return (
      <div className="charts-section">
        <Row gutter={[16, 16]}>
          {selectedPlatform === '全平台' && (
            <Col xs={24} lg={6}>
              <Card title="平台GMV占比" data-chart-type="platform_chart">
                <Skeleton active />
              </Card>
            </Col>
          )}
          <Col xs={24} lg={selectedPlatform === '全平台' ? 18 : 24}>
            <Card title="GMV趋势" data-chart-type="trend_chart">
              <Skeleton active />
            </Card>
          </Col>
        </Row>
      </div>
    );
  }

  const { trend_data, platform_data } = data;

  // 对平台数据进行排序
  const sortedPlatformData = sortPlatformData(platform_data);

  // 饼图配置
  const pieOption = {
    title: {
      text: '平台GMV占比',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 600
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: ¥{c} ({d}%)',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e6e6e6',
      borderWidth: 1,
      borderRadius: 8,
      textStyle: {
        color: '#333'
      }
    },
    legend: {
      orient: 'horizontal',
      left: 'center',
      bottom: '5%',
      itemGap: 8,
      textStyle: {
        fontSize: 11
      }
    },
    series: [
      {
        name: 'GMV',
        type: 'pie',
        radius: ['25%', '50%'],
        center: ['45%', '50%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 20,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          },
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}: {d}%',
          fontSize: 11,
          fontWeight: 'normal',
          distanceToLabelLine: 5
        },
        labelLine: {
          show: true,
          length: 10,
          length2: 8,
          smooth: true
        },
        data: sortedPlatformData ? Object.entries(sortedPlatformData).map(([name, value]) => ({
          value: value.gmv,
          name: name,
          itemStyle: {
            color: getPlatformColor(name)
          }
        })) : []
      }
    ]
  };

  // 复合图配置
  const comboOption = {
    title: {
      text: 'GMV趋势分析',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 600
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e6e6e6',
      borderWidth: 1,
      borderRadius: 8,
      textStyle: {
        color: '#333'
      },
      formatter: function(params) {
        let result = `${params[0].name}<br/>`;  // params[0].name已经是"第X周"格式
        params.forEach(param => {
          if (param.seriesName === 'GMV') {
            result += `${param.seriesName}: ¥${formatNumber(param.value)}<br/>`;
          } else {
            // 后端返回的已经是百分比数值（如-19.8表示-19.8%），确保是数字类型后显示
            const value = typeof param.value === 'number' ? param.value : parseFloat(param.value) || 0;
            result += `${param.seriesName}: ${value.toFixed(2)}%<br/>`;
          }
        });
        return result;
      }
    },
    legend: {
      data: ['GMV', '年同比', '周环比'],
      top: 'bottom',
      itemGap: 20,
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: trend_data ? trend_data.map(item => `第${item.week}周`) : [],
      axisLine: {
        lineStyle: {
          color: '#666666'
        }
      },
      axisLabel: {
        color: '#666666',
        fontSize: 12
      },
      axisTick: {
        show: false
      }
    },
    yAxis: [
      {
        type: 'value',
        name: 'GMV',
        position: 'left',
        nameTextStyle: {
          color: '#666666',
          fontSize: 12
        },
        axisLabel: {
          formatter: function(value) {
            return formatNumber(value);
          },
          color: '#666666',
          fontSize: 11
        },
        axisLine: {
          lineStyle: {
            color: '#666666'
          }
        },
        splitLine: {
          lineStyle: {
            color: '#e8e8e8'
          }
        }
      },
      {
        type: 'value',
        name: '变化率',
        position: 'right',
        nameTextStyle: {
          color: '#666666',
          fontSize: 12
        },
        axisLabel: {
          formatter: '{value}%',
          color: '#666666',
          fontSize: 11
        },
        axisLine: {
          lineStyle: {
            color: '#666666'
          }
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: 'GMV',
        type: 'bar',
        yAxisIndex: 0,
        data: trend_data ? trend_data.map(item => item.gmv) : [],
        itemStyle: {
          color: '#1890ff',
          borderRadius: [4, 4, 0, 0]
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(24, 144, 255, 0.3)'
          }
        }
      },
      {
        name: '年同比',
        type: 'line',
        yAxisIndex: 1,
        data: trend_data ? trend_data.map(item => parseFloat(item.yoy).toFixed(2)) : [],
        itemStyle: {
          color: '#52c41a'
        },
        lineStyle: {
          width: 3,
          shadowBlur: 5,
          shadowColor: 'rgba(82, 196, 26, 0.3)'
        },
        symbol: 'circle',
        symbolSize: 6,
        smooth: true
      },
      {
        name: '周环比',
        type: 'line',
        yAxisIndex: 1,
        data: trend_data ? trend_data.map(item => parseFloat(item.wow).toFixed(2)) : [],
        itemStyle: {
          color: '#faad14'
        },
        lineStyle: {
          width: 3,
          shadowBlur: 5,
          shadowColor: 'rgba(250, 173, 20, 0.3)'
        },
        symbol: 'circle',
        symbolSize: 6,
        smooth: true
      }
    ]
  };

  return (
    <div className="charts-section">
      <Row gutter={[16, 16]}>
        {/* 饼图 - 仅在全平台时显示，放在左侧占1/3宽度 */}
        {selectedPlatform === '全平台' && sortedPlatformData && (
          <Col xs={24} lg={8}>
            <Card data-chart-type="platform_chart">
              <div data-chart-type="platform_chart">
                <ReactECharts
                  option={pieOption}
                  style={{ height: '450px' }}
                  notMerge={true}
                />
              </div>
            </Card>
          </Col>
        )}

        {/* 复合图 - 放在右侧，根据是否显示饼图调整宽度 */}
        <Col xs={24} lg={selectedPlatform === '全平台' ? 16 : 24}>
          <Card data-chart-type="trend_chart">
            <div data-chart-type="trend_chart">
              <ReactECharts
                option={comboOption}
                style={{ height: '450px' }}
                notMerge={true}
              />
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ChartsSection; 