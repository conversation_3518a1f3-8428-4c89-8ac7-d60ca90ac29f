import React, { useState, useEffect } from 'react';
import { Layout, Menu } from 'antd';
import {
  BarChartOutlined,
  DashboardOutlined,
  RadarChartOutlined,
  LineChartOutlined,
  UserOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import isoWeek from 'dayjs/plugin/isoWeek';
import 'dayjs/locale/zh-cn';
import HeaderToolbar from './components/HeaderToolbar';
import TradingDataModule from './components/TradingDataModule';
import ActivityDataModule from './components/ActivityDataModule';
import RTBDataModule from './components/RTBDataModule';
import SupplyDataModule from './components/SupplyDataModule';
import UserDataModule from './components/UserDataModule';
import { logConfigInfo } from './utils/configValidator';
import './App.css';

// 配置dayjs
dayjs.extend(weekOfYear);
dayjs.extend(isoWeek);
dayjs.locale('zh-cn');

const { Sider, Content } = Layout;

const menuItems = [
  {
    key: 'trading',
    icon: <BarChartOutlined />,
    label: '交易表现',
  },
  {
    key: 'user',
    icon: <UserOutlined />,
    label: '用户表现',
  },
  {
    key: 'activity',
    icon: <DashboardOutlined />,
    label: '活动表现',
  },
  {
    key: 'rtb',
    icon: <RadarChartOutlined />,
    label: 'RTB表现',
  },
  {
    key: 'supply',
    icon: <LineChartOutlined />,
    label: '供给表现',
  },
];

// 计算默认选中的周（当前时间所在周的上一周）
const getDefaultWeekDayjs = () => {
  const now = dayjs();
  // 直接获取上一周的dayjs对象
  return now.subtract(1, 'week').startOf('isoWeek');
};

function App() {
  const [selectedWeekDayjs, setSelectedWeekDayjs] = useState(getDefaultWeekDayjs()); // 默认选择上一周
  const [activeModule, setActiveModule] = useState('trading');
  const [brandParam, setBrandParam] = useState(null); // 初始值设为null，表示还未初始化
  const [isInitialized, setIsInitialized] = useState(false); // 添加初始化状态
  const [aiInsights, setAiInsights] = useState(''); // AI洞察内容状态

  // 从URL参数获取品牌信息
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const brand = urlParams.get('brand') || '';
    setBrandParam(brand);
    setIsInitialized(true); // 标记已初始化

    // 在开发环境中显示配置信息
    if (process.env.NODE_ENV === 'development') {
      logConfigInfo();
    }
  }, []);

  const handleWeekChange = (weekDayjs) => {
    setSelectedWeekDayjs(weekDayjs);
  };

  // 为了保持与现有组件的兼容性，提取周数和完整周格式
  const selectedWeek = selectedWeekDayjs.isoWeek();
  const selectedWeekFormat = `${selectedWeekDayjs.year()}-W${selectedWeekDayjs.isoWeek().toString().padStart(2, '0')}`;

  const handleMenuClick = (e) => {
    setActiveModule(e.key);
  };

  const renderContent = () => {
    // 如果还未初始化品牌参数，显示加载状态
    if (!isInitialized) {
      return <div style={{ padding: '50px', textAlign: 'center' }}>正在加载...</div>;
    }

    switch (activeModule) {
      case 'trading':
        return <TradingDataModule
          selectedWeek={selectedWeek}
          brand={brandParam}
          onAiInsightsChange={setAiInsights}
        />;
      case 'activity':
        return <ActivityDataModule selectedWeek={selectedWeekFormat} brand={brandParam} />;
      case 'rtb':
        return <RTBDataModule selectedWeek={selectedWeek} brand={brandParam} />;
      case 'supply':
        return <SupplyDataModule selectedWeek={selectedWeek} selectedBrand={brandParam} />;
      case 'user':
        return <UserDataModule selectedWeek={selectedWeek} brand={brandParam} />;
      default:
        return <TradingDataModule
          selectedWeek={selectedWeek}
          brand={brandParam}
          onAiInsightsChange={setAiInsights}
        />;
    }
  };

  return (
    <div className="app-container">
      <HeaderToolbar
        selectedWeekMoment={selectedWeekDayjs}
        onWeekChange={handleWeekChange}
        brand={brandParam || ''}
        aiInsights={aiInsights}
        activeModule={activeModule}
      />
      <Layout className="main-layout">
        <Sider width={200} className="sidebar">
          <div style={{ padding: '16px 0' }}>
            <h3 style={{ 
              textAlign: 'center', 
              margin: '0 0 16px 0', 
              color: '#262626',
              fontSize: '16px',
              fontWeight: 600
            }}>
              数据领域
            </h3>
            <Menu
              mode="inline"
              selectedKeys={[activeModule]}
              items={menuItems}
              onClick={handleMenuClick}
              style={{ borderRight: 0 }}
            />
          </div>
        </Sider>
        <Content className="content-area">
          {renderContent()}
        </Content>
      </Layout>
    </div>
  );
}

export default App; 