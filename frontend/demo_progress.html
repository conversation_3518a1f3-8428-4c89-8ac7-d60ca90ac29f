<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI洞察进度条演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .demo-card {
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            margin-bottom: 20px;
            padding: 20px;
        }
        .progress-container {
            margin-bottom: 16px;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 8px;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #108ee9 0%, #87d068 100%);
            transition: width 0.3s ease;
            border-radius: 4px;
        }
        .progress-text {
            text-align: center;
            color: #1890ff;
            font-size: 14px;
            margin-bottom: 8px;
            font-weight: 500;
        }
        .progress-detail {
            text-align: center;
            font-size: 12px;
            color: #666;
        }
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        .btn:hover {
            background: #40a9ff;
        }
        .btn:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .step-list {
            background: #f9f9f9;
            padding: 16px;
            border-radius: 6px;
            margin-top: 20px;
        }
        .step-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e8e8e8;
        }
        .step-item:last-child {
            border-bottom: none;
        }
        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .step-number.active {
            background: #1890ff;
            color: white;
        }
        .step-number.completed {
            background: #52c41a;
            color: white;
        }
        .step-content {
            flex: 1;
        }
        .step-title {
            font-weight: 500;
            margin-bottom: 4px;
        }
        .step-desc {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 AI洞察进度条演示</h1>
        <p>模拟AI洞察生成过程中的进度显示效果</p>
        
        <div class="demo-card">
            <h3>📊 进度条效果</h3>
            <div class="progress-text" id="progressText">点击开始按钮查看进度效果</div>
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 0%;"></div>
                </div>
                <div class="progress-detail">等待开始...</div>
            </div>
            
            <button class="btn" onclick="startDemo()" id="startBtn">开始演示</button>
            <button class="btn" onclick="resetDemo()" id="resetBtn">重置</button>
        </div>
        
        <div class="demo-card">
            <h3>📋 进度步骤说明</h3>
            <div class="step-list" id="stepList">
                <div class="step-item">
                    <div class="step-number" id="step1">1</div>
                    <div class="step-content">
                        <div class="step-title">初始化 (0-10%)</div>
                        <div class="step-desc">正在初始化AI洞察生成任务...</div>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-number" id="step2">2</div>
                    <div class="step-content">
                        <div class="step-title">收集交易数据 (10-20%)</div>
                        <div class="step-desc">正在收集GMV、平台分布、维度表现等交易数据...</div>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-number" id="step3">3</div>
                    <div class="step-content">
                        <div class="step-title">收集活动数据 (20-35%)</div>
                        <div class="step-desc">正在收集活动GMV、活动占比、费比等活动数据...</div>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-number" id="step4">4</div>
                    <div class="step-content">
                        <div class="step-title">收集RTB和供给数据 (35-50%)</div>
                        <div class="step-desc">正在收集RTB投放成本、供给门店数据...</div>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-number" id="step5">5</div>
                    <div class="step-content">
                        <div class="step-title">收集用户数据 (50-65%)</div>
                        <div class="step-desc">正在收集新老用户、ARPU、用户增长等数据...</div>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-number" id="step6">6</div>
                    <div class="step-content">
                        <div class="step-title">数据收集完成 (65-80%)</div>
                        <div class="step-desc">所有业务数据收集完成，准备进行AI分析...</div>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-number" id="step7">7</div>
                    <div class="step-content">
                        <div class="step-title">AI智能分析 (80-100%)</div>
                        <div class="step-desc">使用DeepSeek大模型进行智能分析和洞察生成...</div>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-number" id="step8">8</div>
                    <div class="step-content">
                        <div class="step-title">完成 (100%)</div>
                        <div class="step-desc">AI洞察生成完成，可以查看和编辑结果</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const steps = [
            { progress: 10, message: '正在初始化...', stepId: 'step1' },
            { progress: 20, message: '正在收集交易数据...', stepId: 'step2' },
            { progress: 35, message: '正在收集活动数据...', stepId: 'step3' },
            { progress: 50, message: '正在收集RTB投放和供给数据...', stepId: 'step4' },
            { progress: 65, message: '正在收集用户数据...', stepId: 'step5' },
            { progress: 80, message: '数据收集完成，准备AI分析...', stepId: 'step6' },
            { progress: 95, message: '正在进行AI智能分析...', stepId: 'step7' },
            { progress: 100, message: 'AI洞察生成完成！', stepId: 'step8' }
        ];
        
        let currentStep = 0;
        let demoInterval = null;
        
        function updateProgress(percent, message) {
            document.getElementById('progressFill').style.width = percent + '%';
            document.getElementById('progressText').textContent = message;
        }
        
        function updateStepStatus(stepId, status) {
            const stepElement = document.getElementById(stepId);
            stepElement.className = 'step-number ' + status;
        }
        
        function resetSteps() {
            for (let i = 1; i <= 8; i++) {
                document.getElementById(`step${i}`).className = 'step-number';
            }
        }
        
        function startDemo() {
            if (demoInterval) {
                clearInterval(demoInterval);
            }
            
            document.getElementById('startBtn').disabled = true;
            currentStep = 0;
            resetSteps();
            
            demoInterval = setInterval(() => {
                if (currentStep < steps.length) {
                    const step = steps[currentStep];
                    updateProgress(step.progress, step.message);
                    
                    // 更新步骤状态
                    updateStepStatus(step.stepId, 'active');
                    if (currentStep > 0) {
                        updateStepStatus(steps[currentStep - 1].stepId, 'completed');
                    }
                    
                    currentStep++;
                    
                    if (currentStep >= steps.length) {
                        clearInterval(demoInterval);
                        updateStepStatus(step.stepId, 'completed');
                        document.getElementById('startBtn').disabled = false;
                    }
                } else {
                    clearInterval(demoInterval);
                    document.getElementById('startBtn').disabled = false;
                }
            }, 1500); // 每1.5秒更新一次，模拟真实进度
        }
        
        function resetDemo() {
            if (demoInterval) {
                clearInterval(demoInterval);
            }
            
            currentStep = 0;
            updateProgress(0, '点击开始按钮查看进度效果');
            resetSteps();
            document.getElementById('startBtn').disabled = false;
        }
    </script>
</body>
</html>
