[{"column_name": "platform", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "date", "data_type": "date", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "province", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "city", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "brand", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "sub_brand", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "gmv", "data_type": "numeric", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "sales_volume", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "ds", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "store_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "standard_city", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "area", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "upc", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "collect_upc", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "product_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "category1", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "category2", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "collect_vender_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "vender_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "shop_type", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "tar_gmv", "data_type": "numeric", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "branch_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "arrival_type", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "business_type", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "collect_product_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "qli", "data_type": "numeric", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "shop_type_1", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "total_qli", "data_type": "numeric", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "industry_type", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "rsp_gmv", "data_type": "numeric", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "bu", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "standard_city1", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "standard_city_code", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "standard_province", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "standard_province_code", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "tier1", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "tier2", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "tier3", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "gts_gmv", "data_type": "numeric", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "brand_product_id", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "sub_class", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "category3", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "collect_province", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "date_year", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "date_year_month", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "date_month", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "date_year_month_sep", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "date_month_day", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "date_month_day_sep", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "date_day", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "product_line", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "product_type", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "product_gts", "data_type": "numeric", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "product_gts_gmv", "data_type": "numeric", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "brand_price", "data_type": "numeric", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "brand_gmv", "data_type": "numeric", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "is_sdc", "data_type": "integer", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "size", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "lowest_price", "data_type": "numeric", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "highest_price", "data_type": "numeric", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "promotion_price", "data_type": "numeric", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "flavor", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "launch_date", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "product_role", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "packaging", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "is_main_push_product", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "sub_sidiary", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}, {"column_name": "brand_filter_condition", "data_type": "text", "is_nullable": "YES", "column_default": null, "character_maximum_length": null}]