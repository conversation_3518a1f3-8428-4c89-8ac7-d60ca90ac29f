<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>平台筛选样式对比测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .platform-filters {
            margin-bottom: 20px;
        }
        .platform-radio-group {
            display: flex;
            gap: 0;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            overflow: hidden;
        }
        /* 单平台模式样式 */
        .platform-radio-group.single-platform {
            width: auto;
            justify-content: flex-start;
        }
        .platform-radio-button {
            flex: 1;
            padding: 8px 16px;
            text-align: center;
            background: white;
            border: none;
            border-right: 1px solid #d9d9d9;
            cursor: pointer;
            transition: all 0.3s;
        }
        /* 单平台模式下的按钮样式 */
        .platform-radio-group.single-platform .platform-radio-button {
            flex: none;
            width: calc(100% / 6);
            min-width: calc(100% / 6);
        }
        .platform-radio-button:last-child {
            border-right: none;
        }
        .platform-radio-button:hover {
            background: #f0f0f0;
        }
        .platform-radio-button.active {
            background: #1890ff;
            color: white;
        }
        .description {
            margin-top: 10px;
            padding: 10px;
            background: #f6f8fa;
            border-radius: 4px;
            font-size: 14px;
            color: #666;
        }
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>平台筛选样式对比测试</h1>
        
        <!-- 活动表现页面 - 完整模式 -->
        <div class="section">
            <div class="section-title">活动表现页面 - 完整模式（原始宽度）</div>
            <div class="platform-filters">
                <div class="platform-radio-group" id="fullPlatformGroup">
                    <button class="platform-radio-button active" data-value="全平台">全平台</button>
                    <button class="platform-radio-button" data-value="美团">美团</button>
                    <button class="platform-radio-button" data-value="饿了么">饿了么</button>
                    <button class="platform-radio-button" data-value="京东到家">京东到家</button>
                    <button class="platform-radio-button" data-value="多点">多点</button>
                    <button class="platform-radio-button" data-value="淘鲜达">淘鲜达</button>
                </div>
            </div>
            <div class="description">
                ✅ 完整模式：显示所有6个平台选项，按钮平均分配宽度，占满整行
            </div>
        </div>

        <!-- 单平台模式对比 -->
        <div class="section">
            <div class="section-title">其他页面 - 单平台模式（1/6宽度，靠左展示）</div>
            <div class="platform-filters">
                <div class="platform-radio-group single-platform" id="singlePlatformGroup">
                    <button class="platform-radio-button active" data-value="美团">美团</button>
                </div>
            </div>
            <div class="description">
                ✅ 单平台模式：仅显示美团选项，按钮宽度与交易表现页面单个按钮宽度一致
            </div>
            <div class="highlight">
                <strong>样式特点：</strong>
                <ul>
                    <li>容器宽度：auto（自适应）</li>
                    <li>按钮宽度：calc(100% / 6) ≈ 16.67%（与交易表现页面单个按钮宽度一致）</li>
                    <li>对齐方式：靠左展示（justify-content: flex-start）</li>
                    <li>适用页面：用户表现、RTB表现、供给表现</li>
                </ul>
            </div>
        </div>

        <!-- 宽度对比演示 -->
        <div class="section">
            <div class="section-title">宽度对比演示</div>
            
            <div style="margin-bottom: 20px;">
                <div style="font-weight: bold; margin-bottom: 10px;">参考线（完整宽度分为6等份）：</div>
                <div style="display: flex; height: 30px; border: 1px solid #ccc;">
                    <div style="flex: 1; background: #f0f0f0; border-right: 1px solid #ccc; display: flex; align-items: center; justify-content: center; font-size: 12px;">1/6</div>
                    <div style="flex: 1; background: #e0e0e0; border-right: 1px solid #ccc; display: flex; align-items: center; justify-content: center; font-size: 12px;">2/6</div>
                    <div style="flex: 1; background: #f0f0f0; border-right: 1px solid #ccc; display: flex; align-items: center; justify-content: center; font-size: 12px;">3/6</div>
                    <div style="flex: 1; background: #e0e0e0; border-right: 1px solid #ccc; display: flex; align-items: center; justify-content: center; font-size: 12px;">4/6</div>
                    <div style="flex: 1; background: #f0f0f0; border-right: 1px solid #ccc; display: flex; align-items: center; justify-content: center; font-size: 12px;">5/6</div>
                    <div style="flex: 1; background: #e0e0e0; display: flex; align-items: center; justify-content: center; font-size: 12px;">6/6</div>
                </div>
            </div>

            <div style="margin-bottom: 10px;">
                <div style="font-weight: bold; margin-bottom: 10px;">单平台模式实际效果：</div>
                <div class="platform-filters">
                    <div class="platform-radio-group single-platform">
                        <button class="platform-radio-button active" data-value="美团">美团</button>
                    </div>
                </div>
            </div>
            
            <div class="description">
                可以看到单平台模式的按钮宽度与交易表现页面中单个按钮宽度完全一致，并且靠左对齐
            </div>
        </div>

        <!-- 实际应用场景 -->
        <div class="section">
            <div class="section-title">实际应用场景</div>
            
            <div style="margin-bottom: 20px;">
                <h4>用户表现页面</h4>
                <div class="platform-filters">
                    <div class="platform-radio-group single-platform">
                        <button class="platform-radio-button active" data-value="美团">美团</button>
                    </div>
                </div>
            </div>

            <div style="margin-bottom: 20px;">
                <h4>RTB表现页面</h4>
                <div class="platform-filters">
                    <div class="platform-radio-group single-platform">
                        <button class="platform-radio-button active" data-value="美团">美团</button>
                    </div>
                </div>
            </div>

            <div style="margin-bottom: 20px;">
                <h4>供给表现页面</h4>
                <div class="platform-filters">
                    <div class="platform-radio-group single-platform">
                        <button class="platform-radio-button active" data-value="美团">美团</button>
                    </div>
                </div>
            </div>

            <div class="description">
                所有单平台页面都使用相同的样式，保持UI一致性
            </div>
        </div>
    </div>

    <script>
        // 平台筛选交互逻辑
        document.querySelectorAll('.platform-radio-group').forEach(group => {
            group.addEventListener('click', (e) => {
                if (e.target.classList.contains('platform-radio-button')) {
                    // 移除同组内其他按钮的active状态
                    group.querySelectorAll('.platform-radio-button').forEach(btn => {
                        btn.classList.remove('active');
                    });
                    // 添加当前按钮的active状态
                    e.target.classList.add('active');
                    
                    console.log(`选择平台: ${e.target.dataset.value}`);
                }
            });
        });
    </script>
</body>
</html>
