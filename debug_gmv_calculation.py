#!/usr/bin/env python3
"""
调试供给表现GMV计算逻辑
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from real_data_queries import get_week_date_range, get_current_week_number
from database import execute_query
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_gmv_calculation(week=28, brand=''):
    """调试GMV计算过程"""
    print(f"🔍 调试供给表现GMV计算 - 第{week}周, 品牌: '{brand}'")
    
    # 1. 计算日期范围
    current_year = datetime.now().year
    week_start, week_end = get_week_date_range(current_year, week)
    
    # 获取整周日期范围（GMV用）
    week_start_yyyymmdd = week_start.replace('-', '')
    week_end_yyyymmdd = week_end.replace('-', '')
    
    print(f"📅 日期范围: {week_start} 到 {week_end}")
    print(f"📅 YYYYMMDD格式: {week_start_yyyymmdd} 到 {week_end_yyyymmdd}")
    
    # 2. 构建品牌过滤条件
    gmv_brand_filter = ""
    if brand and brand.strip():
        gmv_brand_filter = f"AND brand = '{brand.strip()}'"
    
    print(f"🏷️ 品牌过滤条件: {gmv_brand_filter if gmv_brand_filter else '无品牌过滤'}")
    
    # 3. 构建完整的SQL查询
    gmv_sql = f"""
    SELECT COALESCE(SUM(gmv), 0) as current_gmv
    FROM dws_o2o_sale_activity_detail_analysis_d
    WHERE ds BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}'
    AND source = '全量'
    AND platform = '美团'
    {gmv_brand_filter}
    """
    
    print(f"\n📝 执行的SQL查询:")
    print("=" * 80)
    print(gmv_sql)
    print("=" * 80)
    
    # 4. 执行查询并显示结果
    try:
        result = execute_query(gmv_sql)
        if result:
            current_gmv = float(result[0]['current_gmv'])
            print(f"\n💰 查询结果:")
            print(f"   GMV总额: {current_gmv:,.2f}")
            print(f"   GMV总额(万元): {current_gmv/10000:,.2f}")
            
            # 5. 分析数据合理性
            print(f"\n📊 数据分析:")
            if current_gmv > 100000000:  # 超过1亿
                print(f"   ⚠️  数据异常大 - GMV超过1亿元")
            elif current_gmv > 10000000:  # 超过1千万
                print(f"   ⚠️  数据较大 - GMV超过1千万元")
            else:
                print(f"   ✅ 数据范围正常")
                
        else:
            print("❌ 查询无结果")
            
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        
    # 6. 检查数据表的基本信息
    print(f"\n🔍 检查数据表基本信息:")
    try:
        # 检查表中的数据量
        count_sql = f"""
        SELECT 
            COUNT(*) as total_records,
            COUNT(DISTINCT ds) as date_count,
            MIN(ds) as min_date,
            MAX(ds) as max_date,
            SUM(gmv) as total_gmv
        FROM dws_o2o_sale_activity_detail_analysis_d
        WHERE source = '全量'
        AND platform = '美团'
        {gmv_brand_filter}
        """
        
        count_result = execute_query(count_sql)
        if count_result:
            info = count_result[0]
            print(f"   总记录数: {info['total_records']:,}")
            print(f"   日期数量: {info['date_count']:,}")
            print(f"   日期范围: {info['min_date']} 到 {info['max_date']}")
            print(f"   总GMV: {float(info['total_gmv']):,.2f}")
            
        # 检查指定周的数据分布
        detail_sql = f"""
        SELECT 
            ds,
            COUNT(*) as record_count,
            SUM(gmv) as daily_gmv
        FROM dws_o2o_sale_activity_detail_analysis_d
        WHERE ds BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}'
        AND source = '全量'
        AND platform = '美团'
        {gmv_brand_filter}
        GROUP BY ds
        ORDER BY ds
        """
        
        detail_result = execute_query(detail_sql)
        if detail_result:
            print(f"\n📈 第{week}周每日数据分布:")
            total_week_gmv = 0
            for row in detail_result:
                daily_gmv = float(row['daily_gmv'])
                total_week_gmv += daily_gmv
                print(f"   {row['ds']}: {row['record_count']:,} 条记录, GMV: {daily_gmv:,.2f}")
            print(f"   周总计: {total_week_gmv:,.2f}")
            
    except Exception as e:
        print(f"❌ 检查表信息失败: {e}")

def compare_with_other_weeks():
    """对比其他周的数据"""
    print(f"\n🔍 对比最近几周的GMV数据:")
    
    current_week = get_current_week_number()
    current_year = datetime.now().year
    
    for i in range(5):  # 检查最近5周
        week = current_week - 1 - i
        if week <= 0:
            week += 52
            year = current_year - 1
        else:
            year = current_year
            
        try:
            week_start, week_end = get_week_date_range(year, week)
            week_start_yyyymmdd = week_start.replace('-', '')
            week_end_yyyymmdd = week_end.replace('-', '')
            
            sql = f"""
            SELECT COALESCE(SUM(gmv), 0) as weekly_gmv
            FROM dws_o2o_sale_activity_detail_analysis_d
            WHERE ds BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}'
            AND source = '全量'
            AND platform = '美团'
            """
            
            result = execute_query(sql)
            if result:
                weekly_gmv = float(result[0]['weekly_gmv'])
                print(f"   第{week}周 ({week_start} 到 {week_end}): {weekly_gmv:,.2f}")
                
        except Exception as e:
            print(f"   第{week}周: 查询失败 - {e}")

def main():
    """主函数"""
    print("🚀 开始调试供给表现GMV计算\n")
    
    # 1. 调试无品牌过滤的情况
    debug_gmv_calculation(week=28, brand='')
    
    # 2. 调试有品牌过滤的情况
    print("\n" + "="*100 + "\n")
    debug_gmv_calculation(week=28, brand='圣农')
    
    # 3. 对比其他周的数据
    print("\n" + "="*100 + "\n")
    compare_with_other_weeks()

if __name__ == "__main__":
    main()
