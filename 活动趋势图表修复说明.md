# 活动趋势图表修复说明

## 问题描述
在活动表现模块中，当用户切换"活动数据概览"中的不同卡片时，下方的"活动GMV趋势分析"图表始终显示活动GMV和活动GMV年同比的数据，没有根据选中的卡片动态切换显示对应的指标。

## 问题根因
`ActivityInteractiveTrendChart` 组件中的数据处理逻辑硬编码了活动GMV相关的数据字段，没有根据 `selectedMetric` 参数动态选择要显示的数据。

## 修复方案

### 1. 添加动态配置函数
在组件中添加了 `getMetricConfig` 函数，根据 `selectedMetric` 参数返回对应指标的配置信息：

```javascript
const getMetricConfig = (metric) => {
  const configs = {
    'activity_gmv': {
      title: '活动GMV趋势分析',
      dataKey: 'activity_gmv',
      yoyKey: 'activity_gmv_yoy',
      seriesName: '活动GMV',
      yoySeriesName: '活动GMV年同比',
      yAxisName: '活动GMV (元)',
      unit: '元',
      isPercentage: false
    },
    'activity_gmv_ratio': {
      title: '活动GMV占比趋势分析',
      dataKey: 'activity_gmv_ratio',
      yoyKey: 'activity_gmv_ratio_yoy',
      seriesName: '活动GMV占比',
      yoySeriesName: '活动GMV占比年同比',
      yAxisName: '活动GMV占比 (%)',
      unit: '%',
      isPercentage: true
    },
    // ... 其他指标配置
  };
  return configs[metric] || configs['activity_gmv'];
};
```

### 2. 支持的指标类型
修复后支持以下5种指标的动态切换：

1. **activity_gmv** - 活动GMV（金额类型）
2. **activity_gmv_ratio** - 活动GMV占比（百分比类型）
3. **verification_amount** - 核销金额（金额类型）
4. **activity_cost_ratio** - 活动费比（百分比类型）
5. **total_cost_ratio** - 全量费比（百分比类型）

### 3. 动态数据处理
根据指标类型动态处理数据：

```javascript
// 根据配置处理数据
const mainData = trendData.map(item => {
  const value = item[config.dataKey] || 0;
  return config.isPercentage ? (value * 100).toFixed(2) : Math.round(value);
});

// 处理年同比数据
const yoyData = trendData.map(item => (item[config.yoyKey] * 100).toFixed(2));
```

### 4. 动态图表配置
图表的标题、图例、Y轴标签、提示框等都会根据选中的指标动态更新：

- **图表标题**：根据指标类型显示对应的标题
- **Y轴标签**：金额类型显示千分位格式，百分比类型显示百分号
- **图例名称**：显示对应指标的名称
- **提示框格式**：根据数据类型格式化显示

## 修复文件
- `frontend/src/components/ActivityInteractiveTrendChart.js`

## 测试验证
创建了测试文件 `test_activity_chart_fix.html` 来验证修复效果：

1. 模拟了5个活动数据概览卡片
2. 点击不同卡片时，图表会动态切换显示对应的指标数据
3. 图表标题、Y轴标签、图例等都会相应更新

## 预期效果
修复后，用户在活动表现模块中：

1. 点击"活动GMV"卡片 → 图表显示活动GMV趋势
2. 点击"活动GMV占比"卡片 → 图表显示活动GMV占比趋势
3. 点击"核销金额"卡片 → 图表显示核销金额趋势
4. 点击"活动费比"卡片 → 图表显示活动费比趋势
5. 点击"全量费比"卡片 → 图表显示全量费比趋势

每个指标都会显示对应的数值和年同比数据，图表标题和格式也会相应调整。

## 注意事项
1. 修复保持了原有的图表样式和交互逻辑
2. 百分比类型的数据会自动转换为百分比格式显示
3. 金额类型的数据会使用千分位分隔符格式化
4. 年同比数据统一显示为百分比格式
5. 如果传入未知的指标类型，会默认显示活动GMV数据
