#!/usr/bin/env python3
"""
调试子品牌维度的数据问题
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from database import execute_query
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_sub_brand_supply_data():
    """调试子品牌的供给数据"""
    print("🔍 调试子品牌的供给数据...")
    
    # 获取第28周的周六日期
    from datetime import datetime, timedelta
    from real_data_queries import get_week_date_range
    
    week_start, week_end = get_week_date_range(2025, 28)
    week_start_date = datetime.strptime(week_start, '%Y-%m-%d')
    saturday_date = week_start_date + timedelta(days=5)  # 周六
    saturday_yyyymmdd = saturday_date.strftime('%Y%m%d')
    
    print(f"第28周周六日期: {saturday_yyyymmdd}")
    
    # 1. 检查子品牌供给表的数据
    print("\n1. 检查子品牌供给表的数据:")
    try:
        sql = f"""
        SELECT 
            collect_sub_brand,
            onsale_poi_num,
            poi_permeate_rate,
            poi_sold_rate,
            store_avg_on_sales_sku,
            sku_sold_out_rate
        FROM dws_mt_channel_city_details_daily_data_attribute_d
        WHERE ds = '{saturday_yyyymmdd}'
        AND platform = '美团'
        AND collect_brand = '圣农'
        AND collect_sub_brand IS NOT NULL
        LIMIT 5
        """
        
        result = execute_query(sql)
        if result:
            print(f"✅ 找到 {len(result)} 条子品牌供给数据:")
            for row in result:
                print(f"   子品牌: {row['collect_sub_brand']}")
                print(f"   店铺数: {row['onsale_poi_num']}")
                print(f"   渗透率: {row['poi_permeate_rate']}")
                print(f"   活跃率: {row['poi_sold_rate']}")
                print(f"   平均SKU: {row['store_avg_on_sales_sku']}")
                print(f"   缺货率: {row['sku_sold_out_rate']}")
                print("   ---")
        else:
            print("❌ 未找到子品牌供给数据")
            
    except Exception as e:
        print(f"❌ 查询子品牌供给数据失败: {e}")

def debug_sub_brand_gmv_data():
    """调试子品牌的GMV数据"""
    print("\n2. 检查子品牌GMV数据:")
    
    # 获取第28周的日期范围
    from real_data_queries import get_week_date_range
    
    week_start, week_end = get_week_date_range(2025, 28)
    week_start_yyyymmdd = week_start.replace('-', '')
    week_end_yyyymmdd = week_end.replace('-', '')
    
    print(f"第28周日期范围: {week_start_yyyymmdd} - {week_end_yyyymmdd}")
    
    try:
        sql = f"""
        SELECT 
            sub_brand,
            SUM(gmv) as total_gmv,
            COUNT(*) as record_count
        FROM dws_o2o_sale_activity_detail_analysis_d
        WHERE ds BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}'
        AND source = '全量'
        AND platform = '美团'
        AND brand = '圣农'
        AND sub_brand IS NOT NULL
        GROUP BY sub_brand
        ORDER BY SUM(gmv) DESC
        """
        
        result = execute_query(sql)
        if result:
            print(f"✅ 找到 {len(result)} 个子品牌GMV数据:")
            for row in result:
                print(f"   子品牌: {row['sub_brand']}")
                print(f"   GMV: {row['total_gmv']:,.2f}")
                print(f"   记录数: {row['record_count']}")
                print("   ---")
        else:
            print("❌ 未找到子品牌GMV数据")
            
    except Exception as e:
        print(f"❌ 查询子品牌GMV数据失败: {e}")

def debug_sub_brand_field_mapping():
    """调试子品牌字段映射问题"""
    print("\n3. 检查子品牌字段映射:")
    
    # 检查GMV表和供给表的子品牌字段是否匹配
    try:
        # GMV表的子品牌
        gmv_sql = """
        SELECT DISTINCT sub_brand
        FROM dws_o2o_sale_activity_detail_analysis_d
        WHERE brand = '圣农'
        AND sub_brand IS NOT NULL
        LIMIT 10
        """
        
        gmv_result = execute_query(gmv_sql)
        print("GMV表中的子品牌:")
        if gmv_result:
            for row in gmv_result:
                print(f"   - {row['sub_brand']}")
        else:
            print("   无数据")
        
        # 供给表的子品牌
        supply_sql = """
        SELECT DISTINCT collect_sub_brand
        FROM dws_mt_channel_city_details_daily_data_attribute_d
        WHERE collect_brand = '圣农'
        AND collect_sub_brand IS NOT NULL
        LIMIT 10
        """
        
        supply_result = execute_query(supply_sql)
        print("\n供给表中的子品牌:")
        if supply_result:
            for row in supply_result:
                print(f"   - {row['collect_sub_brand']}")
        else:
            print("   无数据")
            
        # 检查是否有匹配的子品牌
        if gmv_result and supply_result:
            gmv_brands = {row['sub_brand'] for row in gmv_result}
            supply_brands = {row['collect_sub_brand'] for row in supply_result}
            common_brands = gmv_brands & supply_brands
            
            print(f"\n匹配的子品牌: {common_brands}")
            if not common_brands:
                print("❌ GMV表和供给表的子品牌字段不匹配！")
            else:
                print("✅ 找到匹配的子品牌")
                
    except Exception as e:
        print(f"❌ 检查子品牌字段映射失败: {e}")

def test_current_sub_brand_query():
    """测试当前的子品牌查询逻辑"""
    print("\n4. 测试当前的子品牌查询逻辑:")
    
    from real_data_queries import get_supply_detail_real
    
    try:
        result = get_supply_detail_real(dimension_type='子品牌', brand='圣农', selected_week=28)
        
        print(f"查询结果: {result}")
        
        if result.get('data_source') == 'error':
            print(f"❌ 查询失败: {result.get('error')}")
        else:
            data = result.get('data', [])
            print(f"✅ 查询成功，获取到 {len(data)} 条数据")
            
            for item in data:
                print(f"   维度值: {item.get('dimension_value')}")
                print(f"   GMV: {item.get('gmv', 0):,.2f}")
                print(f"   店铺数: {item.get('store_count', 0)}")
                print(f"   渗透率: {item.get('store_penetration', 0):.2%}")
                print("   ---")
                
    except Exception as e:
        print(f"❌ 测试当前查询逻辑失败: {e}")

def main():
    """主函数"""
    print("🚀 开始调试子品牌维度的数据问题\n")
    
    debug_sub_brand_supply_data()
    debug_sub_brand_gmv_data()
    debug_sub_brand_field_mapping()
    test_current_sub_brand_query()
    
    print("\n🎉 调试完成！")

if __name__ == "__main__":
    main()
