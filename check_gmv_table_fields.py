#!/usr/bin/env python3
"""
检查GMV表的字段结构，特别是维度字段
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from database import execute_query
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_gmv_table_fields():
    """检查GMV表的字段结构"""
    print("🔍 检查GMV表 dws_o2o_sale_activity_detail_analysis_d 的字段结构...")
    
    try:
        # 检查表结构
        sql = """
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'dws_o2o_sale_activity_detail_analysis_d'
        ORDER BY column_name
        """
        
        result = execute_query(sql)
        if result:
            print(f"✅ GMV表包含 {len(result)} 个字段:")
            for row in result:
                print(f"   - {row['column_name']}: {row['data_type']}")
        else:
            print("❌ GMV表不存在或无字段")
            
    except Exception as e:
        print(f"❌ 检查GMV表失败: {e}")

def check_gmv_sample_data():
    """检查GMV表的示例数据"""
    print("\n📊 检查GMV表的示例数据...")
    
    try:
        sql = """
        SELECT * 
        FROM dws_o2o_sale_activity_detail_analysis_d
        WHERE brand = '圣农'
        AND platform = '美团'
        AND source = '全量'
        LIMIT 3
        """
        
        result = execute_query(sql)
        if result:
            print(f"✅ GMV表示例数据 ({len(result)} 条):")
            for i, row in enumerate(result):
                print(f"   记录 {i+1}:")
                for key, value in row.items():
                    if value is not None:
                        print(f"     {key}: {value}")
        else:
            print("❌ GMV表无数据或查询失败")
            
    except Exception as e:
        print(f"❌ 检查GMV表数据失败: {e}")

def test_dimension_queries():
    """测试各维度的查询"""
    print("\n🔍 测试各维度的查询...")
    
    # 测试渠道维度
    print("\n1. 测试渠道维度查询:")
    try:
        sql = """
        SELECT DISTINCT channel_name
        FROM dws_o2o_sale_activity_detail_analysis_d
        WHERE brand = '圣农'
        AND platform = '美团'
        AND source = '全量'
        AND channel_name IS NOT NULL
        LIMIT 5
        """
        result = execute_query(sql)
        if result:
            print(f"✅ 找到 {len(result)} 个渠道:")
            for row in result:
                print(f"   - {row['channel_name']}")
        else:
            print("❌ 未找到渠道数据")
    except Exception as e:
        print(f"❌ 渠道查询失败: {e}")
    
    # 测试商品维度
    print("\n2. 测试商品维度查询:")
    try:
        sql = """
        SELECT DISTINCT upc
        FROM dws_o2o_sale_activity_detail_analysis_d
        WHERE brand = '圣农'
        AND platform = '美团'
        AND source = '全量'
        AND upc IS NOT NULL
        LIMIT 5
        """
        result = execute_query(sql)
        if result:
            print(f"✅ 找到 {len(result)} 个商品:")
            for row in result:
                print(f"   - {row['upc']}")
        else:
            print("❌ 未找到商品数据")
    except Exception as e:
        print(f"❌ 商品查询失败: {e}")
    
    # 测试子品牌维度
    print("\n3. 测试子品牌维度查询:")
    try:
        sql = """
        SELECT DISTINCT sub_brand
        FROM dws_o2o_sale_activity_detail_analysis_d
        WHERE brand = '圣农'
        AND platform = '美团'
        AND source = '全量'
        AND sub_brand IS NOT NULL
        LIMIT 5
        """
        result = execute_query(sql)
        if result:
            print(f"✅ 找到 {len(result)} 个子品牌:")
            for row in result:
                print(f"   - {row['sub_brand']}")
        else:
            print("❌ 未找到子品牌数据")
    except Exception as e:
        print(f"❌ 子品牌查询失败: {e}")

def main():
    """主函数"""
    print("🚀 开始检查GMV表的维度字段\n")
    
    check_gmv_table_fields()
    check_gmv_sample_data()
    test_dimension_queries()

if __name__ == "__main__":
    main()
