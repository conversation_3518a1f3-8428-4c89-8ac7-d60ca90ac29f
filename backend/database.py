import psycopg2
import psycopg2.extras
from contextlib import contextmanager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Hologres连接配置
HOLOGRES_CONFIG = {
    'host': "hgpostcn-cn-wwo3iu4fu008-cn-shanghai.hologres.aliyuncs.com",
    'port': 80,
    'dbname': "hologres_prd",
    'user': "LTAI5tJiSFcRXENKTP7NdVbg",
    # 'user': 'LTAI5tEtJCVLA4Dq6m3MoZko',
    'password': "******************************",
    # 'password': '******************************',
    'keepalives': 1,
    'keepalives_idle': 130,
    'keepalives_interval': 10,
    'keepalives_count': 15,
}

@contextmanager
def get_hologres_connection():
    """获取Hologres数据库连接的上下文管理器"""
    conn = None
    try:
        conn = psycopg2.connect(**HOLOGRES_CONFIG)
        logger.info("Successfully connected to Hologres")
        yield conn
    except psycopg2.Error as e:
        logger.error(f"Database connection error: {e}")
        raise
    finally:
        if conn:
            conn.close()
            logger.info("Database connection closed")

def execute_query(sql, params=None):
    """执行SQL查询并返回结果"""
    try:
        with get_hologres_connection() as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                # 设置分区限制
                cursor.execute("SET hg_experimental_foreign_table_max_partition_limit = 512")
                cursor.execute(sql, params)
                results = cursor.fetchall()
                logger.info(f"Query executed successfully, returned {len(results)} rows")
                return results
    except psycopg2.Error as e:
        logger.error(f"Query execution error: {e}")
        raise

def get_table_structure(table_name):
    """获取表结构信息"""
    sql = """
    SELECT 
        column_name,
        data_type,
        is_nullable,
        column_default,
        character_maximum_length
    FROM information_schema.columns 
    WHERE table_name = %s 
    ORDER BY ordinal_position;
    """
    try:
        return execute_query(sql, (table_name,))
    except Exception as e:
        logger.error(f"Failed to get table structure for {table_name}: {e}")
        return []

def test_connection():
    """测试数据库连接"""
    try:
        with get_hologres_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                logger.info("Database connection test successful")
                return True
    except Exception as e:
        logger.error(f"Database connection test failed: {e}")
        return False
