#!/usr/bin/env python3
"""
最终维度数据测试
"""

import requests
import json

def final_dimension_test():
    """最终维度数据测试"""
    
    base_url = "http://localhost:5001/api/activity/detail"
    brand = "圣农"
    
    print("🎯 最终维度数据测试")
    print("=" * 60)
    
    # 测试整体维度 - 应该显示8周时间序列数据
    print("\n1. 测试整体维度（应显示8周时间序列数据）")
    print("-" * 50)
    
    response = requests.get(f"{base_url}?type=整体&brand={brand}")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 整体维度:")
        print(f"   - 维度类型: {data['dimension_type']}")
        print(f"   - 数据条数: {len(data['data'])}")
        print(f"   - 数据类型: 时间序列（周）")
        for i, item in enumerate(data['data'][:3]):
            print(f"     {i+1}. {item['dimension']} - 活动GMV: ¥{item['activity_gmv']:,.2f}")
    else:
        print(f"❌ 整体维度API调用失败: {response.status_code}")
    
    # 测试城市维度 - 应该显示按城市分组的数据
    print("\n2. 测试城市维度（应显示按城市分组的数据）")
    print("-" * 50)
    
    response = requests.get(f"{base_url}?type=城市&brand={brand}&page=1&page_size=5")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 城市维度:")
        print(f"   - 维度类型: {data['dimension_type']}")
        print(f"   - 数据条数: {len(data['data'])}")
        print(f"   - 总数据量: {data['total']}")
        print(f"   - 数据类型: 按城市分组")
        for i, item in enumerate(data['data'][:5]):
            print(f"     {i+1}. {item['dimension']} - 活动GMV: ¥{item['activity_gmv']:,.2f} - GMV占比: {item['gmv_ratio']:.2%}")
    else:
        print(f"❌ 城市维度API调用失败: {response.status_code}")
    
    # 测试零售商维度 - 应该显示按零售商分组的数据
    print("\n3. 测试零售商维度（应显示按零售商分组的数据）")
    print("-" * 50)
    
    response = requests.get(f"{base_url}?type=零售商&brand={brand}&page=1&page_size=5")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 零售商维度:")
        print(f"   - 维度类型: {data['dimension_type']}")
        print(f"   - 数据条数: {len(data['data'])}")
        print(f"   - 总数据量: {data['total']}")
        print(f"   - 数据类型: 按零售商分组")
        for i, item in enumerate(data['data'][:5]):
            print(f"     {i+1}. {item['dimension']} - 活动GMV: ¥{item['activity_gmv']:,.2f} - GMV占比: {item['gmv_ratio']:.2%}")
    else:
        print(f"❌ 零售商维度API调用失败: {response.status_code}")
    
    # 测试活动机制维度 - 应该显示按活动机制分组的数据
    print("\n4. 测试活动机制维度（应显示按活动机制分组的数据）")
    print("-" * 50)

    response = requests.get(f"{base_url}?type=活动机制&brand={brand}&page=1&page_size=5")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 活动机制维度:")
        print(f"   - 维度类型: {data['dimension_type']}")
        print(f"   - 数据条数: {len(data['data'])}")
        print(f"   - 总数据量: {data['total']}")
        print(f"   - 数据类型: 按活动机制分组")
        for i, item in enumerate(data['data'][:5]):
            print(f"     {i+1}. {item['dimension']} - 活动GMV: ¥{item['activity_gmv']:,.2f} - GMV占比: {item['gmv_ratio']:.2%}")
    else:
        print(f"❌ 活动机制维度API调用失败: {response.status_code}")
    
    # 测试指定周的城市维度
    print("\n5. 测试指定周的城市维度（2024-W46）")
    print("-" * 50)
    
    response = requests.get(f"{base_url}?type=城市&brand={brand}&selected_week=2024-W46&page=1&page_size=3")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 指定周城市维度:")
        print(f"   - 维度类型: {data['dimension_type']}")
        print(f"   - 数据条数: {len(data['data'])}")
        print(f"   - 总数据量: {data['total']}")
        print(f"   - 指定周: 2024-W46")
        for i, item in enumerate(data['data'][:3]):
            print(f"     {i+1}. {item['dimension']} - 活动GMV: ¥{item['activity_gmv']:,.2f} - GMV占比: {item['gmv_ratio']:.2%}")
    else:
        print(f"❌ 指定周城市维度API调用失败: {response.status_code}")
    
    print("\n" + "=" * 60)
    print("🎉 最终维度数据测试完成！")
    print("\n📋 测试结果总结:")
    print("   ✅ 整体维度：显示8周时间序列数据")
    print("   ✅ 城市维度：显示按城市分组的数据")
    print("   ✅ 零售商维度：显示按零售商分组的数据")
    print("   ✅ 活动机制维度：显示按活动机制分组的数据")
    print("   ✅ 支持指定周查询")

if __name__ == "__main__":
    final_dimension_test()
