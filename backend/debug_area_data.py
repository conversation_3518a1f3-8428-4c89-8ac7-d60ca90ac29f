#!/usr/bin/env python3
"""
调试大区维度数据不一致问题
"""

from database import execute_query
from real_data_queries import (
    get_dimension_analysis_real, 
    get_dimension_trends_real,
    get_current_week_number,
    get_week_date_range
)
from datetime import datetime
import json

def debug_area_data_consistency():
    """调试大区维度数据一致性问题"""
    print("🔍 调试大区维度数据一致性问题...")
    
    # 获取当前周信息
    current_year = datetime.now().year
    current_week = get_current_week_number()
    last_complete_week = current_week - 1
    if last_complete_week <= 0:
        last_complete_week = 52
        current_year -= 1
    
    week_start, week_end = get_week_date_range(current_year, last_complete_week)
    print(f"📅 当前年份: {current_year}, 当前周: {current_week}, 最后完整周: {last_complete_week}")
    print(f"📅 最后完整周日期范围: {week_start} 到 {week_end}")
    
    print("\n" + "="*60)
    print("1. 测试柱状图数据源 (get_dimension_analysis_real)")
    print("="*60)
    
    try:
        # 获取柱状图数据
        chart_data = get_dimension_analysis_real('大区', '')
        print(f"✅ 柱状图数据获取成功")
        print(f"📊 数据结构: {json.dumps(chart_data, indent=2, ensure_ascii=False)}")
        
        if 'data' in chart_data and chart_data['data']:
            print(f"\n📈 柱状图显示的大区数据:")
            for item in chart_data['data']:
                print(f"  - {item['name']}: GMV = {item['value']:,.2f}")
        else:
            print("⚠️  柱状图数据为空")
            
    except Exception as e:
        print(f"❌ 柱状图数据获取失败: {e}")
    
    print("\n" + "="*60)
    print("2. 测试表格数据源 (get_dimension_trends_real)")
    print("="*60)
    
    try:
        # 获取表格数据
        table_data = get_dimension_trends_real('大区', '', None)
        print(f"✅ 表格数据获取成功")
        print(f"📊 数据结构: {json.dumps(table_data, indent=2, ensure_ascii=False)}")
        
        if 'data' in table_data and table_data['data']:
            print(f"\n📋 表格显示的大区数据:")
            for item in table_data['data']:
                dimension = item['dimension']
                trend = item['trend']
                if trend:
                    # 找到最后一个完整周的数据
                    last_week_data = None
                    for week_data in trend:
                        if week_data['week'] == last_complete_week:
                            last_week_data = week_data
                            break
                    
                    if last_week_data:
                        print(f"  - {dimension}: 第{last_complete_week}周 GMV = {last_week_data['gmv']:,.2f}")
                    else:
                        print(f"  - {dimension}: 未找到第{last_complete_week}周数据")
                else:
                    print(f"  - {dimension}: 无趋势数据")
        else:
            print("⚠️  表格数据为空")
            
    except Exception as e:
        print(f"❌ 表格数据获取失败: {e}")
    
    print("\n" + "="*60)
    print("3. 直接查询数据库验证")
    print("="*60)
    
    try:
        # 直接查询最后完整周的大区数据
        direct_sql = f"""
        SELECT 
            area,
            SUM(gmv) as total_gmv,
            COUNT(*) as record_count
        FROM dws_o2o_sales_d
        WHERE ds BETWEEN '{week_start}' AND '{week_end}'
        AND area IS NOT NULL
        AND area != ''
        GROUP BY area
        ORDER BY total_gmv DESC
        """
        
        direct_results = execute_query(direct_sql)
        print(f"✅ 直接数据库查询成功")
        print(f"📊 第{last_complete_week}周大区数据 (直接查询):")
        
        for row in direct_results:
            area = row['area']
            gmv = float(row['total_gmv'])
            count = row['record_count']
            print(f"  - {area}: GMV = {gmv:,.2f}, 记录数 = {count:,}")
            
        # 检查四川项目组的具体数据
        print(f"\n🔍 检查'四川项目组'的详细数据:")
        sichuan_sql = f"""
        SELECT 
            ds,
            SUM(gmv) as daily_gmv,
            COUNT(*) as daily_count
        FROM dws_o2o_sales_d
        WHERE ds BETWEEN '{week_start}' AND '{week_end}'
        AND area = '四川项目组'
        GROUP BY ds
        ORDER BY ds
        """
        
        sichuan_results = execute_query(sichuan_sql)
        if sichuan_results:
            print(f"📅 四川项目组第{last_complete_week}周每日数据:")
            total_week_gmv = 0
            for row in sichuan_results:
                daily_gmv = float(row['daily_gmv'])
                total_week_gmv += daily_gmv
                print(f"  - {row['ds']}: GMV = {daily_gmv:,.2f}, 记录数 = {row['daily_count']:,}")
            print(f"📊 四川项目组第{last_complete_week}周总GMV: {total_week_gmv:,.2f}")
        else:
            print("⚠️  四川项目组在该周无数据")
            
    except Exception as e:
        print(f"❌ 直接数据库查询失败: {e}")
    
    print("\n" + "="*60)
    print("4. 检查SQL查询差异")
    print("="*60)
    
    # 比较两个函数使用的SQL查询
    print("📝 柱状图使用的SQL (get_dimension_analysis_real):")
    chart_sql = f"""
    SELECT area as dimension_value, SUM(gmv) as gmv
    FROM dws_o2o_sales_d
    WHERE ds BETWEEN '{week_start}' AND '{week_end}'
    AND area IS NOT NULL
    AND area != ''
    GROUP BY area
    ORDER BY gmv DESC
    LIMIT 10
    """
    print(chart_sql)
    
    print("\n📝 表格使用的SQL (get_dimension_trends_real):")
    print("该函数使用批量查询，先获取Top5维度，然后查询多周数据")
    
    # 模拟表格函数的Top5查询
    top5_sql = f"""
    SELECT area as dimension_value, SUM(gmv) as total_gmv
    FROM dws_o2o_sales_d
    WHERE ds BETWEEN '{week_start}' AND '{week_end}'
    AND area IS NOT NULL
    AND area != ''
    GROUP BY area
    ORDER BY total_gmv DESC
    LIMIT 5
    """
    print(top5_sql)

if __name__ == "__main__":
    debug_area_data_consistency()
