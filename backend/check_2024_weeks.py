#!/usr/bin/env python3
"""
检查2024年有数据的周数
"""

from database import execute_query
from datetime import datetime, timedelta
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_week_date_range(year, week):
    """根据年份和周数获取该周的日期范围"""
    # 使用ISO周计算
    jan_4 = datetime(year, 1, 4)
    week_1_monday = jan_4 - timedelta(days=jan_4.weekday())
    week_start = week_1_monday + timedelta(weeks=week-1)
    week_end = week_start + timedelta(days=6)
    return week_start.strftime('%Y-%m-%d'), week_end.strftime('%Y-%m-%d')

def check_2024_weeks():
    """检查2024年有数据的周数"""
    print("=== 检查2024年有数据的周数 ===")
    
    try:
        # 获取2024年的数据日期范围
        sql = """
        SELECT 
            dt,
            COUNT(*) as count
        FROM ods_heyin_bi_data_t_mt_user_list_by_day
        WHERE SUBSTRING(dt, 1, 4) = '2024'
        AND userstatus IN ('新用户', '老用户')
        GROUP BY dt
        ORDER BY dt
        LIMIT 20
        """
        
        result = execute_query(sql)
        print("2024年前20个有数据的日期:")
        
        weeks_with_data = set()
        
        for row in result:
            dt_str = row['dt']
            count = row['count']
            
            # 转换日期格式
            if len(dt_str) == 8:  # yyyymmdd格式
                year = int(dt_str[:4])
                month = int(dt_str[4:6])
                day = int(dt_str[6:8])
                date_obj = datetime(year, month, day)
                
                # 计算ISO周数
                iso_year, iso_week, iso_weekday = date_obj.isocalendar()
                weeks_with_data.add(iso_week)
                
                print(f"  {dt_str} ({date_obj.strftime('%Y-%m-%d')}): {count} 条记录, 第{iso_week}周")
        
        print(f"\n2024年有数据的周数: {sorted(weeks_with_data)}")
        
        # 检查第31周（8月初）的数据
        week_31_start, week_31_end = get_week_date_range(2024, 31)
        week_31_start_yyyymmdd = week_31_start.replace('-', '')
        week_31_end_yyyymmdd = week_31_end.replace('-', '')
        
        print(f"\n检查2024年第31周 ({week_31_start} 到 {week_31_end}):")
        
        sql = f"""
        SELECT 
            userstatus,
            SUM(usernum) as total_users,
            SUM(brandoriginproductpricegmv) as total_gmv
        FROM ods_heyin_bi_data_t_mt_user_list_by_day
        WHERE dt BETWEEN '{week_31_start_yyyymmdd}' AND '{week_31_end_yyyymmdd}'
        AND userstatus IN ('新用户', '老用户')
        GROUP BY userstatus
        ORDER BY userstatus
        """
        
        result = execute_query(sql)
        if result:
            print("  2024年第31周有数据:")
            for row in result:
                print(f"    {row['userstatus']}: 用户{row['total_users']:,}, GMV¥{row['total_gmv']:,.0f}")
        else:
            print("  2024年第31周没有数据")
        
        # 检查第32周
        week_32_start, week_32_end = get_week_date_range(2024, 32)
        week_32_start_yyyymmdd = week_32_start.replace('-', '')
        week_32_end_yyyymmdd = week_32_end.replace('-', '')
        
        print(f"\n检查2024年第32周 ({week_32_start} 到 {week_32_end}):")
        
        sql = f"""
        SELECT 
            userstatus,
            SUM(usernum) as total_users,
            SUM(brandoriginproductpricegmv) as total_gmv
        FROM ods_heyin_bi_data_t_mt_user_list_by_day
        WHERE dt BETWEEN '{week_32_start_yyyymmdd}' AND '{week_32_end_yyyymmdd}'
        AND userstatus IN ('新用户', '老用户')
        GROUP BY userstatus
        ORDER BY userstatus
        """
        
        result = execute_query(sql)
        if result:
            print("  2024年第32周有数据:")
            for row in result:
                print(f"    {row['userstatus']}: 用户{row['total_users']:,}, GMV¥{row['total_gmv']:,.0f}")
        else:
            print("  2024年第32周没有数据")
            
    except Exception as e:
        print(f"检查2024年周数据失败: {e}")

def test_yoy_calculation():
    """测试年同比计算"""
    print("\n=== 测试年同比计算 ===")
    
    try:
        # 测试2025年第32周与2024年第32周的年同比
        current_year = 2025
        last_year = 2024
        test_week = 32
        
        # 2025年第32周
        current_week_start, current_week_end = get_week_date_range(current_year, test_week)
        current_week_start_yyyymmdd = current_week_start.replace('-', '')
        current_week_end_yyyymmdd = current_week_end.replace('-', '')
        
        print(f"2025年第{test_week}周 ({current_week_start} 到 {current_week_end}):")
        
        sql = f"""
        SELECT 
            userstatus,
            SUM(usernum) as total_users,
            SUM(brandoriginproductpricegmv) as total_gmv
        FROM ods_heyin_bi_data_t_mt_user_list_by_day
        WHERE dt BETWEEN '{current_week_start_yyyymmdd}' AND '{current_week_end_yyyymmdd}'
        AND userstatus IN ('新用户', '老用户')
        GROUP BY userstatus
        ORDER BY userstatus
        """
        
        current_result = execute_query(sql)
        current_data = {}
        
        if current_result:
            for row in current_result:
                current_data[row['userstatus']] = {
                    'users': int(row['total_users']),
                    'gmv': float(row['total_gmv'])
                }
                print(f"  {row['userstatus']}: 用户{row['total_users']:,}, GMV¥{row['total_gmv']:,.0f}")
        
        # 2024年第32周
        last_year_week_start, last_year_week_end = get_week_date_range(last_year, test_week)
        last_year_week_start_yyyymmdd = last_year_week_start.replace('-', '')
        last_year_week_end_yyyymmdd = last_year_week_end.replace('-', '')
        
        print(f"\n2024年第{test_week}周 ({last_year_week_start} 到 {last_year_week_end}):")
        
        sql = f"""
        SELECT 
            userstatus,
            SUM(usernum) as total_users,
            SUM(brandoriginproductpricegmv) as total_gmv
        FROM ods_heyin_bi_data_t_mt_user_list_by_day
        WHERE dt BETWEEN '{last_year_week_start_yyyymmdd}' AND '{last_year_week_end_yyyymmdd}'
        AND userstatus IN ('新用户', '老用户')
        GROUP BY userstatus
        ORDER BY userstatus
        """
        
        last_year_result = execute_query(sql)
        last_year_data = {}
        
        if last_year_result:
            for row in last_year_result:
                last_year_data[row['userstatus']] = {
                    'users': int(row['total_users']),
                    'gmv': float(row['total_gmv'])
                }
                print(f"  {row['userstatus']}: 用户{row['total_users']:,}, GMV¥{row['total_gmv']:,.0f}")
        
        # 计算年同比
        print(f"\n年同比计算:")
        for user_type in ['新用户', '老用户']:
            if user_type in current_data and user_type in last_year_data:
                current_users = current_data[user_type]['users']
                last_year_users = last_year_data[user_type]['users']
                
                if last_year_users > 0:
                    yoy = (current_users - last_year_users) / last_year_users
                    print(f"  {user_type}数量年同比: {yoy:.1%}")
                else:
                    print(f"  {user_type}数量年同比: 无法计算（去年数据为0）")
            else:
                print(f"  {user_type}数量年同比: 无法计算（缺少数据）")
            
    except Exception as e:
        print(f"测试年同比计算失败: {e}")

if __name__ == "__main__":
    print(f"开始检查2024年周数据 - {datetime.now()}")
    
    check_2024_weeks()
    test_yoy_calculation()
    
    print(f"\n检查完成 - {datetime.now()}")
