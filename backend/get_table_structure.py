#!/usr/bin/env python3
"""
获取Hologres表结构的脚本
"""

from database import get_table_structure, test_connection
import json

def main():
    print("Testing database connection...")
    if not test_connection():
        print("❌ Database connection failed!")
        return
    
    print("✅ Database connection successful!")
    
    table_name = "dws_o2o_sales_d"
    print(f"\nGetting structure for table: {table_name}")
    
    try:
        structure = get_table_structure(table_name)
        
        if not structure:
            print(f"❌ No structure found for table {table_name}")
            return
        
        print(f"\n📊 Table Structure for {table_name}:")
        print("=" * 80)
        print(f"{'Column Name':<30} {'Data Type':<20} {'Nullable':<10} {'Default':<15}")
        print("-" * 80)
        
        for column in structure:
            column_name = column['column_name']
            data_type = column['data_type']
            is_nullable = 'YES' if column['is_nullable'] == 'YES' else 'NO'
            default_value = str(column['column_default']) if column['column_default'] else 'NULL'
            
            print(f"{column_name:<30} {data_type:<20} {is_nullable:<10} {default_value:<15}")
        
        print(f"\n✅ Found {len(structure)} columns in table {table_name}")
        
        # 保存结构到JSON文件
        with open(f'{table_name}_structure.json', 'w', encoding='utf-8') as f:
            json.dump([dict(row) for row in structure], f, indent=2, ensure_ascii=False, default=str)
        
        print(f"📁 Table structure saved to {table_name}_structure.json")
        
    except Exception as e:
        print(f"❌ Error getting table structure: {e}")

if __name__ == "__main__":
    main()
