"""
真实数据查询模块
基于 dws_o2o_sales_d 表的数据查询
"""

from database import execute_query
from datetime import datetime, timedelta
import logging
import random

logger = logging.getLogger(__name__)

def get_week_date_range(year, week):
    """根据年份和周数获取该周的日期范围"""
    # 使用ISO周计算
    # 第1周包含1月4日的那一周
    jan_4 = datetime(year, 1, 4)
    # 找到第1周的周一
    week_1_monday = jan_4 - timedelta(days=jan_4.weekday())

    # 计算目标周的开始日期
    week_start = week_1_monday + timedelta(weeks=week-1)
    week_end = week_start + timedelta(days=6)

    return week_start.strftime('%Y-%m-%d'), week_end.strftime('%Y-%m-%d')

def get_current_week_number():
    """获取当前周数"""
    return datetime.now().isocalendar()[1]

def get_sales_targets(brand='', platform=''):
    """
    从dim_t_datatable_sales_target表获取销售目标数据

    Args:
        brand: 品牌名称
        platform: 平台名称

    Returns:
        dict: 包含月度和年度目标的字典
    """
    try:
        # 构建查询条件
        conditions = []
        if brand:
            conditions.append(f"brand = '{brand}'")
        if platform and platform != '全平台':
            conditions.append(f"platform = '{platform}'")

        where_clause = ""
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)

        # 查询目标数据
        sql = f"""
        SELECT
            brand,
            platform,
            tar_gmv,
            challenge_target_gmv,
            y_month
        FROM dim_t_datatable_sales_target
        {where_clause}
        ORDER BY y_month
        """

        results = execute_query(sql)

        # 组织数据结构
        targets = {
            'monthly': {},  # 月度目标 {month: target_gmv}
            'yearly': {}    # 年度目标 {year: total_target}
        }

        for row in results:
            y_month = row['y_month']
            tar_gmv = float(row['tar_gmv']) if row['tar_gmv'] else 0
            challenge_gmv = float(row['challenge_target_gmv']) if row['challenge_target_gmv'] else 0

            # 解析年月
            if isinstance(y_month, str):
                year_month = datetime.strptime(y_month, '%Y-%m-%d')
            else:
                year_month = y_month

            year = year_month.year
            month = year_month.month

            # 存储月度目标
            month_key = f"{year}-{month:02d}"
            if month_key not in targets['monthly']:
                targets['monthly'][month_key] = 0
            targets['monthly'][month_key] += tar_gmv

            # 累计年度目标
            if year not in targets['yearly']:
                targets['yearly'][year] = 0
            targets['yearly'][year] += tar_gmv

        logger.info(f"Retrieved sales targets for brand={brand}, platform={platform}")
        return targets

    except Exception as e:
        logger.error(f"Error getting sales targets: {e}")
        # 返回空目标数据
        return {
            'monthly': {},
            'yearly': {}
        }

def calculate_mtd_gmv(platform='全平台', brand=''):
    """
    计算MTD GMV

    逻辑：
    1. 如果当前日期不是月初（日期>1），计算本月1日到昨天的GMV
    2. 如果当前日期是月初（日期=1），计算上月完整的GMV作为参考
    3. 这样可以确保用户总是能看到有意义的MTD数据

    Args:
        platform: 平台名称
        brand: 品牌名称

    Returns:
        float: MTD GMV值
    """
    try:
        current_date = datetime.now()
        current_year = current_date.year
        current_month = current_date.month
        current_day = current_date.day

        # 构建过滤条件
        filters = []
        if platform != '全平台':
            filters.append(f"platform = '{platform}'")
        if brand:
            filters.append(f"brand = '{brand}'")

        filter_clause = ""
        if filters:
            filter_clause = "AND " + " AND ".join(filters)

        if current_day == 1:
            # 当前是月初，计算上月完整的GMV作为参考
            if current_month == 1:
                # 当前是1月，上月是去年12月
                last_month_year = current_year - 1
                last_month = 12
            else:
                last_month_year = current_year
                last_month = current_month - 1

            # 计算上月的第一天和最后一天
            last_month_start = datetime(last_month_year, last_month, 1)
            if last_month == 12:
                last_month_end = datetime(last_month_year, 12, 31)
            else:
                next_month_start = datetime(last_month_year, last_month + 1, 1)
                last_month_end = next_month_start - timedelta(days=1)

            start_date_str = last_month_start.strftime('%Y-%m-%d')
            end_date_str = last_month_end.strftime('%Y-%m-%d')

            logger.info(f"Current date is month start, calculating last month GMV from {start_date_str} to {end_date_str}")
        else:
            # 不是月初，计算本月1日到昨天的GMV
            month_start = datetime(current_year, current_month, 1)
            yesterday = current_date - timedelta(days=1)

            start_date_str = month_start.strftime('%Y-%m-%d')
            end_date_str = yesterday.strftime('%Y-%m-%d')

            logger.info(f"Calculating current month MTD from {start_date_str} to {end_date_str}")

        # 查询MTD GMV
        sql = f"""
        SELECT COALESCE(SUM(gmv), 0) as mtd_gmv
        FROM dws_o2o_sales_d
        WHERE ds BETWEEN '{start_date_str}' AND '{end_date_str}'
        {filter_clause}
        """

        result = execute_query(sql)
        mtd_gmv = float(result[0]['mtd_gmv']) if result and result[0]['mtd_gmv'] else 0

        logger.info(f"MTD GMV calculated: {mtd_gmv} (from {start_date_str} to {end_date_str})")
        return mtd_gmv

    except Exception as e:
        logger.error(f"Error calculating MTD GMV: {e}")
        return 0

def calculate_ytd_gmv(platform='全平台', brand=''):
    """
    计算YTD GMV

    逻辑：
    1. 如果当前日期不是年初（不是1月1日），计算本年1月1日到昨天的GMV
    2. 如果当前日期是年初（1月1日），计算去年完整的GMV作为参考
    3. 这样可以确保用户总是能看到有意义的YTD数据

    Args:
        platform: 平台名称
        brand: 品牌名称

    Returns:
        float: YTD GMV值
    """
    try:
        current_date = datetime.now()
        current_year = current_date.year
        current_month = current_date.month
        current_day = current_date.day

        # 构建过滤条件
        filters = []
        if platform != '全平台':
            filters.append(f"platform = '{platform}'")
        if brand:
            filters.append(f"brand = '{brand}'")

        filter_clause = ""
        if filters:
            filter_clause = "AND " + " AND ".join(filters)

        if current_month == 1 and current_day == 1:
            # 当前是年初（1月1日），计算去年完整的GMV作为参考
            last_year = current_year - 1
            start_date_str = f"{last_year}-01-01"
            end_date_str = f"{last_year}-12-31"

            logger.info(f"Current date is year start, calculating last year GMV from {start_date_str} to {end_date_str}")
        else:
            # 不是年初，计算本年1月1日到昨天的GMV
            year_start = datetime(current_year, 1, 1)
            yesterday = current_date - timedelta(days=1)

            start_date_str = year_start.strftime('%Y-%m-%d')
            end_date_str = yesterday.strftime('%Y-%m-%d')

            logger.info(f"Calculating current year YTD from {start_date_str} to {end_date_str}")

        # 查询YTD GMV
        sql = f"""
        SELECT COALESCE(SUM(gmv), 0) as ytd_gmv
        FROM dws_o2o_sales_d
        WHERE ds BETWEEN '{start_date_str}' AND '{end_date_str}'
        {filter_clause}
        """

        result = execute_query(sql)
        ytd_gmv = float(result[0]['ytd_gmv']) if result and result[0]['ytd_gmv'] else 0

        logger.info(f"YTD GMV calculated: {ytd_gmv} (from {start_date_str} to {end_date_str})")
        return ytd_gmv

    except Exception as e:
        logger.error(f"Error calculating YTD GMV: {e}")
        return 0

def calculate_mtd_yoy(platform='全平台', brand=''):
    """
    计算MTD年同比

    逻辑：
    1. 获取当前MTD数据
    2. 获取去年同月同期的MTD数据
    3. 计算年同比 = (当前MTD - 去年同期MTD) / 去年同期MTD

    Args:
        platform: 平台名称
        brand: 品牌名称

    Returns:
        float: MTD年同比（小数形式）
    """
    try:
        current_date = datetime.now()
        current_year = current_date.year
        current_month = current_date.month
        current_week = current_date.isocalendar()[1]

        # 获取当前MTD
        current_mtd = calculate_mtd_gmv(platform, brand)

        # 获取上一个完整周
        prev_week = current_week - 1
        prev_year = current_year

        if prev_week <= 0:
            prev_year = current_year - 1
            last_day_of_prev_year = datetime(prev_year, 12, 31)
            prev_week = last_day_of_prev_year.isocalendar()[1]

        # 获取上一个完整周的日期范围
        _, prev_week_end = get_week_date_range(prev_year, prev_week)
        prev_week_end_date = datetime.strptime(prev_week_end, '%Y-%m-%d')

        # 计算去年同期MTD
        last_year = current_year - 1
        last_year_month_start = datetime(last_year, current_month, 1)

        # 去年同期的结束日期：去年同月的同一个相对日期
        # 如果当前周日在本月内，则计算去年同月同期的MTD
        if prev_week_end_date.year == current_year and prev_week_end_date.month == current_month:
            # 计算当前周日在本月的第几天
            days_in_month = prev_week_end_date.day

            # 去年同月的同一天（如果去年该月没有这一天，则取该月最后一天）
            try:
                last_year_end_date = datetime(last_year, current_month, days_in_month)
            except ValueError:
                # 处理2月29日等特殊情况
                import calendar
                last_day_of_month = calendar.monthrange(last_year, current_month)[1]
                last_year_end_date = datetime(last_year, current_month, last_day_of_month)

            # 构建过滤条件
            filters = []
            if platform != '全平台':
                filters.append(f"platform = '{platform}'")
            if brand:
                filters.append(f"brand = '{brand}'")

            filter_clause = ""
            if filters:
                filter_clause = "AND " + " AND ".join(filters)

            # 查询去年同期MTD
            last_year_month_start_str = last_year_month_start.strftime('%Y-%m-%d')
            last_year_end_str = last_year_end_date.strftime('%Y-%m-%d')

            sql = f"""
            SELECT COALESCE(SUM(gmv), 0) as last_year_mtd
            FROM dws_o2o_sales_d
            WHERE ds BETWEEN '{last_year_month_start_str}' AND '{last_year_end_str}'
            {filter_clause}
            """

            result = execute_query(sql)
            last_year_mtd = float(result[0]['last_year_mtd']) if result and result[0]['last_year_mtd'] else 0

            # 计算年同比
            if last_year_mtd > 0:
                yoy = (current_mtd - last_year_mtd) / last_year_mtd
                logger.info(f"MTD YoY calculated: current={current_mtd:,.2f}, last_year={last_year_mtd:,.2f}, yoy={yoy:.4f}")
                return yoy
            else:
                logger.info(f"MTD YoY: no last year data, returning 0")
                return 0
        else:
            # 当前周日不在本月内，MTD为0，年同比也为0
            logger.info(f"MTD YoY: current MTD is 0, returning 0")
            return 0

    except Exception as e:
        logger.error(f"Error calculating MTD YoY: {e}")
        return 0

def calculate_mtd_mom(platform='全平台', brand=''):
    """
    计算MTD月环比

    逻辑：
    1. 获取当前MTD数据
    2. 获取上月同期的MTD数据
    3. 计算月环比 = (当前MTD - 上月同期MTD) / 上月同期MTD

    Args:
        platform: 平台名称
        brand: 品牌名称

    Returns:
        float: MTD月环比（小数形式）
    """
    try:
        current_date = datetime.now()
        current_year = current_date.year
        current_month = current_date.month
        current_week = current_date.isocalendar()[1]

        # 获取当前MTD
        current_mtd = calculate_mtd_gmv(platform, brand)

        # 获取上一个完整周
        prev_week = current_week - 1
        prev_year = current_year

        if prev_week <= 0:
            prev_year = current_year - 1
            last_day_of_prev_year = datetime(prev_year, 12, 31)
            prev_week = last_day_of_prev_year.isocalendar()[1]

        # 获取上一个完整周的日期范围
        _, prev_week_end = get_week_date_range(prev_year, prev_week)
        prev_week_end_date = datetime.strptime(prev_week_end, '%Y-%m-%d')

        # 计算上月同期MTD
        last_month = current_month - 1
        last_month_year = current_year
        if last_month <= 0:
            last_month = 12
            last_month_year = current_year - 1

        last_month_start = datetime(last_month_year, last_month, 1)

        # 如果当前周日在本月内，则计算上月同期的MTD
        if prev_week_end_date.year == current_year and prev_week_end_date.month == current_month:
            # 计算当前周日在本月的第几天
            days_in_month = prev_week_end_date.day

            # 上月的同一天（如果上月没有这一天，则取该月最后一天）
            try:
                last_month_end_date = datetime(last_month_year, last_month, days_in_month)
            except ValueError:
                # 处理月份天数不同的情况
                import calendar
                last_day_of_month = calendar.monthrange(last_month_year, last_month)[1]
                last_month_end_date = datetime(last_month_year, last_month, last_day_of_month)

            # 构建过滤条件
            filters = []
            if platform != '全平台':
                filters.append(f"platform = '{platform}'")
            if brand:
                filters.append(f"brand = '{brand}'")

            filter_clause = ""
            if filters:
                filter_clause = "AND " + " AND ".join(filters)

            # 查询上月同期MTD
            last_month_start_str = last_month_start.strftime('%Y-%m-%d')
            last_month_end_str = last_month_end_date.strftime('%Y-%m-%d')

            sql = f"""
            SELECT COALESCE(SUM(gmv), 0) as last_month_mtd
            FROM dws_o2o_sales_d
            WHERE ds BETWEEN '{last_month_start_str}' AND '{last_month_end_str}'
            {filter_clause}
            """

            result = execute_query(sql)
            last_month_mtd = float(result[0]['last_month_mtd']) if result and result[0]['last_month_mtd'] else 0

            # 计算月环比
            if last_month_mtd > 0:
                mom = (current_mtd - last_month_mtd) / last_month_mtd
                logger.info(f"MTD MoM calculated: current={current_mtd:,.2f}, last_month={last_month_mtd:,.2f}, mom={mom:.4f}")
                return mom
            else:
                logger.info(f"MTD MoM: no last month data, returning 0")
                return 0
        else:
            # 当前周日不在本月内，MTD为0，月环比也为0
            logger.info(f"MTD MoM: current MTD is 0, returning 0")
            return 0

    except Exception as e:
        logger.error(f"Error calculating MTD MoM: {e}")
        return 0

def calculate_ytd_yoy(platform='全平台', brand=''):
    """
    计算YTD年同比

    逻辑：
    1. 获取当前YTD数据
    2. 获取去年同期的YTD数据
    3. 计算年同比 = (当前YTD - 去年同期YTD) / 去年同期YTD

    Args:
        platform: 平台名称
        brand: 品牌名称

    Returns:
        float: YTD年同比（小数形式）
    """
    try:
        current_date = datetime.now()
        current_year = current_date.year
        current_week = current_date.isocalendar()[1]

        # 获取当前YTD
        current_ytd = calculate_ytd_gmv(platform, brand)

        # 获取上一个完整周
        prev_week = current_week - 1
        prev_year = current_year

        if prev_week <= 0:
            prev_year = current_year - 1
            last_day_of_prev_year = datetime(prev_year, 12, 31)
            prev_week = last_day_of_prev_year.isocalendar()[1]

        # 获取上一个完整周的日期范围
        _, prev_week_end = get_week_date_range(prev_year, prev_week)
        prev_week_end_date = datetime.strptime(prev_week_end, '%Y-%m-%d')

        # 如果当前周日在本年内，则计算去年同期的YTD
        if prev_week_end_date.year == current_year:
            # 计算去年同期YTD：去年1月1日到去年同期日期
            last_year = current_year - 1
            last_year_start = f"{last_year}-01-01"

            # 去年的同一天
            last_year_end_date = datetime(last_year, prev_week_end_date.month, prev_week_end_date.day)
            last_year_end_str = last_year_end_date.strftime('%Y-%m-%d')

            # 构建过滤条件
            filters = []
            if platform != '全平台':
                filters.append(f"platform = '{platform}'")
            if brand:
                filters.append(f"brand = '{brand}'")

            filter_clause = ""
            if filters:
                filter_clause = "AND " + " AND ".join(filters)

            # 查询去年同期YTD
            sql = f"""
            SELECT COALESCE(SUM(gmv), 0) as last_year_ytd
            FROM dws_o2o_sales_d
            WHERE ds BETWEEN '{last_year_start}' AND '{last_year_end_str}'
            {filter_clause}
            """

            result = execute_query(sql)
            last_year_ytd = float(result[0]['last_year_ytd']) if result and result[0]['last_year_ytd'] else 0

            # 计算年同比
            if last_year_ytd > 0:
                yoy = (current_ytd - last_year_ytd) / last_year_ytd
                logger.info(f"YTD YoY calculated: current={current_ytd:,.2f}, last_year={last_year_ytd:,.2f}, yoy={yoy:.4f}")
                return yoy
            else:
                logger.info(f"YTD YoY: no last year data, returning 0")
                return 0
        else:
            # 当前周日不在本年内，YTD为0，年同比也为0
            logger.info(f"YTD YoY: current YTD is 0, returning 0")
            return 0

    except Exception as e:
        logger.error(f"Error calculating YTD YoY: {e}")
        return 0

def calculate_mtd_ytd_optimized(platform='全平台', brand=''):
    """
    优化的MTD和YTD计算函数 - 一次查询获取所有数据

    使用新的逻辑：
    - MTD: 如果是月初则显示上月完整数据，否则显示本月到昨天的数据
    - YTD: 如果是年初则显示去年完整数据，否则显示本年到昨天的数据

    Returns:
        dict: {
            'mtd_current': float,
            'mtd_yoy': float,
            'mtd_mom': float,
            'ytd_current': float,
            'ytd_yoy': float
        }
    """
    try:
        current_date = datetime.now()
        current_year = current_date.year
        current_month = current_date.month
        current_day = current_date.day

        # 构建过滤条件
        filters = []
        if platform != '全平台':
            filters.append(f"platform = '{platform}'")
        if brand:
            filters.append(f"brand = '{brand}'")

        filter_clause = ""
        if filters:
            filter_clause = "AND " + " AND ".join(filters)

        # 初始化结果
        result = {
            'mtd_current': 0,
            'mtd_yoy': 0,
            'mtd_mom': 0,
            'ytd_current': 0,
            'ytd_yoy': 0
        }

        # 确定MTD计算范围
        if current_day == 1:
            # 月初，计算上月完整数据
            if current_month == 1:
                mtd_year = current_year - 1
                mtd_month = 12
            else:
                mtd_year = current_year
                mtd_month = current_month - 1

            mtd_start = datetime(mtd_year, mtd_month, 1)
            if mtd_month == 12:
                mtd_end = datetime(mtd_year, 12, 31)
            else:
                next_month_start = datetime(mtd_year, mtd_month + 1, 1)
                mtd_end = next_month_start - timedelta(days=1)
        else:
            # 非月初，计算本月到昨天
            mtd_start = datetime(current_year, current_month, 1)
            mtd_end = current_date - timedelta(days=1)

        # 确定YTD计算范围
        if current_month == 1 and current_day == 1:
            # 年初，计算去年完整数据
            ytd_start = datetime(current_year - 1, 1, 1)
            ytd_end = datetime(current_year - 1, 12, 31)
        else:
            # 非年初，计算本年到昨天
            ytd_start = datetime(current_year, 1, 1)
            ytd_end = current_date - timedelta(days=1)

        # 计算比较期间的日期范围
        mtd_start_str = mtd_start.strftime('%Y-%m-%d')
        mtd_end_str = mtd_end.strftime('%Y-%m-%d')
        ytd_start_str = ytd_start.strftime('%Y-%m-%d')
        ytd_end_str = ytd_end.strftime('%Y-%m-%d')

        # 计算去年同期MTD
        last_year_mtd_start = datetime(mtd_start.year - 1, mtd_start.month, mtd_start.day)
        try:
            last_year_mtd_end = datetime(mtd_end.year - 1, mtd_end.month, mtd_end.day)
        except ValueError:
            # 处理闰年2月29日的情况
            import calendar
            last_day = calendar.monthrange(mtd_end.year - 1, mtd_end.month)[1]
            last_year_mtd_end = datetime(mtd_end.year - 1, mtd_end.month, min(mtd_end.day, last_day))

        # 计算上月同期MTD
        if mtd_start.month == 1:
            last_month_mtd_start = datetime(mtd_start.year - 1, 12, mtd_start.day)
        else:
            last_month_mtd_start = datetime(mtd_start.year, mtd_start.month - 1, mtd_start.day)

        try:
            if mtd_end.month == 1:
                last_month_mtd_end = datetime(mtd_end.year - 1, 12, mtd_end.day)
            else:
                last_month_mtd_end = datetime(mtd_end.year, mtd_end.month - 1, mtd_end.day)
        except ValueError:
            # 处理月份天数不同的情况
            import calendar
            target_month = 12 if mtd_end.month == 1 else mtd_end.month - 1
            target_year = mtd_end.year - 1 if mtd_end.month == 1 else mtd_end.year
            last_day = calendar.monthrange(target_year, target_month)[1]
            last_month_mtd_end = datetime(target_year, target_month, min(mtd_end.day, last_day))

        # 计算去年同期YTD
        last_year_ytd_start = datetime(ytd_start.year - 1, ytd_start.month, ytd_start.day)
        try:
            last_year_ytd_end = datetime(ytd_end.year - 1, ytd_end.month, ytd_end.day)
        except ValueError:
            # 处理闰年的情况
            import calendar
            last_day = calendar.monthrange(ytd_end.year - 1, ytd_end.month)[1]
            last_year_ytd_end = datetime(ytd_end.year - 1, ytd_end.month, min(ytd_end.day, last_day))

        # 构建合并查询
        sql_parts = []

        # 当前MTD
        sql_parts.append(f"""
        SELECT 'mtd_current' as metric, COALESCE(SUM(gmv), 0) as value
        FROM dws_o2o_sales_d
        WHERE ds BETWEEN '{mtd_start_str}' AND '{mtd_end_str}'
        {filter_clause}
        """)

        # 去年同期MTD
        sql_parts.append(f"""
        SELECT 'mtd_last_year' as metric, COALESCE(SUM(gmv), 0) as value
        FROM dws_o2o_sales_d
        WHERE ds BETWEEN '{last_year_mtd_start.strftime('%Y-%m-%d')}' AND '{last_year_mtd_end.strftime('%Y-%m-%d')}'
        {filter_clause}
        """)

        # 上月同期MTD
        sql_parts.append(f"""
        SELECT 'mtd_last_month' as metric, COALESCE(SUM(gmv), 0) as value
        FROM dws_o2o_sales_d
        WHERE ds BETWEEN '{last_month_mtd_start.strftime('%Y-%m-%d')}' AND '{last_month_mtd_end.strftime('%Y-%m-%d')}'
        {filter_clause}
        """)

        # 当前YTD
        sql_parts.append(f"""
        SELECT 'ytd_current' as metric, COALESCE(SUM(gmv), 0) as value
        FROM dws_o2o_sales_d
        WHERE ds BETWEEN '{ytd_start_str}' AND '{ytd_end_str}'
        {filter_clause}
        """)

        # 去年同期YTD
        sql_parts.append(f"""
        SELECT 'ytd_last_year' as metric, COALESCE(SUM(gmv), 0) as value
        FROM dws_o2o_sales_d
        WHERE ds BETWEEN '{last_year_ytd_start.strftime('%Y-%m-%d')}' AND '{last_year_ytd_end.strftime('%Y-%m-%d')}'
        {filter_clause}
        """)

        if sql_parts:
            combined_sql = " UNION ALL ".join(sql_parts)

            query_results = execute_query(combined_sql)

            # 处理查询结果
            data_dict = {row['metric']: float(row['value']) for row in query_results}

            # 计算MTD相关指标
            mtd_current = data_dict.get('mtd_current', 0)
            mtd_last_year = data_dict.get('mtd_last_year', 0)
            mtd_last_month = data_dict.get('mtd_last_month', 0)

            result['mtd_current'] = mtd_current
            result['mtd_yoy'] = (mtd_current - mtd_last_year) / mtd_last_year if mtd_last_year > 0 else 0
            result['mtd_mom'] = (mtd_current - mtd_last_month) / mtd_last_month if mtd_last_month > 0 else 0

            # 计算YTD相关指标
            ytd_current = data_dict.get('ytd_current', 0)
            ytd_last_year = data_dict.get('ytd_last_year', 0)

            result['ytd_current'] = ytd_current
            result['ytd_yoy'] = (ytd_current - ytd_last_year) / ytd_last_year if ytd_last_year > 0 else 0

            logger.info(f"Optimized MTD/YTD calculation completed: {result}")

        return result

    except Exception as e:
        logger.error(f"Error in optimized MTD/YTD calculation: {e}")
        return {
            'mtd_current': 0,
            'mtd_yoy': 0,
            'mtd_mom': 0,
            'ytd_current': 0,
            'ytd_yoy': 0
        }

def get_trading_summary_real(week, platform='全平台', brand=''):
    """获取交易概览数据 - 真实数据（使用新的MTD/YTD计算逻辑）"""
    current_year = datetime.now().year
    week_start, week_end = get_week_date_range(current_year, week)

    # 构建过滤条件
    filters = []
    if platform != '全平台':
        filters.append(f"platform = '{platform}'")
    if brand:
        filters.append(f"brand = '{brand}'")

    filter_clause = ""
    if filters:
        filter_clause = "AND " + " AND ".join(filters)

    # 获取销售目标数据
    targets = get_sales_targets(brand, platform)
    current_month = datetime.now().month
    current_month_key = f"{current_year}-{current_month:02d}"

    # 获取月度和年度目标
    mtd_target = targets['monthly'].get(current_month_key, 0)
    ytd_target = targets['yearly'].get(current_year, 0)

    # 使用优化的MTD和YTD计算逻辑（一次查询获取所有数据）
    mtd_ytd_data = calculate_mtd_ytd_optimized(platform, brand)

    mtd_gmv = mtd_ytd_data['mtd_current']
    ytd_gmv = mtd_ytd_data['ytd_current']
    mtd_yoy = mtd_ytd_data['mtd_yoy']
    mtd_mom = mtd_ytd_data['mtd_mom']
    ytd_yoy = mtd_ytd_data['ytd_yoy']

    # 简化查询，只获取当前周和同比环比数据
    last_year = current_year - 1
    last_year_week_start, last_year_week_end = get_week_date_range(last_year, week)

    # 处理上一周的跨年情况
    prev_week = week - 1
    prev_year = current_year
    if prev_week <= 0:
        prev_year = current_year - 1
        last_day_of_prev_year = datetime(prev_year, 12, 31)
        prev_week = last_day_of_prev_year.isocalendar()[1]

    prev_week_start, prev_week_end = get_week_date_range(prev_year, prev_week)

    combined_sql = f"""
    SELECT
        'week' as period_type,
        COALESCE(SUM(gmv), 0) as gmv
    FROM dws_o2o_sales_d
    WHERE ds BETWEEN '{week_start}' AND '{week_end}'
    {filter_clause}

    UNION ALL

    SELECT
        'week_last_year' as period_type,
        COALESCE(SUM(gmv), 0) as gmv
    FROM dws_o2o_sales_d
    WHERE ds BETWEEN '{last_year_week_start}' AND '{last_year_week_end}'
    {filter_clause}

    UNION ALL

    SELECT
        'week_last_week' as period_type,
        COALESCE(SUM(gmv), 0) as gmv
    FROM dws_o2o_sales_d
    WHERE ds BETWEEN '{prev_week_start}' AND '{prev_week_end}'
    {filter_clause}
    """

    try:
        # 执行合并查询
        results = execute_query(combined_sql)

        # 解析结果
        data_dict = {}
        for row in results:
            data_dict[row['period_type']] = float(row['gmv'])

        week_gmv = data_dict.get('week', 0)
        gmv_last_year = data_dict.get('week_last_year', 0)
        gmv_last_week = data_dict.get('week_last_week', 0)

        # 计算同比和环比（返回小数形式，前端会转换为百分比）
        yoy = (week_gmv - gmv_last_year) / gmv_last_year if gmv_last_year > 0 else 0
        wow = (week_gmv - gmv_last_week) / gmv_last_week if gmv_last_week > 0 else 0

        # 计算MTD和YTD达成率
        mtd_rate = mtd_gmv / mtd_target if mtd_target > 0 else 0
        ytd_rate = ytd_gmv / ytd_target if ytd_target > 0 else 0

        return {
            'week_data': {
                'week': week,
                'gmv': week_gmv,
                'yoy': yoy,
                'wow': wow
            },
            'mtd_data': {
                'current': mtd_gmv,
                'target': mtd_target,
                'rate': mtd_rate,
                'yoy': mtd_yoy,  # MTD年同比
                'mom': mtd_mom   # MTD月环比
            },
            'ytd_data': {
                'current': ytd_gmv,
                'target': ytd_target,
                'rate': ytd_rate,
                'yoy': ytd_yoy   # YTD年同比
            }
        }

    except Exception as e:
        logger.error(f"Error getting trading summary: {e}")
        raise

def get_trading_trends_real(platform='全平台', brand=''):
    """获取GMV趋势数据 - 真实数据（简化版本）"""
    current_year = datetime.now().year

    # 构建过滤条件
    filters = []
    if platform != '全平台':
        filters.append(f"platform = '{platform}'")
    if brand:
        filters.append(f"brand = '{brand}'")

    filter_clause = ""
    if filters:
        filter_clause = "AND " + " AND ".join(filters)

    # 获取最近8周的数据：从t-1周开始往前推8周，但需要额外获取第9周来计算第一周的周环比
    current_week = get_current_week_number()
    trend_data = []

    # 优化的趋势查询 - 正确的周数计算和同比环比
    try:
        # 构建所有需要的日期范围：从t-1周开始往前推9周（多获取一周用于计算第一周的周环比）
        all_date_ranges = []
        for week_offset in range(1, 10):  # 从1开始，往前推9周
            target_week = current_week - week_offset
            year = current_year

            if target_week <= 0:
                target_week += 52
                year = current_year - 1

            week_start, week_end = get_week_date_range(year, target_week)
            all_date_ranges.append((target_week, year, week_start, week_end))

        # 反转列表，使其按时间顺序排列（从早到晚）
        all_date_ranges.reverse()

        # 分离出显示的8周和用于计算周环比的第9周
        date_ranges = all_date_ranges[1:]  # 最近8周（用于显示）
        prev_week_range = all_date_ranges[0]  # 第9周（仅用于计算第一周的周环比）

        # 使用单个查询获取所有周的数据（包括第9周用于计算第一周的周环比）
        all_conditions = []
        for target_week, year, week_start, week_end in all_date_ranges:  # 使用all_date_ranges包含第9周
            all_conditions.append(f"(ds BETWEEN '{week_start}' AND '{week_end}')")

        combined_sql = f"""
        SELECT
            ds,
            COALESCE(SUM(gmv), 0) as gmv
        FROM dws_o2o_sales_d
        WHERE ({' OR '.join(all_conditions)})
        {filter_clause}
        GROUP BY ds
        ORDER BY ds
        """

        all_results = execute_query(combined_sql)

        # 按日期范围聚合数据（包括第9周）
        week_gmv_map = {}
        for target_week, year, week_start, week_end in all_date_ranges:  # 使用all_date_ranges包含第9周
            week_total = 0
            for row in all_results:
                if week_start <= row['ds'] <= week_end:
                    week_total += float(row['gmv'])
            week_gmv_map[target_week] = week_total

        # 获取同比数据（去年同期8周）
        yoy_conditions = []
        yoy_week_map = {}
        for target_week, year, week_start, week_end in date_ranges:
            yoy_year = year - 1
            yoy_week_start, yoy_week_end = get_week_date_range(yoy_year, target_week)
            yoy_conditions.append(f"(ds BETWEEN '{yoy_week_start}' AND '{yoy_week_end}')")
            yoy_week_map[target_week] = (yoy_week_start, yoy_week_end)

        # 查询去年同期数据
        if yoy_conditions:
            yoy_sql = f"""
            SELECT
                ds,
                COALESCE(SUM(gmv), 0) as gmv
            FROM dws_o2o_sales_d
            WHERE ({' OR '.join(yoy_conditions)})
            {filter_clause}
            GROUP BY ds
            ORDER BY ds
            """

            yoy_results = execute_query(yoy_sql)

            # 按周聚合去年同期数据
            yoy_gmv_map = {}
            for target_week, (yoy_start, yoy_end) in yoy_week_map.items():
                yoy_total = 0
                for row in yoy_results:
                    if yoy_start <= row['ds'] <= yoy_end:
                        yoy_total += float(row['gmv'])
                yoy_gmv_map[target_week] = yoy_total
        else:
            yoy_gmv_map = {}

        # 构建趋势数据，计算正确的同比和环比
        for i, (target_week, year, week_start, week_end) in enumerate(date_ranges):
            week_gmv = week_gmv_map.get(target_week, 0)

            # 计算环比（与上一周比较）
            if i > 0:
                # 对于第2-8周，使用前一周的数据
                prev_week = date_ranges[i-1][0]
                prev_week_gmv = week_gmv_map.get(prev_week, 0)
                wow = (week_gmv - prev_week_gmv) / prev_week_gmv * 100 if prev_week_gmv > 0 else 0
            else:
                # 对于第1周，使用第9周的数据（all_date_ranges[0]）
                prev_week = all_date_ranges[0][0]  # 第9周的周数
                prev_week_gmv = week_gmv_map.get(prev_week, 0)
                wow = (week_gmv - prev_week_gmv) / prev_week_gmv * 100 if prev_week_gmv > 0 else 0

            # 计算同比（与去年同周比较）
            yoy_gmv = yoy_gmv_map.get(target_week, 0)
            yoy = (week_gmv - yoy_gmv) / yoy_gmv * 100 if yoy_gmv > 0 else 0

            trend_data.append({
                'week': target_week,
                'gmv': week_gmv,
                'yoy': yoy,
                'wow': wow
            })

    except Exception as e:
        logger.error(f"Error getting trends data: {e}")
        # 返回mock数据（格式与真实数据一致，百分比格式）
        for week_offset in range(7, -1, -1):
            target_week = current_week - week_offset
            trend_data.append({
                'week': target_week,
                'gmv': 1000000,  # Mock数据
                'yoy': 10.0,  # 10%
                'wow': 5.0    # 5%
            })

    # 平台数据（仅在全平台时返回）
    platform_data = None
    if platform == '全平台':
        platform_data = get_platform_data_real(brand)

    return {
        'trend_data': trend_data,
        'platform_data': platform_data
    }

def get_platform_data_real(brand=''):
    """获取各平台GMV数据 - 真实数据"""
    current_year = datetime.now().year
    current_week = get_current_week_number()

    # 如果有品牌过滤，使用最近4周的数据以获得更完整的平台覆盖
    # 如果没有品牌过滤，使用当前周的数据
    if brand:
        # 最近4周的数据
        week_start_4weeks, _ = get_week_date_range(current_year, max(1, current_week - 3))
        week_end_current = get_week_date_range(current_year, current_week)[1]
        date_filter = f"ds BETWEEN '{week_start_4weeks}' AND '{week_end_current}'"
    else:
        # 当前周的数据
        week_start, week_end = get_week_date_range(current_year, current_week)
        date_filter = f"ds BETWEEN '{week_start}' AND '{week_end}'"

    # 构建品牌过滤条件
    brand_filter = ""
    if brand:
        brand_filter = f"AND brand = '{brand}'"

    platform_sql = f"""
    SELECT
        platform,
        COALESCE(SUM(gmv), 0) as gmv,
        COALESCE(SUM(sales_volume), 0) as sales_volume
    FROM dws_o2o_sales_d
    WHERE {date_filter}
    AND platform IS NOT NULL
    AND platform != ''
    {brand_filter}
    GROUP BY platform
    ORDER BY gmv DESC
    """

    try:
        results = execute_query(platform_sql)
        platform_data = {}

        for row in results:
            platform_name = row['platform']
            if platform_name:  # 确保平台名称不为空
                platform_data[platform_name] = {
                    'gmv': float(row['gmv']),
                    'sales_volume': int(row['sales_volume'])
                }

        return platform_data

    except Exception as e:
        logger.error(f"Error getting platform data: {e}")
        return {}

def get_dimension_analysis_real(dimension='子品牌', brand='', platform='全平台'):
    """获取维度分析数据 - 显示最后一个完整周的GMV分布"""
    # 维度字段映射
    dimension_mapping = {
        '子品牌': 'sub_brand',
        '品线': 'product_line',
        '渠道类型': 'shop_type',
        '大区': 'area'
    }

    dimension_field = dimension_mapping.get(dimension, 'sub_brand')

    # 获取最后一个完整周（当前周-1）
    current_year = datetime.now().year
    current_week = get_current_week_number()
    last_complete_week = current_week - 1
    if last_complete_week <= 0:
        last_complete_week = 52
        current_year -= 1

    week_start, week_end = get_week_date_range(current_year, last_complete_week)

    # 构建过滤条件
    filters = []
    if brand:
        filters.append(f"brand = '{brand}'")
    if platform != '全平台':
        filters.append(f"platform = '{platform}'")

    filter_clause = ""
    if filters:
        filter_clause = "AND " + " AND ".join(filters)

    # 查询最后一个完整周的维度GMV分布
    dimension_sql = f"""
    SELECT {dimension_field} as dimension_value, SUM(gmv) as gmv
    FROM dws_o2o_sales_d
    WHERE ds BETWEEN '{week_start}' AND '{week_end}'
    AND {dimension_field} IS NOT NULL
    AND {dimension_field} != ''
    {filter_clause}
    GROUP BY {dimension_field}
    ORDER BY gmv DESC
    LIMIT 10
    """

    try:
        dimension_result = execute_query(dimension_sql)
        dimension_data = []

        for row in dimension_result:
            dimension_value = row['dimension_value']
            gmv = float(row['gmv'])

            if dimension_value and gmv > 0:
                dimension_data.append({
                    'name': dimension_value,
                    'value': gmv
                })

        logger.info(f"Successfully retrieved dimension analysis for {dimension}, week {last_complete_week}, {len(dimension_data)} items")

    except Exception as e:
        logger.error(f"Error getting dimension analysis for {dimension}: {e}")
        # 返回mock数据
        dimension_data = [
            {'name': f'Mock{dimension}1', 'value': 1000000},
            {'name': f'Mock{dimension}2', 'value': 800000},
            {'name': f'Mock{dimension}3', 'value': 600000},
            {'name': f'Mock{dimension}4', 'value': 400000},
            {'name': f'Mock{dimension}5', 'value': 200000}
        ]

    return {
        'dimension': dimension,
        'week': last_complete_week,
        'data': dimension_data
    }

def get_dimension_trends_real(dimension='子品牌', brand='', selected_week=None, platform='全平台'):
    """获取维度趋势数据 - 用于明细数据表格 - 优化版本"""
    # 维度字段映射
    dimension_mapping = {
        '子品牌': 'sub_brand',
        '品线': 'product_line',
        '渠道类型': 'shop_type',
        '大区': 'area'
    }

    dimension_field = dimension_mapping.get(dimension, 'sub_brand')

    # 确定基准周次
    current_year = datetime.now().year
    if selected_week:
        # 使用用户选择的周次
        base_week = int(selected_week)
    else:
        # 使用当前周的上一周（最后一个完整周）
        current_week = get_current_week_number()
        base_week = current_week - 1
        if base_week <= 0:
            base_week = 52
            current_year -= 1

    # 构建过滤条件
    filters = []
    if brand:
        filters.append(f"brand = '{brand}'")
    if platform != '全平台':
        filters.append(f"platform = '{platform}'")

    filter_clause = ""
    if filters:
        filter_clause = "AND " + " AND ".join(filters)

    # 获取基准周的Top10维度值（用于确定要显示哪些维度）- 与柱状图保持一致
    week_start, week_end = get_week_date_range(current_year, base_week)

    top_dimensions_sql = f"""
    SELECT {dimension_field} as dimension_value, SUM(gmv) as total_gmv
    FROM dws_o2o_sales_d
    WHERE ds BETWEEN '{week_start}' AND '{week_end}'
    AND {dimension_field} IS NOT NULL
    AND {dimension_field} != ''
    {filter_clause}
    GROUP BY {dimension_field}
    ORDER BY total_gmv DESC
    LIMIT 10
    """

    try:
        top_dimensions_result = execute_query(top_dimensions_sql)
        if not top_dimensions_result:
            return {'dimension': dimension, 'data': []}

        # 获取所有维度值（Top10，与柱状图保持一致）
        dimension_values = [row['dimension_value'] for row in top_dimensions_result]
        dimension_values_str = "', '".join(dimension_values)

        # 构建所有需要的日期范围
        all_date_ranges = []
        week_mapping = {}  # 用于映射日期范围到周次

        for week_offset in range(0, 9):  # 查询9周数据，为第8周提供环比基准
            target_week = base_week - week_offset
            target_year = current_year
            if target_week <= 0:
                target_week += 52
                target_year -= 1

            # 当前周
            week_start, week_end = get_week_date_range(target_year, target_week)
            all_date_ranges.append(f"(ds BETWEEN '{week_start}' AND '{week_end}')")
            week_mapping[f"{week_start}_{week_end}"] = {'week': target_week, 'year': target_year, 'type': 'current'}

            # 年同比周
            yoy_year = target_year - 1
            yoy_week_start, yoy_week_end = get_week_date_range(yoy_year, target_week)
            all_date_ranges.append(f"(ds BETWEEN '{yoy_week_start}' AND '{yoy_week_end}')")
            week_mapping[f"{yoy_week_start}_{yoy_week_end}"] = {'week': target_week, 'year': yoy_year, 'type': 'yoy'}

            # 周环比周（上一周）- 为前8周都计算环比数据
            if week_offset < 8:
                wow_week = target_week - 1
                wow_year = target_year
                if wow_week <= 0:
                    wow_week = 52
                    wow_year -= 1
                wow_week_start, wow_week_end = get_week_date_range(wow_year, wow_week)
                all_date_ranges.append(f"(ds BETWEEN '{wow_week_start}' AND '{wow_week_end}')")
                week_mapping[f"{wow_week_start}_{wow_week_end}"] = {'week': wow_week, 'year': wow_year, 'type': 'wow', 'for_week': target_week}

        # 使用单个查询获取所有数据
        batch_sql = f"""
        SELECT
            {dimension_field} as dimension_value,
            ds,
            SUM(gmv) as gmv
        FROM dws_o2o_sales_d
        WHERE {dimension_field} IN ('{dimension_values_str}')
        AND ({' OR '.join(all_date_ranges)})
        {filter_clause}
        GROUP BY {dimension_field}, ds
        ORDER BY {dimension_field}, ds
        """

        batch_result = execute_query(batch_sql)

        # 组织数据结构
        data_by_dimension = {}
        for row in batch_result:
            dimension_value = row['dimension_value']
            ds = row['ds']
            gmv = float(row['gmv']) if row['gmv'] else 0

            if dimension_value not in data_by_dimension:
                data_by_dimension[dimension_value] = {}

            if ds not in data_by_dimension[dimension_value]:
                data_by_dimension[dimension_value][ds] = 0

            data_by_dimension[dimension_value][ds] += gmv

        # 构建最终结果
        dimension_trends = []

        for dimension_row in top_dimensions_result:
            dimension_value = dimension_row['dimension_value']
            if not dimension_value:
                continue

            total_gmv = float(dimension_row['total_gmv'])
            trend_data = []

            # 为每一周构建数据
            for week_offset in range(0, 8):
                target_week = base_week - week_offset
                target_year = current_year
                if target_week <= 0:
                    target_week += 52
                    target_year -= 1

                # 获取当前周数据
                week_start, week_end = get_week_date_range(target_year, target_week)
                week_gmv = 0

                # 累计该周范围内的所有日期数据
                for ds in data_by_dimension.get(dimension_value, {}):
                    if week_start <= ds <= week_end:
                        week_gmv += data_by_dimension[dimension_value][ds]

                # 获取年同比数据
                yoy_year = target_year - 1
                yoy_week_start, yoy_week_end = get_week_date_range(yoy_year, target_week)
                yoy_gmv = 0

                for ds in data_by_dimension.get(dimension_value, {}):
                    if yoy_week_start <= ds <= yoy_week_end:
                        yoy_gmv += data_by_dimension[dimension_value][ds]

                yoy = (week_gmv - yoy_gmv) / yoy_gmv if yoy_gmv > 0 else 0

                # 获取周环比数据 - 现在所有8周都能计算环比
                wow_week = target_week - 1
                wow_year = target_year
                if wow_week <= 0:
                    wow_week = 52
                    wow_year -= 1

                wow_week_start, wow_week_end = get_week_date_range(wow_year, wow_week)
                wow_gmv = 0

                for ds in data_by_dimension.get(dimension_value, {}):
                    if wow_week_start <= ds <= wow_week_end:
                        wow_gmv += data_by_dimension[dimension_value][ds]

                wow = (week_gmv - wow_gmv) / wow_gmv if wow_gmv > 0 else 0

                trend_data.append({
                    'week': target_week,
                    'gmv': week_gmv,
                    'yoy': yoy,
                    'wow': wow
                })

            dimension_trends.append({
                'dimension': dimension_value,
                'trend': trend_data,
                'total_gmv': total_gmv,
                'yoy': 0.16,  # 整体年同比
                'wow': 0.05   # 整体周环比
            })

        logger.info(f"Successfully retrieved dimension trends for {dimension}, {len(dimension_trends)} items")

    except Exception as e:
        logger.error(f"Error getting dimension trends for {dimension}: {e}")
        # 返回mock数据
        dimension_trends = []
        for i in range(3):
            trend_data = []
            for week_offset in range(7, -1, -1):
                target_week = current_week - week_offset
                if target_week <= 0:
                    target_week += 52

                trend_data.append({
                    'week': target_week,
                    'gmv': 1000000 * (1 - i * 0.2) * (1 + week_offset * 0.1),
                    'yoy': 0.15 + (i * 0.05),
                    'wow': 0.05 + (i * 0.02)
                })

            dimension_trends.append({
                'dimension': f'Mock{dimension}{i+1}',
                'trend': trend_data,
                'total_gmv': 8000000 * (1 - i * 0.2),
                'yoy': 0.16,
                'wow': 0.05
            })

    return {
        'dimension': dimension,
        'data': dimension_trends
    }

def get_top10_data_real(brand='', platform='全平台'):
    """获取Top10数据 - 真实数据，包含年同比和周环比计算"""
    current_year = datetime.now().year
    current_week = get_current_week_number()

    # 使用最后一个完整周（当前周-1）
    base_week = current_week - 1
    if base_week <= 0:
        base_week = 52
        current_year -= 1

    week_start, week_end = get_week_date_range(current_year, base_week)

    # 构建过滤条件
    filters = []
    if brand:
        filters.append(f"brand = '{brand}'")
    if platform != '全平台':
        filters.append(f"platform = '{platform}'")

    filter_clause = ""
    if filters:
        filter_clause = "AND " + " AND ".join(filters)

    # 计算年同比和周环比需要的日期范围
    yoy_year = current_year - 1
    yoy_week_start, yoy_week_end = get_week_date_range(yoy_year, base_week)

    wow_week = base_week - 1
    wow_year = current_year
    if wow_week <= 0:
        wow_week = 52
        wow_year -= 1
    wow_week_start, wow_week_end = get_week_date_range(wow_year, wow_week)

    # 使用CTE查询包含年同比和周环比的数据
    def build_top10_sql(field_name):
        return f"""
        WITH current_week AS (
            SELECT
                {field_name} as name,
                COALESCE(SUM(gmv), 0) as current_gmv,
                COALESCE(SUM(sales_volume), 0) as current_volume
            FROM dws_o2o_sales_d
            WHERE ds BETWEEN '{week_start}' AND '{week_end}'
            AND {field_name} IS NOT NULL
            AND {field_name} != ''
            {filter_clause}
            GROUP BY {field_name}
        ),
        yoy_week AS (
            SELECT
                {field_name} as name,
                COALESCE(SUM(gmv), 0) as yoy_gmv
            FROM dws_o2o_sales_d
            WHERE ds BETWEEN '{yoy_week_start}' AND '{yoy_week_end}'
            AND {field_name} IS NOT NULL
            AND {field_name} != ''
            {filter_clause}
            GROUP BY {field_name}
        ),
        wow_week AS (
            SELECT
                {field_name} as name,
                COALESCE(SUM(gmv), 0) as wow_gmv
            FROM dws_o2o_sales_d
            WHERE ds BETWEEN '{wow_week_start}' AND '{wow_week_end}'
            AND {field_name} IS NOT NULL
            AND {field_name} != ''
            {filter_clause}
            GROUP BY {field_name}
        )
        SELECT
            c.name,
            c.current_gmv as gmv,
            c.current_volume as sales_volume,
            COALESCE(y.yoy_gmv, 0) as yoy_gmv,
            COALESCE(w.wow_gmv, 0) as wow_gmv
        FROM current_week c
        LEFT JOIN yoy_week y ON c.name = y.name
        LEFT JOIN wow_week w ON c.name = w.name
        ORDER BY c.current_gmv DESC
        LIMIT 10
        """

    # 构建三个查询
    products_sql = build_top10_sql('product_name')
    stores_sql = build_top10_sql('vender_name')  # 修正：使用vender_name字段作为零售商
    cities_sql = build_top10_sql('standard_city')

    try:
        products_result = execute_query(products_sql)
        stores_result = execute_query(stores_sql)
        cities_result = execute_query(cities_sql)

        # 格式化数据，包含年同比和周环比计算
        def format_top10_data(results):
            data = []
            for i, row in enumerate(results, 1):
                current_gmv = float(row['gmv'])
                yoy_gmv = float(row['yoy_gmv']) if row['yoy_gmv'] else 0
                wow_gmv = float(row['wow_gmv']) if row['wow_gmv'] else 0

                # 计算年同比和周环比（小数形式）
                yoy = (current_gmv - yoy_gmv) / yoy_gmv if yoy_gmv > 0 else 0
                wow = (current_gmv - wow_gmv) / wow_gmv if wow_gmv > 0 else 0

                data.append({
                    'rank': i,
                    'name': row['name'],
                    'gmv': current_gmv,
                    'sales_volume': int(row['sales_volume']),
                    'yoy': yoy,  # 年同比（小数形式）
                    'wow': wow   # 周环比（小数形式）
                })
            return data

        products_data = format_top10_data(products_result)
        stores_data = format_top10_data(stores_result)
        cities_data = format_top10_data(cities_result)

        logger.info(f"Successfully retrieved top10 data with YoY/WoW calculations for brand '{brand}'")

        return {
            'products': products_data,
            'stores': stores_data,
            'cities': cities_data
        }

    except Exception as e:
        logger.error(f"Error getting top10 data: {e}")
        return {
            'products': [],
            'stores': [],
            'cities': []
        }

def get_user_data_summary_real(week, brand=''):
    """
    获取用户数据概览 - 真实数据

    Args:
        week: 周数
        brand: 品牌名称

    Returns:
        dict: 用户数据概览
    """
    try:
        current_year = datetime.now().year
        week_start, week_end = get_week_date_range(current_year, week)

        # 转换日期格式为yyyymmdd
        week_start_yyyymmdd = week_start.replace('-', '')
        week_end_yyyymmdd = week_end.replace('-', '')

        # 构建过滤条件 - 不使用sub_brand过滤，因为数据中没有'全部品牌'
        filters = []
        if brand:
            filters.append(f"brand = '{brand}'")

        filter_clause = ""
        if filters:
            filter_clause = "AND " + " AND ".join(filters)

        # 获取去年同周数据用于年同比计算
        last_year = current_year - 1
        last_year_week_start, last_year_week_end = get_week_date_range(last_year, week)
        last_year_week_start_yyyymmdd = last_year_week_start.replace('-', '')
        last_year_week_end_yyyymmdd = last_year_week_end.replace('-', '')

        # 获取上一周数据用于周环比计算
        prev_week = week - 1
        prev_year = current_year
        if prev_week <= 0:
            prev_week = 52
            prev_year = current_year - 1
        prev_week_start, prev_week_end = get_week_date_range(prev_year, prev_week)
        prev_week_start_yyyymmdd = prev_week_start.replace('-', '')
        prev_week_end_yyyymmdd = prev_week_end.replace('-', '')

        # 合并查询：当前周、去年同周、上一周的新客和老客数据
        combined_sql = f"""
        SELECT
            'current_new' as data_type,
            COALESCE(SUM(usernum), 0) as usernum,
            COALESCE(SUM(brandoriginproductpricegmv), 0) as gmv,
            COALESCE(AVG(brandorigincustprice), 0) as avg_price
        FROM ods_heyin_bi_data_t_mt_user_list_by_day
        WHERE dt BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}'
        AND userstatus = '新用户'
        {filter_clause}

        UNION ALL

        SELECT
            'current_old' as data_type,
            COALESCE(SUM(usernum), 0) as usernum,
            COALESCE(SUM(brandoriginproductpricegmv), 0) as gmv,
            COALESCE(AVG(brandorigincustprice), 0) as avg_price
        FROM ods_heyin_bi_data_t_mt_user_list_by_day
        WHERE dt BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}'
        AND userstatus = '老用户'
        {filter_clause}

        UNION ALL

        SELECT
            'last_year_new' as data_type,
            COALESCE(SUM(usernum), 0) as usernum,
            COALESCE(SUM(brandoriginproductpricegmv), 0) as gmv,
            COALESCE(AVG(brandorigincustprice), 0) as avg_price
        FROM ods_heyin_bi_data_t_mt_user_list_by_day
        WHERE dt BETWEEN '{last_year_week_start_yyyymmdd}' AND '{last_year_week_end_yyyymmdd}'
        AND userstatus = '新用户'
        {filter_clause}

        UNION ALL

        SELECT
            'last_year_old' as data_type,
            COALESCE(SUM(usernum), 0) as usernum,
            COALESCE(SUM(brandoriginproductpricegmv), 0) as gmv,
            COALESCE(AVG(brandorigincustprice), 0) as avg_price
        FROM ods_heyin_bi_data_t_mt_user_list_by_day
        WHERE dt BETWEEN '{last_year_week_start_yyyymmdd}' AND '{last_year_week_end_yyyymmdd}'
        AND userstatus = '老用户'
        {filter_clause}

        UNION ALL

        SELECT
            'prev_week_new' as data_type,
            COALESCE(SUM(usernum), 0) as usernum,
            COALESCE(SUM(brandoriginproductpricegmv), 0) as gmv,
            COALESCE(AVG(brandorigincustprice), 0) as avg_price
        FROM ods_heyin_bi_data_t_mt_user_list_by_day
        WHERE dt BETWEEN '{prev_week_start_yyyymmdd}' AND '{prev_week_end_yyyymmdd}'
        AND userstatus = '新用户'
        {filter_clause}

        UNION ALL

        SELECT
            'prev_week_old' as data_type,
            COALESCE(SUM(usernum), 0) as usernum,
            COALESCE(SUM(brandoriginproductpricegmv), 0) as gmv,
            COALESCE(AVG(brandorigincustprice), 0) as avg_price
        FROM ods_heyin_bi_data_t_mt_user_list_by_day
        WHERE dt BETWEEN '{prev_week_start_yyyymmdd}' AND '{prev_week_end_yyyymmdd}'
        AND userstatus = '老用户'
        {filter_clause}
        """

        results = execute_query(combined_sql)

        # 处理查询结果
        data_dict = {row['data_type']: row for row in results}

        # 提取数据
        current_new = data_dict.get('current_new', {'usernum': 0, 'gmv': 0, 'avg_price': 0})
        current_old = data_dict.get('current_old', {'usernum': 0, 'gmv': 0, 'avg_price': 0})
        last_year_new = data_dict.get('last_year_new', {'usernum': 0, 'gmv': 0, 'avg_price': 0})
        last_year_old = data_dict.get('last_year_old', {'usernum': 0, 'gmv': 0, 'avg_price': 0})
        prev_week_new = data_dict.get('prev_week_new', {'usernum': 0, 'gmv': 0, 'avg_price': 0})
        prev_week_old = data_dict.get('prev_week_old', {'usernum': 0, 'gmv': 0, 'avg_price': 0})

        # 计算新客指标
        new_user_count = int(current_new['usernum'])
        new_user_gmv = float(current_new['gmv'])
        new_user_avg_price = float(current_new['avg_price'])

        # 计算新客年同比和周环比
        new_user_yoy = (new_user_count - int(last_year_new['usernum'])) / int(last_year_new['usernum']) if int(last_year_new['usernum']) > 0 else 0
        new_user_wow = (new_user_count - int(prev_week_new['usernum'])) / int(prev_week_new['usernum']) if int(prev_week_new['usernum']) > 0 else 0

        new_gmv_yoy = (new_user_gmv - float(last_year_new['gmv'])) / float(last_year_new['gmv']) if float(last_year_new['gmv']) > 0 else 0
        new_gmv_wow = (new_user_gmv - float(prev_week_new['gmv'])) / float(prev_week_new['gmv']) if float(prev_week_new['gmv']) > 0 else 0

        new_price_yoy = (new_user_avg_price - float(last_year_new['avg_price'])) / float(last_year_new['avg_price']) if float(last_year_new['avg_price']) > 0 else 0
        new_price_wow = (new_user_avg_price - float(prev_week_new['avg_price'])) / float(prev_week_new['avg_price']) if float(prev_week_new['avg_price']) > 0 else 0

        # 计算老客指标
        old_user_count = int(current_old['usernum'])
        old_user_gmv = float(current_old['gmv'])
        old_user_avg_price = float(current_old['avg_price'])

        # 计算老客年同比和周环比
        old_user_yoy = (old_user_count - int(last_year_old['usernum'])) / int(last_year_old['usernum']) if int(last_year_old['usernum']) > 0 else 0
        old_user_wow = (old_user_count - int(prev_week_old['usernum'])) / int(prev_week_old['usernum']) if int(prev_week_old['usernum']) > 0 else 0

        old_gmv_yoy = (old_user_gmv - float(last_year_old['gmv'])) / float(last_year_old['gmv']) if float(last_year_old['gmv']) > 0 else 0
        old_gmv_wow = (old_user_gmv - float(prev_week_old['gmv'])) / float(prev_week_old['gmv']) if float(prev_week_old['gmv']) > 0 else 0

        old_price_yoy = (old_user_avg_price - float(last_year_old['avg_price'])) / float(last_year_old['avg_price']) if float(last_year_old['avg_price']) > 0 else 0
        old_price_wow = (old_user_avg_price - float(prev_week_old['avg_price'])) / float(prev_week_old['avg_price']) if float(prev_week_old['avg_price']) > 0 else 0

        logger.info(f"Successfully retrieved user data summary for week {week}, brand '{brand}'")

        return {
            'new_user': {
                'count': new_user_count,
                'count_yoy': new_user_yoy,
                'count_wow': new_user_wow,
                'gmv': new_user_gmv,
                'gmv_yoy': new_gmv_yoy,
                'gmv_wow': new_gmv_wow,
                'avg_price': new_user_avg_price,
                'avg_price_yoy': new_price_yoy,
                'avg_price_wow': new_price_wow
            },
            'old_user': {
                'count': old_user_count,
                'count_yoy': old_user_yoy,
                'count_wow': old_user_wow,
                'gmv': old_user_gmv,
                'gmv_yoy': old_gmv_yoy,
                'gmv_wow': old_gmv_wow,
                'avg_price': old_user_avg_price,
                'avg_price_yoy': old_price_yoy,
                'avg_price_wow': old_price_wow
            }
        }

    except Exception as e:
        logger.error(f"Error getting user data summary: {e}")
        # 返回mock数据
        return {
            'new_user': {
                'count': 5000,
                'count_yoy': 0.15,
                'count_wow': 0.05,
                'gmv': 2500000,
                'gmv_yoy': 0.20,
                'gmv_wow': 0.08,
                'avg_price': 500,
                'avg_price_yoy': 0.10,
                'avg_price_wow': 0.03
            },
            'old_user': {
                'count': 15000,
                'count_yoy': 0.08,
                'count_wow': 0.02,
                'gmv': 12000000,
                'gmv_yoy': 0.12,
                'gmv_wow': 0.04,
                'avg_price': 800,
                'avg_price_yoy': 0.05,
                'avg_price_wow': 0.01
            }
        }

def get_user_data_trends_real(brand=''):
    """
    获取用户数据趋势 - 真实数据（近8周）

    Args:
        brand: 品牌名称

    Returns:
        dict: 用户数据趋势
    """
    try:
        current_year = datetime.now().year
        current_week = get_current_week_number()

        # 构建过滤条件 - 不使用sub_brand过滤
        filters = []
        if brand:
            filters.append(f"brand = '{brand}'")

        filter_clause = ""
        if filters:
            filter_clause = "AND " + " AND ".join(filters)

        # 获取最近8周的数据：从t-1周开始往前推8周
        trend_data = []

        # 构建所有需要的日期范围
        date_ranges = []
        for week_offset in range(1, 9):  # 从1开始，往前推8周
            target_week = current_week - week_offset
            year = current_year

            if target_week <= 0:
                target_week += 52
                year = current_year - 1

            week_start, week_end = get_week_date_range(year, target_week)
            # 转换日期格式为yyyymmdd
            week_start_yyyymmdd = week_start.replace('-', '')
            week_end_yyyymmdd = week_end.replace('-', '')
            date_ranges.append((target_week, year, week_start_yyyymmdd, week_end_yyyymmdd))

        # 反转列表，使其按时间顺序排列（从早到晚）
        date_ranges.reverse()

        # 使用单个查询获取所有周的数据
        all_conditions = []
        for target_week, year, week_start_yyyymmdd, week_end_yyyymmdd in date_ranges:
            all_conditions.append(f"(dt BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}')")

        combined_sql = f"""
        SELECT
            dt,
            userstatus,
            COALESCE(SUM(usernum), 0) as usernum,
            COALESCE(SUM(brandoriginproductpricegmv), 0) as gmv,
            COALESCE(AVG(brandorigincustprice), 0) as avg_price
        FROM ods_heyin_bi_data_t_mt_user_list_by_day
        WHERE ({' OR '.join(all_conditions)})
        AND userstatus IN ('新用户', '老用户')
        {filter_clause}
        GROUP BY dt, userstatus
        ORDER BY dt, userstatus
        """

        all_results = execute_query(combined_sql)

        # 按日期和用户类型组织数据
        data_by_date = {}
        for row in all_results:
            dt = row['dt']
            userstatus = row['userstatus']

            if dt not in data_by_date:
                data_by_date[dt] = {}

            data_by_date[dt][userstatus] = {
                'usernum': int(row['usernum']),
                'gmv': float(row['gmv']),
                'avg_price': float(row['avg_price'])
            }

        # 按周聚合数据
        for target_week, year, week_start_yyyymmdd, week_end_yyyymmdd in date_ranges:
            week_data = {
                'new_user': {'usernum': 0, 'gmv': 0, 'avg_price': 0},
                'old_user': {'usernum': 0, 'gmv': 0, 'avg_price': 0}
            }

            # 累计该周范围内的所有日期数据
            for dt in data_by_date:
                if week_start_yyyymmdd <= dt <= week_end_yyyymmdd:
                    if '新用户' in data_by_date[dt]:
                        week_data['new_user']['usernum'] += data_by_date[dt]['新用户']['usernum']
                        week_data['new_user']['gmv'] += data_by_date[dt]['新用户']['gmv']
                        # 客单价取平均值
                        if data_by_date[dt]['新用户']['avg_price'] > 0:
                            week_data['new_user']['avg_price'] = data_by_date[dt]['新用户']['avg_price']

                    if '老用户' in data_by_date[dt]:
                        week_data['old_user']['usernum'] += data_by_date[dt]['老用户']['usernum']
                        week_data['old_user']['gmv'] += data_by_date[dt]['老用户']['gmv']
                        # 客单价取平均值
                        if data_by_date[dt]['老用户']['avg_price'] > 0:
                            week_data['old_user']['avg_price'] = data_by_date[dt]['老用户']['avg_price']

            trend_data.append({
                'week': target_week,
                'new_user': week_data['new_user'],
                'old_user': week_data['old_user']
            })

        logger.info(f"Successfully retrieved user data trends for brand '{brand}', {len(trend_data)} weeks")

        return {
            'trend_data': trend_data
        }

    except Exception as e:
        logger.error(f"Error getting user data trends: {e}")
        # 返回mock数据
        current_week = get_current_week_number()
        trend_data = []

        for week_offset in range(7, -1, -1):
            target_week = current_week - week_offset
            if target_week <= 0:
                target_week += 52

            trend_data.append({
                'week': target_week,
                'new_user': {
                    'usernum': 5000 + week_offset * 100,
                    'gmv': 2500000 + week_offset * 50000,
                    'avg_price': 500 + week_offset * 10
                },
                'old_user': {
                    'usernum': 15000 + week_offset * 200,
                    'gmv': 12000000 + week_offset * 100000,
                    'avg_price': 800 + week_offset * 15
                }
            })

        return {
            'trend_data': trend_data
        }

def get_user_platform_charts_real(brand=''):
    """
    获取用户数据分平台图表数据 - 真实数据（最新完整周）

    Args:
        brand: 品牌名称

    Returns:
        dict: 分平台用户数据
    """
    try:
        current_year = datetime.now().year
        current_week = get_current_week_number()

        # 使用最后一个完整周（当前周-1）
        target_week = current_week - 1
        target_year = current_year

        # 处理跨年情况
        if target_week <= 0:
            target_week += 52
            target_year -= 1

        week_start, week_end = get_week_date_range(target_year, target_week)

        # 转换为yyyymmdd格式（用户数据表使用的格式）
        week_start_yyyymmdd = week_start.replace('-', '')
        week_end_yyyymmdd = week_end.replace('-', '')

        # 构建过滤条件
        filters = []
        if brand:
            filters.append(f"brand = '{brand}'")

        filter_clause = ""
        if filters:
            filter_clause = "AND " + " AND ".join(filters)

        # 先获取整体用户数据（不分平台）
        user_sql = f"""
        SELECT
            userstatus,
            COALESCE(SUM(usernum), 0) as user_count,
            COALESCE(SUM(brandoriginproductpricegmv), 0) as gmv,
            COALESCE(AVG(brandorigincustprice), 0) as avg_price
        FROM ods_heyin_bi_data_t_mt_user_list_by_day
        WHERE dt BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}'
        AND userstatus IN ('新用户', '老用户')
        {filter_clause}
        GROUP BY userstatus
        ORDER BY userstatus
        """

        user_results = execute_query(user_sql)

        # 获取该品牌有销售数据的平台列表
        platform_sql = f"""
        SELECT DISTINCT platform
        FROM dws_o2o_sales_d
        WHERE date BETWEEN '{week_start}' AND '{week_end}'
        AND platform IS NOT NULL
        AND platform != ''
        {filter_clause.replace('brand', 'brand')}
        ORDER BY platform
        """

        platform_results = execute_query(platform_sql)
        platforms = [row['platform'] for row in platform_results] if platform_results else ['美团', '饿了么', '京东到家']

        # 处理用户数据
        user_data = {}
        for row in user_results:
            userstatus = row['userstatus']
            user_count = int(row['user_count'])
            gmv = float(row['gmv'])
            avg_price = float(row['avg_price'])

            user_data[userstatus] = {
                'user_count': user_count,
                'gmv': gmv,
                'avg_price': avg_price
            }

        # 为每个平台分配用户数据（按平台数量平均分配）
        platform_data = []
        if user_data and platforms:
            platform_count = len(platforms)

            for platform in platforms:
                new_user_data = user_data.get('新用户', {'user_count': 0, 'gmv': 0, 'avg_price': 0})
                old_user_data = user_data.get('老用户', {'user_count': 0, 'gmv': 0, 'avg_price': 0})

                # 按平台数量平均分配（简单分配策略）
                platform_data.append({
                    'platform': platform,
                    'new_user_count': int(new_user_data['user_count'] / platform_count),
                    'old_user_count': int(old_user_data['user_count'] / platform_count),
                    'new_user_gmv': new_user_data['gmv'] / platform_count,
                    'old_user_gmv': old_user_data['gmv'] / platform_count,
                    'new_user_arpu': new_user_data['avg_price'],
                    'old_user_arpu': old_user_data['avg_price']
                })

        # 过滤掉新老客数据都为0的平台
        filtered_data = []
        for data in platform_data:
            if data['new_user_count'] > 0 or data['old_user_count'] > 0:
                filtered_data.append(data)

        logger.info(f"Successfully retrieved user platform charts for brand '{brand}', {len(filtered_data)} platforms")
        return {'platform_data': filtered_data}

    except Exception as e:
        logger.error(f"Error getting user platform charts: {e}")
        # 返回默认数据
        return {'platform_data': []}

def get_user_data_detail_real(brand=''):
    """
    获取用户数据明细 - 真实数据（近8周，包含年同比和周环比）

    Args:
        brand: 品牌名称

    Returns:
        dict: 用户数据明细
    """
    try:
        current_year = datetime.now().year
        current_week = get_current_week_number()

        # 构建过滤条件 - 不使用sub_brand过滤
        filters = []
        if brand:
            filters.append(f"brand = '{brand}'")

        filter_clause = ""
        if filters:
            filter_clause = "AND " + " AND ".join(filters)

        # 获取最近8周的数据：从t-1周开始往前推8周
        detail_data = []

        # 构建所有需要的日期范围（包括年同比和周环比数据）
        all_date_ranges = []
        week_mapping = {}

        for week_offset in range(1, 9):  # 从1开始，往前推8周
            target_week = current_week - week_offset
            year = current_year

            if target_week <= 0:
                target_week += 52
                year = current_year - 1

            week_start, week_end = get_week_date_range(year, target_week)
            week_start_yyyymmdd = week_start.replace('-', '')
            week_end_yyyymmdd = week_end.replace('-', '')

            # 当前周数据
            all_date_ranges.append(f"(dt BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}')")
            week_mapping[f"{week_start_yyyymmdd}_{week_end_yyyymmdd}"] = {'week': target_week, 'year': year, 'type': 'current'}

            # 年同比数据
            yoy_year = year - 1
            yoy_week_start, yoy_week_end = get_week_date_range(yoy_year, target_week)
            yoy_week_start_yyyymmdd = yoy_week_start.replace('-', '')
            yoy_week_end_yyyymmdd = yoy_week_end.replace('-', '')
            all_date_ranges.append(f"(dt BETWEEN '{yoy_week_start_yyyymmdd}' AND '{yoy_week_end_yyyymmdd}')")
            week_mapping[f"{yoy_week_start_yyyymmdd}_{yoy_week_end_yyyymmdd}"] = {'week': target_week, 'year': yoy_year, 'type': 'yoy'}

            # 周环比数据（上一周）
            wow_week = target_week - 1
            wow_year = year
            if wow_week <= 0:
                wow_week = 52
                wow_year = year - 1
            wow_week_start, wow_week_end = get_week_date_range(wow_year, wow_week)
            wow_week_start_yyyymmdd = wow_week_start.replace('-', '')
            wow_week_end_yyyymmdd = wow_week_end.replace('-', '')
            all_date_ranges.append(f"(dt BETWEEN '{wow_week_start_yyyymmdd}' AND '{wow_week_end_yyyymmdd}')")
            week_mapping[f"{wow_week_start_yyyymmdd}_{wow_week_end_yyyymmdd}"] = {'week': wow_week, 'year': wow_year, 'type': 'wow', 'for_week': target_week}

        # 使用单个查询获取所有数据
        combined_sql = f"""
        SELECT
            dt,
            userstatus,
            COALESCE(SUM(usernum), 0) as usernum,
            COALESCE(SUM(brandoriginproductpricegmv), 0) as gmv,
            COALESCE(AVG(brandorigincustprice), 0) as avg_price
        FROM ods_heyin_bi_data_t_mt_user_list_by_day
        WHERE ({' OR '.join(all_date_ranges)})
        AND userstatus IN ('新用户', '老用户')
        {filter_clause}
        GROUP BY dt, userstatus
        ORDER BY dt, userstatus
        """

        all_results = execute_query(combined_sql)

        # 按日期和用户类型组织数据
        data_by_date = {}
        for row in all_results:
            dt = row['dt']
            userstatus = row['userstatus']

            if dt not in data_by_date:
                data_by_date[dt] = {}

            data_by_date[dt][userstatus] = {
                'usernum': int(row['usernum']),
                'gmv': float(row['gmv']),
                'avg_price': float(row['avg_price'])
            }

        # 按周聚合数据并计算年同比和周环比（倒序处理，最新的周在前）
        for week_offset in range(1, 9):  # 从1开始，往前推8周
            target_week = current_week - week_offset
            year = current_year

            if target_week <= 0:
                target_week += 52
                year = current_year - 1

            week_start, week_end = get_week_date_range(year, target_week)
            week_start_yyyymmdd = week_start.replace('-', '')
            week_end_yyyymmdd = week_end.replace('-', '')

            # 聚合当前周数据
            current_new = {'usernum': 0, 'gmv': 0, 'avg_price': 0}
            current_old = {'usernum': 0, 'gmv': 0, 'avg_price': 0}

            for dt in data_by_date:
                if week_start_yyyymmdd <= dt <= week_end_yyyymmdd:
                    if '新用户' in data_by_date[dt]:
                        current_new['usernum'] += data_by_date[dt]['新用户']['usernum']
                        current_new['gmv'] += data_by_date[dt]['新用户']['gmv']
                        if data_by_date[dt]['新用户']['avg_price'] > 0:
                            current_new['avg_price'] = data_by_date[dt]['新用户']['avg_price']

                    if '老用户' in data_by_date[dt]:
                        current_old['usernum'] += data_by_date[dt]['老用户']['usernum']
                        current_old['gmv'] += data_by_date[dt]['老用户']['gmv']
                        if data_by_date[dt]['老用户']['avg_price'] > 0:
                            current_old['avg_price'] = data_by_date[dt]['老用户']['avg_price']

            # 聚合年同比数据
            yoy_year = year - 1
            yoy_week_start, yoy_week_end = get_week_date_range(yoy_year, target_week)
            yoy_week_start_yyyymmdd = yoy_week_start.replace('-', '')
            yoy_week_end_yyyymmdd = yoy_week_end.replace('-', '')
            yoy_new = {'usernum': 0, 'gmv': 0, 'avg_price': 0}
            yoy_old = {'usernum': 0, 'gmv': 0, 'avg_price': 0}

            for dt in data_by_date:
                if yoy_week_start_yyyymmdd <= dt <= yoy_week_end_yyyymmdd:
                    if '新用户' in data_by_date[dt]:
                        yoy_new['usernum'] += data_by_date[dt]['新用户']['usernum']
                        yoy_new['gmv'] += data_by_date[dt]['新用户']['gmv']
                        if data_by_date[dt]['新用户']['avg_price'] > 0:
                            yoy_new['avg_price'] = data_by_date[dt]['新用户']['avg_price']

                    if '老用户' in data_by_date[dt]:
                        yoy_old['usernum'] += data_by_date[dt]['老用户']['usernum']
                        yoy_old['gmv'] += data_by_date[dt]['老用户']['gmv']
                        if data_by_date[dt]['老用户']['avg_price'] > 0:
                            yoy_old['avg_price'] = data_by_date[dt]['老用户']['avg_price']

            # 聚合周环比数据
            wow_week = target_week - 1
            wow_year = year
            if wow_week <= 0:
                wow_week = 52
                wow_year = year - 1
            wow_week_start, wow_week_end = get_week_date_range(wow_year, wow_week)
            wow_week_start_yyyymmdd = wow_week_start.replace('-', '')
            wow_week_end_yyyymmdd = wow_week_end.replace('-', '')
            wow_new = {'usernum': 0, 'gmv': 0, 'avg_price': 0}
            wow_old = {'usernum': 0, 'gmv': 0, 'avg_price': 0}

            for dt in data_by_date:
                if wow_week_start_yyyymmdd <= dt <= wow_week_end_yyyymmdd:
                    if '新用户' in data_by_date[dt]:
                        wow_new['usernum'] += data_by_date[dt]['新用户']['usernum']
                        wow_new['gmv'] += data_by_date[dt]['新用户']['gmv']
                        if data_by_date[dt]['新用户']['avg_price'] > 0:
                            wow_new['avg_price'] = data_by_date[dt]['新用户']['avg_price']

                    if '老用户' in data_by_date[dt]:
                        wow_old['usernum'] += data_by_date[dt]['老用户']['usernum']
                        wow_old['gmv'] += data_by_date[dt]['老用户']['gmv']
                        if data_by_date[dt]['老用户']['avg_price'] > 0:
                            wow_old['avg_price'] = data_by_date[dt]['老用户']['avg_price']

            # 计算总计和占比
            total_user_count = current_new['usernum'] + current_old['usernum']
            total_gmv = current_new['gmv'] + current_old['gmv']

            new_user_ratio = current_new['usernum'] / total_user_count if total_user_count > 0 else 0
            old_user_ratio = current_old['usernum'] / total_user_count if total_user_count > 0 else 0
            new_user_gmv_ratio = current_new['gmv'] / total_gmv if total_gmv > 0 else 0
            old_user_gmv_ratio = current_old['gmv'] / total_gmv if total_gmv > 0 else 0

            # 计算年同比和周环比
            new_user_yoy = (current_new['usernum'] - yoy_new['usernum']) / yoy_new['usernum'] if yoy_new['usernum'] > 0 else 0
            old_user_yoy = (current_old['usernum'] - yoy_old['usernum']) / yoy_old['usernum'] if yoy_old['usernum'] > 0 else 0
            new_user_wow = (current_new['usernum'] - wow_new['usernum']) / wow_new['usernum'] if wow_new['usernum'] > 0 else 0
            old_user_wow = (current_old['usernum'] - wow_old['usernum']) / wow_old['usernum'] if wow_old['usernum'] > 0 else 0

            new_user_gmv_yoy = (current_new['gmv'] - yoy_new['gmv']) / yoy_new['gmv'] if yoy_new['gmv'] > 0 else 0
            old_user_gmv_yoy = (current_old['gmv'] - yoy_old['gmv']) / yoy_old['gmv'] if yoy_old['gmv'] > 0 else 0
            new_user_gmv_wow = (current_new['gmv'] - wow_new['gmv']) / wow_new['gmv'] if wow_new['gmv'] > 0 else 0
            old_user_gmv_wow = (current_old['gmv'] - wow_old['gmv']) / wow_old['gmv'] if wow_old['gmv'] > 0 else 0

            new_user_arpu_yoy = (current_new['avg_price'] - yoy_new['avg_price']) / yoy_new['avg_price'] if yoy_new['avg_price'] > 0 else 0
            old_user_arpu_yoy = (current_old['avg_price'] - yoy_old['avg_price']) / yoy_old['avg_price'] if yoy_old['avg_price'] > 0 else 0
            new_user_arpu_wow = (current_new['avg_price'] - wow_new['avg_price']) / wow_new['avg_price'] if wow_new['avg_price'] > 0 else 0
            old_user_arpu_wow = (current_old['avg_price'] - wow_old['avg_price']) / wow_old['avg_price'] if wow_old['avg_price'] > 0 else 0

            detail_data.append({
                'week': target_week,
                'new_user_count': current_new['usernum'],
                'new_user_ratio': new_user_ratio,
                'new_user_yoy': new_user_yoy,
                'new_user_wow': new_user_wow,
                'new_user_gmv': current_new['gmv'],
                'new_user_gmv_ratio': new_user_gmv_ratio,
                'new_user_gmv_yoy': new_user_gmv_yoy,
                'new_user_gmv_wow': new_user_gmv_wow,
                'new_user_arpu': current_new['avg_price'],
                'new_user_arpu_yoy': new_user_arpu_yoy,
                'new_user_arpu_wow': new_user_arpu_wow,
                'old_user_count': current_old['usernum'],
                'old_user_ratio': old_user_ratio,
                'old_user_yoy': old_user_yoy,
                'old_user_wow': old_user_wow,
                'old_user_gmv': current_old['gmv'],
                'old_user_gmv_ratio': old_user_gmv_ratio,
                'old_user_gmv_yoy': old_user_gmv_yoy,
                'old_user_gmv_wow': old_user_gmv_wow,
                'old_user_arpu': current_old['avg_price'],
                'old_user_arpu_yoy': old_user_arpu_yoy,
                'old_user_arpu_wow': old_user_arpu_wow,
                'total_user_count': total_user_count,
                'total_gmv': total_gmv
            })

        # 保持数据按周次倒序排列（最新的周在最上面）
        # detail_data.reverse()  # 注释掉反转，保持倒序

        logger.info(f"Successfully retrieved user data detail for brand '{brand}', {len(detail_data)} weeks")

        return detail_data

    except Exception as e:
        logger.error(f"Error getting user data detail: {e}")
        # 返回mock数据
        current_week = get_current_week_number()
        detail_data = []

        for week_offset in range(1, 9):  # 与真实数据保持一致，从1开始往前推8周
            target_week = current_week - week_offset
            if target_week <= 0:
                target_week += 52

            # Mock数据
            new_user_count = random.randint(18000, 42000)
            new_user_gmv = round(random.uniform(1800000, 4500000), 2)
            new_user_arpu = round(new_user_gmv / new_user_count, 2) if new_user_count > 0 else 0

            old_user_count = random.randint(45000, 95000)
            old_user_gmv = round(random.uniform(5500000, 13000000), 2)
            old_user_arpu = round(old_user_gmv / old_user_count, 2) if old_user_count > 0 else 0

            total_user_count = new_user_count + old_user_count
            total_gmv = new_user_gmv + old_user_gmv

            detail_data.append({
                'week': target_week,
                'new_user_count': new_user_count,
                'new_user_ratio': round(new_user_count / total_user_count, 3),
                'new_user_yoy': round(random.uniform(-0.2, 0.3), 3),
                'new_user_wow': round(random.uniform(-0.2, 0.3), 3),
                'new_user_gmv': new_user_gmv,
                'new_user_gmv_ratio': round(new_user_gmv / total_gmv, 3),
                'new_user_gmv_yoy': round(random.uniform(-0.25, 0.35), 3),
                'new_user_gmv_wow': round(random.uniform(-0.25, 0.35), 3),
                'new_user_arpu': new_user_arpu,
                'new_user_arpu_yoy': round(random.uniform(-0.1, 0.2), 3),
                'new_user_arpu_wow': round(random.uniform(-0.1, 0.2), 3),
                'old_user_count': old_user_count,
                'old_user_ratio': round(old_user_count / total_user_count, 3),
                'old_user_yoy': round(random.uniform(-0.15, 0.25), 3),
                'old_user_wow': round(random.uniform(-0.15, 0.25), 3),
                'old_user_gmv': old_user_gmv,
                'old_user_gmv_ratio': round(old_user_gmv / total_gmv, 3),
                'old_user_gmv_yoy': round(random.uniform(-0.2, 0.3), 3),
                'old_user_gmv_wow': round(random.uniform(-0.2, 0.3), 3),
                'old_user_arpu': old_user_arpu,
                'old_user_arpu_yoy': round(random.uniform(-0.1, 0.2), 3),
                'old_user_arpu_wow': round(random.uniform(-0.1, 0.2), 3),
                'total_user_count': total_user_count,
                'total_gmv': total_gmv
            })

        return detail_data

def get_activity_summary_real(week, brand='', platform='全平台'):
    """
    获取活动数据概览 - 真实数据

    Args:
        week: 周数
        brand: 品牌名称
        platform: 平台名称

    Returns:
        dict: 活动数据概览
    """
    try:
        current_year = datetime.now().year
        week_start, week_end = get_week_date_range(current_year, week)

        # 转换日期格式为yyyymmdd（活动表的ds格式）
        week_start_yyyymmdd = week_start.replace('-', '')
        week_end_yyyymmdd = week_end.replace('-', '')

        # 构建过滤条件
        filters = []
        if brand:
            filters.append(f"brand = '{brand}'")
        if platform != '全平台':
            filters.append(f"platform = '{platform}'")

        filter_clause = ""
        if filters:
            filter_clause = "AND " + " AND ".join(filters)

        # 获取去年同周数据用于年同比计算
        last_year = current_year - 1
        last_year_week_start, last_year_week_end = get_week_date_range(last_year, week)
        last_year_week_start_yyyymmdd = last_year_week_start.replace('-', '')
        last_year_week_end_yyyymmdd = last_year_week_end.replace('-', '')

        # 合并查询当前周和去年同周的数据
        combined_sql = f"""
        SELECT
            'current' as period_type,
            COALESCE(SUM(activity_gmv), 0) as activity_gmv,
            COALESCE(SUM(gmv), 0) as total_gmv,
            COALESCE(SUM(activity_expense), 0) as activity_expense
        FROM dws_o2o_sale_activity_detail_analysis_d
        WHERE ds BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}'
        {filter_clause}

        UNION ALL

        SELECT
            'last_year' as period_type,
            COALESCE(SUM(activity_gmv), 0) as activity_gmv,
            COALESCE(SUM(gmv), 0) as total_gmv,
            COALESCE(SUM(activity_expense), 0) as activity_expense
        FROM dws_o2o_sale_activity_detail_analysis_d
        WHERE ds BETWEEN '{last_year_week_start_yyyymmdd}' AND '{last_year_week_end_yyyymmdd}'
        {filter_clause}
        """

        results = execute_query(combined_sql)

        # 处理查询结果
        data_dict = {row['period_type']: row for row in results}

        current_data = data_dict.get('current', {
            'activity_gmv': 0, 'total_gmv': 0, 'activity_expense': 0
        })
        last_year_data = data_dict.get('last_year', {
            'activity_gmv': 0, 'total_gmv': 0, 'activity_expense': 0
        })

        # 提取当前周数据
        current_activity_gmv = float(current_data['activity_gmv'])
        current_total_gmv = float(current_data['total_gmv'])
        current_activity_expense = float(current_data['activity_expense'])

        # 提取去年同周数据
        last_year_activity_gmv = float(last_year_data['activity_gmv'])
        last_year_total_gmv = float(last_year_data['total_gmv'])
        last_year_activity_expense = float(last_year_data['activity_expense'])

        # 计算各项指标
        # 活动GMV占比
        activity_gmv_ratio = current_activity_gmv / current_total_gmv if current_total_gmv > 0 else 0
        last_year_activity_gmv_ratio = last_year_activity_gmv / last_year_total_gmv if last_year_total_gmv > 0 else 0

        # 活动费比 = 核销金额 / 活动GMV
        activity_cost_ratio = current_activity_expense / current_activity_gmv if current_activity_gmv > 0 else 0
        last_year_activity_cost_ratio = last_year_activity_expense / last_year_activity_gmv if last_year_activity_gmv > 0 else 0

        # 全量费比 = 核销金额 / 总GMV
        total_cost_ratio = current_activity_expense / current_total_gmv if current_total_gmv > 0 else 0
        last_year_total_cost_ratio = last_year_activity_expense / last_year_total_gmv if last_year_total_gmv > 0 else 0

        # 计算年同比
        activity_gmv_yoy = (current_activity_gmv - last_year_activity_gmv) / last_year_activity_gmv if last_year_activity_gmv > 0 else 0
        activity_gmv_ratio_yoy = activity_gmv_ratio - last_year_activity_gmv_ratio  # pp差值
        verification_amount_yoy = (current_activity_expense - last_year_activity_expense) / last_year_activity_expense if last_year_activity_expense > 0 else 0
        activity_cost_ratio_yoy = activity_cost_ratio - last_year_activity_cost_ratio  # pp差值
        total_cost_ratio_yoy = total_cost_ratio - last_year_total_cost_ratio  # pp差值

        result = {
            'activity_gmv': {
                'value': current_activity_gmv,
                'yoy': activity_gmv_yoy
            },
            'activity_gmv_ratio': {
                'value': activity_gmv_ratio,
                'yoy': activity_gmv_ratio_yoy
            },
            'verification_amount': {
                'value': current_activity_expense,
                'yoy': verification_amount_yoy
            },
            'activity_cost_ratio': {
                'value': activity_cost_ratio,
                'yoy': activity_cost_ratio_yoy
            },
            'total_cost_ratio': {
                'value': total_cost_ratio,
                'yoy': total_cost_ratio_yoy
            }
        }

        logger.info(f"Successfully retrieved activity summary for week {week}, brand '{brand}', platform '{platform}'")
        return result

    except Exception as e:
        logger.error(f"Error getting activity summary: {e}")
        # 返回默认数据
        return {
            'activity_gmv': {'value': 0, 'yoy': 0},
            'activity_gmv_ratio': {'value': 0, 'yoy': 0},
            'verification_amount': {'value': 0, 'yoy': 0},
            'activity_cost_ratio': {'value': 0, 'yoy': 0},
            'total_cost_ratio': {'value': 0, 'yoy': 0}
        }

def get_activity_trends_real(brand='', end_week=None, platform='全平台'):
    """
    获取活动趋势数据 - 真实数据（近8个完整周）

    Args:
        brand: 品牌名称
        end_week: 结束周数，如果不指定则使用上一个完整周
        platform: 平台名称

    Returns:
        dict: 活动趋势数据
    """
    try:
        current_year = datetime.now().year

        # 确定结束周数
        if end_week is None:
            current_week = get_current_week_number()
            end_week = current_week - 1  # 默认使用上一个完整周
        else:
            # 如果指定了end_week，直接使用
            pass

        # 获取近8个完整周的数据（包括指定的结束周）
        weeks_data = []
        for i in range(7, -1, -1):  # 从第7周前到当前指定周
            target_week = end_week - i
            target_year = current_year

            # 处理跨年情况
            if target_week <= 0:
                target_week += 52
                target_year -= 1

            week_start, week_end = get_week_date_range(target_year, target_week)
            week_start_yyyymmdd = week_start.replace('-', '')
            week_end_yyyymmdd = week_end.replace('-', '')

            weeks_data.append({
                'week': target_week,
                'year': target_year,
                'start_yyyymmdd': week_start_yyyymmdd,
                'end_yyyymmdd': week_end_yyyymmdd
            })

        # 构建过滤条件
        filters = []
        if brand:
            filters.append(f"brand = '{brand}'")
        if platform != '全平台':
            filters.append(f"platform = '{platform}'")

        filter_clause = ""
        if filters:
            filter_clause = "AND " + " AND ".join(filters)

        # 构建所有周的查询条件
        week_conditions = []
        for week_info in weeks_data:
            week_conditions.append(
                f"(ds BETWEEN '{week_info['start_yyyymmdd']}' AND '{week_info['end_yyyymmdd']}')"
            )

        # 合并查询所有周的数据
        combined_sql = f"""
        SELECT
            ds,
            COALESCE(SUM(activity_gmv), 0) as activity_gmv,
            COALESCE(SUM(gmv), 0) as total_gmv,
            COALESCE(SUM(activity_expense), 0) as activity_expense
        FROM dws_o2o_sale_activity_detail_analysis_d
        WHERE ({' OR '.join(week_conditions)})
        {filter_clause}
        GROUP BY ds
        ORDER BY ds
        """

        results = execute_query(combined_sql)

        # 按周聚合数据
        trend_data = []
        for week_info in weeks_data:
            week_activity_gmv = 0
            week_total_gmv = 0
            week_activity_expense = 0

            # 聚合该周的数据
            for row in results:
                ds_str = str(row['ds'])
                if week_info['start_yyyymmdd'] <= ds_str <= week_info['end_yyyymmdd']:
                    week_activity_gmv += float(row['activity_gmv'])
                    week_total_gmv += float(row['total_gmv'])
                    week_activity_expense += float(row['activity_expense'])

            # 计算各项比率
            activity_gmv_ratio = week_activity_gmv / week_total_gmv if week_total_gmv > 0 else 0
            activity_cost_ratio = week_activity_expense / week_activity_gmv if week_activity_gmv > 0 else 0
            total_cost_ratio = week_activity_expense / week_total_gmv if week_total_gmv > 0 else 0

            # 查询去年同周数据计算年同比
            last_year = week_info['year'] - 1
            last_year_week_start, last_year_week_end = get_week_date_range(last_year, week_info['week'])
            last_year_start_yyyymmdd = last_year_week_start.replace('-', '')
            last_year_end_yyyymmdd = last_year_week_end.replace('-', '')

            # 查询去年同周活动数据
            last_year_sql = f"""
            SELECT
                COALESCE(SUM(activity_gmv), 0) as activity_gmv,
                COALESCE(SUM(gmv), 0) as total_gmv,
                COALESCE(SUM(activity_expense), 0) as activity_expense
            FROM dws_o2o_sale_activity_detail_analysis_d
            WHERE ds BETWEEN '{last_year_start_yyyymmdd}' AND '{last_year_end_yyyymmdd}'
            {filter_clause}
            """

            try:
                last_year_result = execute_query(last_year_sql)
                last_year_activity_gmv = float(last_year_result[0]['activity_gmv']) if last_year_result else 0
                last_year_total_gmv = float(last_year_result[0]['total_gmv']) if last_year_result else 0
                last_year_activity_expense = float(last_year_result[0]['activity_expense']) if last_year_result else 0

                # 计算去年同期比率
                last_year_activity_gmv_ratio = last_year_activity_gmv / last_year_total_gmv if last_year_total_gmv > 0 else 0
                last_year_activity_cost_ratio = last_year_activity_expense / last_year_activity_gmv if last_year_activity_gmv > 0 else 0
                last_year_total_cost_ratio = last_year_activity_expense / last_year_total_gmv if last_year_total_gmv > 0 else 0

                # 计算年同比
                activity_gmv_yoy = (week_activity_gmv - last_year_activity_gmv) / last_year_activity_gmv if last_year_activity_gmv > 0 else 0
                activity_gmv_ratio_yoy = activity_gmv_ratio - last_year_activity_gmv_ratio  # pp差值
                verification_amount_yoy = (week_activity_expense - last_year_activity_expense) / last_year_activity_expense if last_year_activity_expense > 0 else 0
                activity_cost_ratio_yoy = activity_cost_ratio - last_year_activity_cost_ratio  # pp差值
                total_cost_ratio_yoy = total_cost_ratio - last_year_total_cost_ratio  # pp差值
            except Exception as e:
                logger.warning(f"Error calculating YoY for week {week_info['week']}: {e}")
                # 如果查询失败，设置年同比为0
                activity_gmv_yoy = 0
                activity_gmv_ratio_yoy = 0
                verification_amount_yoy = 0
                activity_cost_ratio_yoy = 0
                total_cost_ratio_yoy = 0

            trend_data.append({
                'week': week_info['week'],
                'activity_gmv': week_activity_gmv,
                'activity_gmv_ratio': activity_gmv_ratio,
                'verification_amount': week_activity_expense,
                'activity_cost_ratio': activity_cost_ratio,
                'total_cost_ratio': total_cost_ratio,
                'activity_gmv_yoy': activity_gmv_yoy,
                'activity_gmv_ratio_yoy': activity_gmv_ratio_yoy,
                'verification_amount_yoy': verification_amount_yoy,
                'activity_cost_ratio_yoy': activity_cost_ratio_yoy,
                'total_cost_ratio_yoy': total_cost_ratio_yoy
            })

        logger.info(f"Successfully retrieved activity trends for brand '{brand}', platform '{platform}', {len(trend_data)} weeks")
        return {'trend_data': trend_data}

    except Exception as e:
        logger.error(f"Error getting activity trends: {e}")
        # 返回默认数据
        current_week = get_current_week_number()
        trend_data = []
        for i in range(8, 0, -1):
            target_week = current_week - i
            if target_week <= 0:
                target_week += 52

            trend_data.append({
                'week': target_week,
                'activity_gmv': 0,
                'activity_gmv_ratio': 0,
                'verification_amount': 0,
                'activity_cost_ratio': 0,
                'total_cost_ratio': 0,
                'activity_gmv_yoy': 0,
                'activity_gmv_ratio_yoy': 0,
                'verification_amount_yoy': 0,
                'activity_cost_ratio_yoy': 0,
                'total_cost_ratio_yoy': 0
            })

        return {'trend_data': trend_data}

def get_activity_detail_real_weeks(brand='', platform='全平台'):
    """
    获取活动详细数据 - 整体维度，最近8个完整周的真实数据

    Args:
        brand: 品牌名称
        platform: 平台名称

    Returns:
        list: 8周的活动详细数据
    """
    try:
        current_year = datetime.now().year
        current_week = get_current_week_number()

        # 计算最近8个完整周（不包括当前周）
        weeks_data = []
        for i in range(8, 0, -1):  # 倒序：从最新周到最早周
            target_week = current_week - i
            target_year = current_year

            # 处理跨年情况
            if target_week <= 0:
                target_week += 52
                target_year -= 1

            week_start, week_end = get_week_date_range(target_year, target_week)
            weeks_data.append({
                'week': target_week,
                'year': target_year,
                'start': week_start,
                'end': week_end,
                'start_yyyymmdd': week_start.replace('-', ''),
                'end_yyyymmdd': week_end.replace('-', '')
            })

        # 构建过滤条件
        filters = []
        if brand and brand.strip():
            filters.append(f"brand = '{brand.strip()}'")
        if platform != '全平台':
            filters.append(f"platform = '{platform}'")

        filter_clause = ""
        if filters:
            filter_clause = "AND " + " AND ".join(filters)

        logger.info(f"Activity detail weeks query - brand: '{brand}', filter_clause: '{filter_clause}'")

        # 构建所有周的查询条件
        week_conditions = []
        for week_info in weeks_data:
            week_conditions.append(
                f"(ds BETWEEN '{week_info['start_yyyymmdd']}' AND '{week_info['end_yyyymmdd']}')"
            )

        # 合并查询所有周的数据
        combined_sql = f"""
        SELECT
            ds,
            COALESCE(SUM(activity_gmv), 0) as activity_gmv,
            COALESCE(SUM(gmv), 0) as total_gmv,
            COALESCE(SUM(activity_expense), 0) as activity_expense
        FROM dws_o2o_sale_activity_detail_analysis_d
        WHERE ({' OR '.join(week_conditions)})
        {filter_clause}
        GROUP BY ds
        ORDER BY ds
        """

        results = execute_query(combined_sql)

        # 按周聚合数据并计算同比
        detail_data = []
        for week_info in weeks_data:
            week_activity_gmv = 0
            week_total_gmv = 0
            week_activity_expense = 0

            # 聚合该周的数据
            for row in results:
                ds_str = str(row['ds'])
                if week_info['start_yyyymmdd'] <= ds_str <= week_info['end_yyyymmdd']:
                    week_activity_gmv += float(row['activity_gmv'])
                    week_total_gmv += float(row['total_gmv'])
                    week_activity_expense += float(row['activity_expense'])

            # 计算去年同周数据用于同比
            last_year = week_info['year'] - 1
            last_year_week_start, last_year_week_end = get_week_date_range(last_year, week_info['week'])
            last_year_start_yyyymmdd = last_year_week_start.replace('-', '')
            last_year_end_yyyymmdd = last_year_week_end.replace('-', '')

            last_year_sql = f"""
            SELECT
                COALESCE(SUM(activity_gmv), 0) as activity_gmv,
                COALESCE(SUM(gmv), 0) as total_gmv,
                COALESCE(SUM(activity_expense), 0) as activity_expense
            FROM dws_o2o_sale_activity_detail_analysis_d
            WHERE ds BETWEEN '{last_year_start_yyyymmdd}' AND '{last_year_end_yyyymmdd}'
            {filter_clause}
            """

            last_year_result = execute_query(last_year_sql)
            last_year_activity_gmv = float(last_year_result[0]['activity_gmv']) if last_year_result else 0
            last_year_activity_expense = float(last_year_result[0]['activity_expense']) if last_year_result else 0

            # 计算各项比率和同比
            activity_cost_ratio = week_activity_expense / week_activity_gmv if week_activity_gmv > 0 else 0
            total_cost_ratio = week_activity_expense / week_total_gmv if week_total_gmv > 0 else 0

            # 计算同比
            activity_gmv_yoy = (week_activity_gmv - last_year_activity_gmv) / last_year_activity_gmv if last_year_activity_gmv > 0 else 0
            verification_yoy = (week_activity_expense - last_year_activity_expense) / last_year_activity_expense if last_year_activity_expense > 0 else 0

            # 计算费比同比
            last_year_activity_cost_ratio = last_year_activity_expense / last_year_activity_gmv if last_year_activity_gmv > 0 else 0
            activity_cost_ratio_yoy = (activity_cost_ratio - last_year_activity_cost_ratio) / last_year_activity_cost_ratio if last_year_activity_cost_ratio > 0 else 0

            last_year_total_gmv = float(last_year_result[0]['total_gmv']) if last_year_result else 0
            last_year_total_cost_ratio = last_year_activity_expense / last_year_total_gmv if last_year_total_gmv > 0 else 0
            total_cost_ratio_yoy = (total_cost_ratio - last_year_total_cost_ratio) / last_year_total_cost_ratio if last_year_total_cost_ratio > 0 else 0

            detail_data.append({
                'dimension': f'第{week_info["week"]}周',
                'activity_gmv': week_activity_gmv,
                'gmv_yoy': activity_gmv_yoy,
                'activity_ratio': week_activity_gmv / week_total_gmv if week_total_gmv > 0 else 0,
                'verification_amount': week_activity_expense,
                'verification_yoy': verification_yoy,
                'activity_cost_ratio': activity_cost_ratio,
                'activity_cost_ratio_yoy': activity_cost_ratio_yoy,
                'total_cost_ratio': total_cost_ratio,
                'total_cost_ratio_yoy': total_cost_ratio_yoy
            })

        # 按周次倒序排列（最新的周在最上面）
        detail_data.reverse()

        logger.info(f"Successfully retrieved activity detail for {len(detail_data)} weeks, brand '{brand}'")
        return detail_data

    except Exception as e:
        logger.error(f"Error getting activity detail real weeks: {e}")
        return []

def get_activity_detail_real_dimension(dimension_type, selected_week='', brand='', page=1, page_size=20, platform='全平台'):
    """
    获取活动详细数据 - 按维度，选定周的真实数据

    Args:
        dimension_type: 维度类型（城市、零售商、活动机制）
        selected_week: 选定的周，格式如'2024-W47'
        brand: 品牌名称
        page: 页码
        page_size: 每页大小
        platform: 平台名称

    Returns:
        dict: 包含数据、总数、分页信息的字典
    """
    try:
        # 解析选定的周
        if selected_week and selected_week.strip():
            # 解析格式如'2024-W47'
            year_str, week_str = selected_week.split('-W')
            target_year = int(year_str)
            target_week = int(week_str)
        else:
            # 如果没有指定周，使用最后一个完整周（当前周-1）
            current_year = datetime.now().year
            current_week = get_current_week_number()
            target_week = current_week - 1
            target_year = current_year

            # 处理跨年情况
            if target_week <= 0:
                target_week += 52
                target_year -= 1

        week_start, week_end = get_week_date_range(target_year, target_week)
        week_start_yyyymmdd = week_start.replace('-', '')
        week_end_yyyymmdd = week_end.replace('-', '')

        # 构建过滤条件
        filters = []
        if brand and brand.strip():
            filters.append(f"brand = '{brand.strip()}'")
        if platform != '全平台':
            filters.append(f"platform = '{platform}'")

        filter_clause = ""
        if filters:
            filter_clause = "AND " + " AND ".join(filters)

        logger.info(f"Activity detail dimension query - dimension_type: {dimension_type}, selected_week: '{selected_week}', target_year: {target_year}, target_week: {target_week}, week_range: {week_start} to {week_end}, brand: '{brand}', filter_clause: '{filter_clause}'")

        # 根据维度类型确定分组字段
        dimension_field_map = {
            '城市': 'standard_city',
            '商品': 'product_name',
            '零售商': 'vender_name',
            '活动机制': 'coupon_name'
        }

        dimension_field = dimension_field_map.get(dimension_type, 'standard_city')

        # 查询该周的总活动GMV（用于计算占比）
        total_sql = f"""
        SELECT COALESCE(SUM(activity_gmv), 0) as total_activity_gmv
        FROM dws_o2o_sale_activity_detail_analysis_d
        WHERE ds BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}'
        {filter_clause}
        """

        # 查询当周总核销金额用于计算核销占比
        total_verification_sql = f"""
        SELECT COALESCE(SUM(activity_expense), 0) as total_verification_amount
        FROM dws_o2o_sale_activity_detail_analysis_d
        WHERE ds BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}'
        {filter_clause}
        """

        total_result = execute_query(total_sql)
        total_activity_gmv = float(total_result[0]['total_activity_gmv']) if total_result else 0

        total_verification_result = execute_query(total_verification_sql)
        total_verification_amount = float(total_verification_result[0]['total_verification_amount']) if total_verification_result else 0

        # 查询分维度数据
        dimension_sql = f"""
        SELECT
            {dimension_field} as dimension_value,
            COALESCE(SUM(activity_gmv), 0) as activity_gmv,
            COALESCE(SUM(gmv), 0) as total_gmv,
            COALESCE(SUM(activity_expense), 0) as activity_expense
        FROM dws_o2o_sale_activity_detail_analysis_d
        WHERE ds BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}'
        {filter_clause}
        GROUP BY {dimension_field}
        HAVING COALESCE(SUM(activity_gmv), 0) > 0
        ORDER BY COALESCE(SUM(activity_gmv), 0) DESC
        """

        results = execute_query(dimension_sql)

        # 计算总数
        total_count = len(results)

        # 优化：预先计算去年同周数据，避免N+1查询问题
        last_year = target_year - 1
        last_year_week_start, last_year_week_end = get_week_date_range(last_year, target_week)
        last_year_start_yyyymmdd = last_year_week_start.replace('-', '')
        last_year_end_yyyymmdd = last_year_week_end.replace('-', '')

        # 批量查询所有维度值的去年数据
        dimension_values = [row['dimension_value'] for row in results]
        if dimension_values:
            dimension_values_str = "', '".join([str(v).replace("'", "''") for v in dimension_values])

            last_year_batch_sql = f"""
            SELECT
                {dimension_field} as dimension_value,
                COALESCE(SUM(activity_gmv), 0) as activity_gmv,
                COALESCE(SUM(gmv), 0) as total_gmv,
                COALESCE(SUM(activity_expense), 0) as activity_expense
            FROM dws_o2o_sale_activity_detail_analysis_d
            WHERE ds BETWEEN '{last_year_start_yyyymmdd}' AND '{last_year_end_yyyymmdd}'
            AND {dimension_field} IN ('{dimension_values_str}')
            {filter_clause}
            GROUP BY {dimension_field}
            """

            last_year_results = execute_query(last_year_batch_sql)

            # 构建去年数据的字典以便快速查找
            last_year_data = {}
            for row in last_year_results:
                last_year_data[row['dimension_value']] = {
                    'activity_gmv': float(row['activity_gmv']),
                    'total_gmv': float(row['total_gmv']),
                    'activity_expense': float(row['activity_expense'])
                }
        else:
            last_year_data = {}

        # 分页处理
        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        paged_results = results[start_index:end_index]

        # 处理数据并计算同比
        detail_data = []
        for row in paged_results:
            dimension_value = row['dimension_value']
            activity_gmv = float(row['activity_gmv'])
            total_gmv = float(row['total_gmv'])
            activity_expense = float(row['activity_expense'])

            # 从预查询的数据中获取去年同周数据
            last_year_info = last_year_data.get(dimension_value, {
                'activity_gmv': 0,
                'total_gmv': 0,
                'activity_expense': 0
            })

            last_year_activity_gmv = last_year_info['activity_gmv']
            last_year_total_gmv = last_year_info['total_gmv']
            last_year_activity_expense = last_year_info['activity_expense']

            # 计算各项指标
            gmv_ratio = activity_gmv / total_activity_gmv if total_activity_gmv > 0 else 0  # GMV占比 = 单一维度活动GMV/当周活动GMV
            activity_cost_ratio = activity_expense / activity_gmv if activity_gmv > 0 else 0

            # 计算同比
            gmv_yoy = (activity_gmv - last_year_activity_gmv) / last_year_activity_gmv if last_year_activity_gmv > 0 else 0
            verification_yoy = (activity_expense - last_year_activity_expense) / last_year_activity_expense if last_year_activity_expense > 0 else 0

            # 计算费比同比
            last_year_activity_cost_ratio = last_year_activity_expense / last_year_activity_gmv if last_year_activity_gmv > 0 else 0
            activity_cost_ratio_yoy = (activity_cost_ratio - last_year_activity_cost_ratio) / last_year_activity_cost_ratio if last_year_activity_cost_ratio > 0 else 0

            row_data = {
                'dimension': dimension_value,
                'activity_gmv': activity_gmv,
                'gmv_yoy': gmv_yoy,
                'verification_amount': activity_expense,
                'verification_yoy': verification_yoy,
                'activity_cost_ratio': activity_cost_ratio,
                'activity_cost_ratio_yoy': activity_cost_ratio_yoy
            }

            # 整体维度不显示GMV占比和核销占比
            if dimension_type != '整体':
                row_data['gmv_ratio'] = gmv_ratio
                # 核销占比 = 单一维度核销金额/当周总核销金额
                verification_ratio = activity_expense / total_verification_amount if total_verification_amount > 0 else 0
                row_data['verification_ratio'] = verification_ratio

            # 活动机制维度不展示活动占比和全量费比
            if dimension_type != '活动机制':
                activity_ratio = activity_gmv / total_gmv if total_gmv > 0 else 0
                total_cost_ratio = activity_expense / total_gmv if total_gmv > 0 else 0

                # 使用预查询的去年数据计算同比（已经包含了total_gmv）
                last_year_activity_ratio = last_year_activity_gmv / last_year_total_gmv if last_year_total_gmv > 0 else 0
                last_year_total_cost_ratio = last_year_activity_expense / last_year_total_gmv if last_year_total_gmv > 0 else 0

                activity_ratio_yoy = (activity_ratio - last_year_activity_ratio) / last_year_activity_ratio if last_year_activity_ratio > 0 else 0
                total_cost_ratio_yoy = (total_cost_ratio - last_year_total_cost_ratio) / last_year_total_cost_ratio if last_year_total_cost_ratio > 0 else 0

                row_data.update({
                    'activity_ratio': activity_ratio,
                    'activity_ratio_yoy': activity_ratio_yoy,
                    'total_cost_ratio': total_cost_ratio,
                    'total_cost_ratio_yoy': total_cost_ratio_yoy
                })

            detail_data.append(row_data)

        logger.info(f"Successfully retrieved activity detail for dimension '{dimension_type}', brand '{brand}', page {page}")

        return {
            'dimension_type': dimension_type,
            'data': detail_data,
            'total': total_count,
            'page': page,
            'page_size': page_size
        }

    except Exception as e:
        logger.error(f"Error getting activity detail real dimension: {e}")
        # 返回mock数据作为备用
        dimension_names = {
            '城市': ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉', '南京', '重庆', '西安',
                    '苏州', '天津', '郑州', '长沙', '青岛', '大连', '宁波', '厦门', '福州', '济南'],
            '商品': ['圣农黄金鸡翅中500g', '圣农黄金鸡翅根800g', '圣农冷鲜鸡胸肉380g', '圣农鸡腿肉600g', '圣农鸡胸肉片400g',
                    '圣农鸡翅尖300g', '圣农鸡爪500g', '圣农鸡心200g', '圣农鸡肝250g', '圣农鸡脖300g',
                    '圣农鸡架500g', '圣农鸡汤块1kg', '圣农鸡肉丸400g', '圣农鸡肉肠300g', '圣农鸡肉饼200g',
                    '圣农鸡肉串250g', '圣农鸡肉卷350g', '圣农鸡肉粒300g', '圣农鸡肉丝200g', '圣农鸡肉片400g'],
            '零售商': ['沃尔玛', '家乐福', '华润万家', '永辉超市', '大润发', '物美', '世纪联华', '卜蜂莲花',
                     '新华都', '人人乐', '百联', '联华', '易初莲花', '乐购', '美特好', '天虹', '新世界', '步步高', '中百', '农工商'],
            '活动机制': ['满减券', '折扣券', '免邮券', '新人券', '会员券', '生日券', '节日券', '限时券',
                     '品类券', '品牌券', '店铺券', '平台券', '红包券', '返现券', '积分券', '组合券', '阶梯券', '门槛券', '专享券', '通用券']
        }

        names = dimension_names.get(dimension_type, dimension_names['城市'])
        total_count = len(names)

        # 分页处理
        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        paged_names = names[start_index:end_index]

        data = []
        for i, name in enumerate(paged_names):
            # GMV递减排序
            base_gmv = 5000000 * (len(paged_names) - i) / len(paged_names)

            row_data = {
                'dimension': name,
                'activity_gmv': round(base_gmv + random.uniform(-200000, 200000), 2),
                'gmv_yoy': round(random.uniform(-0.2, 0.3), 3),
                'gmv_ratio': round(random.uniform(0.15, 0.35), 3),
                'verification_amount': round(random.uniform(800000, 1500000), 2),
                'verification_yoy': round(random.uniform(-0.2, 0.4), 3),
                'activity_cost_ratio': round(random.uniform(0.08, 0.15), 3),
                'activity_cost_ratio_yoy': round(random.uniform(-0.02, 0.03), 3)
            }

            # 活动机制不展示活动占比和全量费比
            if dimension_type != '活动机制':
                row_data.update({
                    'activity_ratio': round(random.uniform(0.6, 0.9), 3),
                    'activity_ratio_yoy': round(random.uniform(-0.1, 0.15), 3),
                    'total_cost_ratio': round(random.uniform(0.12, 0.20), 3),
                    'total_cost_ratio_yoy': round(random.uniform(-0.03, 0.04), 3)
                })

            data.append(row_data)

        return {
            'dimension_type': dimension_type,
            'data': data,
            'total': total_count,
            'page': page,
            'page_size': page_size
        }

def get_activity_platform_charts_real(brand='', platform='全平台'):
    """
    获取活动数据分平台图表数据 - 真实数据（最新完整周）

    Args:
        brand: 品牌名称
        platform: 平台名称

    Returns:
        dict: 分平台活动数据
    """
    try:
        current_year = datetime.now().year
        current_week = get_current_week_number()

        # 使用最后一个完整周（当前周-1）
        target_week = current_week - 1
        target_year = current_year

        # 处理跨年情况
        if target_week <= 0:
            target_week += 52
            target_year -= 1

        week_start, week_end = get_week_date_range(target_year, target_week)
        week_start_yyyymmdd = week_start.replace('-', '')
        week_end_yyyymmdd = week_end.replace('-', '')

        # 构建过滤条件
        filters = []
        if brand:
            filters.append(f"brand = '{brand}'")
        if platform != '全平台':
            filters.append(f"platform = '{platform}'")

        filter_clause = ""
        if filters:
            filter_clause = "AND " + " AND ".join(filters)

        # 查询分平台数据
        platform_sql = f"""
        SELECT
            platform,
            COALESCE(SUM(activity_gmv), 0) as activity_gmv,
            COALESCE(SUM(gmv), 0) as total_gmv,
            COALESCE(SUM(activity_expense), 0) as activity_expense
        FROM dws_o2o_sale_activity_detail_analysis_d
        WHERE ds BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}'
        AND platform IS NOT NULL
        AND platform != ''
        {filter_clause}
        GROUP BY platform
        ORDER BY SUM(activity_gmv) DESC
        """

        results = execute_query(platform_sql)

        # 处理查询结果
        platform_data = []
        for row in results:
            platform = row['platform']
            activity_gmv = float(row['activity_gmv'])
            total_gmv = float(row['total_gmv'])
            activity_expense = float(row['activity_expense'])

            # 过滤掉活动GMV为0的平台
            if activity_gmv <= 0:
                continue

            # 计算各项比率
            activity_gmv_ratio = activity_gmv / total_gmv if total_gmv > 0 else 0
            activity_cost_ratio = activity_expense / activity_gmv if activity_gmv > 0 else 0
            total_cost_ratio = activity_expense / total_gmv if total_gmv > 0 else 0

            platform_data.append({
                'platform': platform,
                'activity_gmv': activity_gmv,
                'activity_gmv_ratio': activity_gmv_ratio,
                'verification_amount': activity_expense,
                'activity_cost_ratio': activity_cost_ratio,
                'total_cost_ratio': total_cost_ratio
            })

        logger.info(f"Successfully retrieved activity platform charts for brand '{brand}', platform '{platform}', {len(platform_data)} platforms")
        return {'platform_data': platform_data}

    except Exception as e:
        logger.error(f"Error getting activity platform charts: {e}")
        # 返回默认数据
        return {'platform_data': []}


# RTB投放相关查询函数
def get_rtb_summary_real(week, brand=''):
    """获取RTB投放概览数据 - 真实数据"""
    try:
        current_year = datetime.now().year
        week = int(week)  # 确保week是整数
        week_start, week_end = get_week_date_range(current_year, week)

        # 转换日期格式为yyyymmdd
        week_start_yyyymmdd = week_start.replace('-', '')
        week_end_yyyymmdd = week_end.replace('-', '')

        # 构建品牌过滤条件
        brand_filter = ""
        if brand:
            brand_filter = f"AND brand = '{brand}'"

        # 获取上一周数据用于计算周环比
        prev_week = week - 1
        prev_year = current_year
        if prev_week <= 0:
            prev_year = current_year - 1
            last_day_of_prev_year = datetime(prev_year, 12, 31)
            prev_week = last_day_of_prev_year.isocalendar()[1]

        prev_week_start, prev_week_end = get_week_date_range(prev_year, prev_week)
        prev_week_start_yyyymmdd = prev_week_start.replace('-', '')
        prev_week_end_yyyymmdd = prev_week_end.replace('-', '')

        # 查询计划数据表的当周数据
        current_week_sql = f"""
        SELECT
            COALESCE(SUM(consume), 0) as consumption,
            COALESCE(SUM(mapping_budget), 0) as budget,
            COALESCE(SUM(direct_transaction_amount), 0) as guided_amount,
            COALESCE(SUM(exposure), 0) as exposure,
            COALESCE(SUM(click), 0) as click,
            COALESCE(SUM(direct_order_volume), 0) as order_volume,
            COALESCE(SUM(new_user_num), 0) as new_user_num
        FROM dwd_rtb_advertising_plan_all_type_mapping_data_d
        WHERE ds BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}'
        {brand_filter}
        """

        # 查询上周数据用于计算周环比
        prev_week_sql = f"""
        SELECT
            COALESCE(SUM(consume), 0) as consumption,
            COALESCE(SUM(direct_transaction_amount), 0) as guided_amount,
            COALESCE(SUM(exposure), 0) as exposure,
            COALESCE(SUM(click), 0) as click,
            COALESCE(SUM(direct_order_volume), 0) as order_volume,
            COALESCE(SUM(new_user_num), 0) as new_user_num
        FROM dwd_rtb_advertising_plan_all_type_mapping_data_d
        WHERE ds BETWEEN '{prev_week_start_yyyymmdd}' AND '{prev_week_end_yyyymmdd}'
        {brand_filter}
        """

        current_result = execute_query(current_week_sql)
        prev_result = execute_query(prev_week_sql)

        if not current_result:
            logger.warning("No current week RTB data found")
            return {}

        current_data = current_result[0]
        prev_data = prev_result[0] if prev_result else {}

        # 提取当周数据
        consumption = float(current_data['consumption'])
        budget = float(current_data['budget'])
        guided_amount = float(current_data['guided_amount'])
        exposure = float(current_data['exposure'])
        click = float(current_data['click'])
        order_volume = float(current_data['order_volume'])
        new_user_num = float(current_data['new_user_num'])

        # 提取上周数据
        prev_consumption = float(prev_data.get('consumption', 0)) if prev_data else 0
        prev_guided_amount = float(prev_data.get('guided_amount', 0)) if prev_data else 0
        prev_exposure = float(prev_data.get('exposure', 0)) if prev_data else 0
        prev_click = float(prev_data.get('click', 0)) if prev_data else 0
        prev_order_volume = float(prev_data.get('order_volume', 0)) if prev_data else 0
        prev_new_user_num = float(prev_data.get('new_user_num', 0)) if prev_data else 0

        # 计算各项指标
        consumption_progress = consumption / budget if budget > 0 else 0
        roi = guided_amount / consumption if consumption > 0 else 0
        ctr = click / exposure if exposure > 0 else 0  # 点击率
        order_conversion_rate = order_volume / click if click > 0 else 0  # 订单转化率
        cpm = consumption / (exposure / 1000) if exposure > 0 else 0  # 千次曝光成本
        cpc = consumption / click if click > 0 else 0  # 点击成本
        new_user_cost = consumption / new_user_num if new_user_num > 0 else 0  # 新客成本

        # 计算周环比
        consumption_wow = (consumption - prev_consumption) / prev_consumption if prev_consumption > 0 else 0
        guided_amount_wow = (guided_amount - prev_guided_amount) / prev_guided_amount if prev_guided_amount > 0 else 0
        roi_wow = 0  # ROI周环比需要特殊计算
        if prev_consumption > 0 and consumption > 0:
            prev_roi = prev_guided_amount / prev_consumption
            roi_wow = (roi - prev_roi) / prev_roi if prev_roi > 0 else 0

        exposure_wow = (exposure - prev_exposure) / prev_exposure if prev_exposure > 0 else 0
        click_wow = (click - prev_click) / prev_click if prev_click > 0 else 0

        # 计算点击率周环比
        ctr_wow = 0
        if prev_exposure > 0 and exposure > 0:
            prev_ctr = prev_click / prev_exposure
            ctr_wow = (ctr - prev_ctr) / prev_ctr if prev_ctr > 0 else 0

        # 计算订单转化率周环比
        order_conversion_rate_wow = 0
        if prev_click > 0 and click > 0:
            prev_order_conversion_rate = prev_order_volume / prev_click
            order_conversion_rate_wow = (order_conversion_rate - prev_order_conversion_rate) / prev_order_conversion_rate if prev_order_conversion_rate > 0 else 0

        # 计算新客成本周环比
        new_user_cost_wow = 0
        if prev_new_user_num > 0 and new_user_num > 0:
            prev_new_user_cost = prev_consumption / prev_new_user_num
            new_user_cost_wow = (new_user_cost - prev_new_user_cost) / prev_new_user_cost if prev_new_user_cost > 0 else 0

        return {
            'consumption': {
                'amount': consumption,
                'wow': consumption_wow,
                'progress': consumption_progress
            },
            't1_guided_amount': {
                'amount': guided_amount,
                'wow': guided_amount_wow
            },
            'roi': {
                'value': roi,
                'wow': roi_wow
            },
            'exposure': {
                'count': exposure,
                'wow': exposure_wow
            },
            'click': {
                'count': click,
                'wow': click_wow
            },
            'ctr': {
                'rate': ctr,
                'wow': ctr_wow
            },
            'order_volume': {
                'count': order_volume,
                'wow': (order_volume - prev_order_volume) / prev_order_volume if prev_order_volume > 0 else 0
            },
            'order_conversion_rate': {
                'rate': order_conversion_rate,
                'wow': order_conversion_rate_wow
            },
            'budget': {
                'amount': budget
            },
            'cpm': {
                'cost': cpm,
                'wow': 0  # 需要计算上周CPM来得出周环比
            },
            'cpc': {
                'cost': cpc,
                'wow': 0  # 需要计算上周CPC来得出周环比
            },
            'new_user_cost': {
                'cost': new_user_cost,
                'wow': new_user_cost_wow
            }
        }

    except Exception as e:
        logger.error(f"Error getting RTB summary data: {e}")
        return {}


def get_rtb_trends_real(brand=''):
    """获取RTB趋势数据 - 真实数据（最近8周）"""
    try:
        current_date = datetime.now()
        current_year = current_date.year
        current_week = current_date.isocalendar()[1]

        # 获取最近8个完整周（不包括当前周）
        trend_data = []

        # 构建品牌过滤条件
        brand_filter = ""
        if brand:
            brand_filter = f"AND brand = '{brand}'"

        for i in range(8):
            week_num = current_week - 1 - i  # 从上周开始往前推
            year = current_year

            if week_num <= 0:
                year = current_year - 1
                last_day_of_prev_year = datetime(year, 12, 31)
                week_num = last_day_of_prev_year.isocalendar()[1] + week_num

            week_start, week_end = get_week_date_range(year, week_num)

            # 转换日期格式为yyyymmdd
            week_start_yyyymmdd = week_start.replace('-', '')
            week_end_yyyymmdd = week_end.replace('-', '')

            # 查询该周数据
            week_sql = f"""
            SELECT
                COALESCE(SUM(consume), 0) as consumption,
                COALESCE(SUM(direct_transaction_amount), 0) as guided_amount,
                COALESCE(SUM(exposure), 0) as exposure,
                COALESCE(SUM(click), 0) as click,
                COALESCE(SUM(direct_order_volume), 0) as order_volume
            FROM dwd_rtb_advertising_plan_all_type_mapping_data_d
            WHERE ds BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}'
            {brand_filter}
            """

            result = execute_query(week_sql)

            if result:
                data = result[0]
                consumption = float(data['consumption'])
                guided_amount = float(data['guided_amount'])
                exposure = float(data['exposure'])
                click = float(data['click'])
                order_volume = float(data['order_volume'])

                # 计算ROI
                roi = guided_amount / consumption if consumption > 0 else 0

                trend_data.append({
                    'week': week_num,
                    'year': year,
                    'consumption': consumption,
                    't1_guided_amount': guided_amount,
                    'roi': roi,
                    'exposure': exposure,
                    'click': click,
                    'order_volume': order_volume
                })

        # 反转数据，使其按时间顺序排列
        trend_data.reverse()

        # 计算周环比
        for i in range(1, len(trend_data)):
            current = trend_data[i]
            previous = trend_data[i-1]

            # 消耗周环比
            current['consumption_wow'] = (current['consumption'] - previous['consumption']) / previous['consumption'] if previous['consumption'] > 0 else 0

            # 引导GMV周环比
            current['t1_guided_amount_wow'] = (current['t1_guided_amount'] - previous['t1_guided_amount']) / previous['t1_guided_amount'] if previous['t1_guided_amount'] > 0 else 0

            # ROI周环比
            current['roi_wow'] = (current['roi'] - previous['roi']) / previous['roi'] if previous['roi'] > 0 else 0

        # 第一周没有周环比数据
        if trend_data:
            trend_data[0]['consumption_wow'] = 0
            trend_data[0]['t1_guided_amount_wow'] = 0
            trend_data[0]['roi_wow'] = 0

        return {'trend_data': trend_data}

    except Exception as e:
        logger.error(f"Error getting RTB trends data: {e}")
        return {'trend_data': []}


def get_rtb_detail_weeks_real(selected_week, brand=''):
    """获取RTB整体维度近8周明细数据 - 真实数据"""
    try:
        current_year = datetime.now().year
        selected_week = int(selected_week)

        # 构建品牌过滤条件
        brand_filter = ""
        if brand:
            brand_filter = f"AND brand = '{brand}'"

        # 获取近8个完整周的数据（包括选定周）
        detail_data = []

        for i in range(8):
            week_num = selected_week - i
            year = current_year

            if week_num <= 0:
                year = current_year - 1
                last_day_of_prev_year = datetime(year, 12, 31)
                week_num = last_day_of_prev_year.isocalendar()[1] + week_num

            week_start, week_end = get_week_date_range(year, week_num)
            week_start_yyyymmdd = week_start.replace('-', '')
            week_end_yyyymmdd = week_end.replace('-', '')

            # 获取上一周数据用于计算周环比
            prev_week = week_num - 1
            prev_year = year
            if prev_week <= 0:
                prev_year = year - 1
                last_day_of_prev_year = datetime(prev_year, 12, 31)
                prev_week = last_day_of_prev_year.isocalendar()[1]

            prev_week_start, prev_week_end = get_week_date_range(prev_year, prev_week)
            prev_week_start_yyyymmdd = prev_week_start.replace('-', '')
            prev_week_end_yyyymmdd = prev_week_end.replace('-', '')

            # 获取去年同期数据用于计算年同比
            last_year = year - 1
            last_year_week_start, last_year_week_end = get_week_date_range(last_year, week_num)
            last_year_week_start_yyyymmdd = last_year_week_start.replace('-', '')
            last_year_week_end_yyyymmdd = last_year_week_end.replace('-', '')

            # 查询当周数据
            current_week_sql = f"""
            SELECT
                COALESCE(SUM(consume), 0) as consumption,
                COALESCE(SUM(mapping_budget), 0) as budget,
                COALESCE(SUM(direct_transaction_amount), 0) as guided_amount,
                COALESCE(SUM(exposure), 0) as exposure,
                COALESCE(SUM(click), 0) as click,
                COALESCE(SUM(direct_order_volume), 0) as order_volume
            FROM dwd_rtb_advertising_plan_all_type_mapping_data_d
            WHERE ds BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}'
            {brand_filter}
            """

            # 查询上周数据
            prev_week_sql = f"""
            SELECT
                COALESCE(SUM(consume), 0) as consumption,
                COALESCE(SUM(direct_transaction_amount), 0) as guided_amount,
                COALESCE(SUM(exposure), 0) as exposure,
                COALESCE(SUM(click), 0) as click,
                COALESCE(SUM(direct_order_volume), 0) as order_volume
            FROM dwd_rtb_advertising_plan_all_type_mapping_data_d
            WHERE ds BETWEEN '{prev_week_start_yyyymmdd}' AND '{prev_week_end_yyyymmdd}'
            {brand_filter}
            """

            # 查询去年同期数据
            last_year_sql = f"""
            SELECT
                COALESCE(SUM(consume), 0) as consumption,
                COALESCE(SUM(direct_transaction_amount), 0) as guided_amount,
                COALESCE(SUM(exposure), 0) as exposure,
                COALESCE(SUM(click), 0) as click,
                COALESCE(SUM(direct_order_volume), 0) as order_volume
            FROM dwd_rtb_advertising_plan_all_type_mapping_data_d
            WHERE ds BETWEEN '{last_year_week_start_yyyymmdd}' AND '{last_year_week_end_yyyymmdd}'
            {brand_filter}
            """

            current_result = execute_query(current_week_sql)
            prev_result = execute_query(prev_week_sql)
            last_year_result = execute_query(last_year_sql)

            if current_result:
                current_data = current_result[0]
                prev_data = prev_result[0] if prev_result else {}
                last_year_data = last_year_result[0] if last_year_result else {}

                # 当周数据
                consumption = float(current_data['consumption'])
                budget = float(current_data['budget'])
                guided_amount = float(current_data['guided_amount'])
                exposure = float(current_data['exposure'])
                click = float(current_data['click'])
                order_volume = float(current_data['order_volume'])

                # 上周数据
                prev_consumption = float(prev_data.get('consumption', 0))
                prev_guided_amount = float(prev_data.get('guided_amount', 0))
                prev_exposure = float(prev_data.get('exposure', 0))
                prev_click = float(prev_data.get('click', 0))
                prev_order_volume = float(prev_data.get('order_volume', 0))

                # 去年同期数据
                last_year_consumption = float(last_year_data.get('consumption', 0))
                last_year_guided_amount = float(last_year_data.get('guided_amount', 0))
                last_year_exposure = float(last_year_data.get('exposure', 0))
                last_year_click = float(last_year_data.get('click', 0))
                last_year_order_volume = float(last_year_data.get('order_volume', 0))

                # 计算各项指标
                consumption_progress = consumption / budget if budget > 0 else 0
                roi = guided_amount / consumption if consumption > 0 else 0
                ctr = click / exposure if exposure > 0 else 0
                order_conversion_rate = order_volume / click if click > 0 else 0
                cpm = consumption / (exposure / 1000) if exposure > 0 else 0
                cpc = consumption / click if click > 0 else 0

                # 计算周环比
                consumption_wow = (consumption - prev_consumption) / prev_consumption if prev_consumption > 0 else 0
                guided_amount_wow = (guided_amount - prev_guided_amount) / prev_guided_amount if prev_guided_amount > 0 else 0

                # ROI周环比
                roi_wow = 0
                if prev_consumption > 0 and consumption > 0:
                    prev_roi = prev_guided_amount / prev_consumption
                    roi_wow = (roi - prev_roi) / prev_roi if prev_roi > 0 else 0

                exposure_wow = (exposure - prev_exposure) / prev_exposure if prev_exposure > 0 else 0
                click_wow = (click - prev_click) / prev_click if prev_click > 0 else 0
                order_volume_wow = (order_volume - prev_order_volume) / prev_order_volume if prev_order_volume > 0 else 0

                # 计算年同比
                consumption_yoy = (consumption - last_year_consumption) / last_year_consumption if last_year_consumption > 0 else 0
                guided_amount_yoy = (guided_amount - last_year_guided_amount) / last_year_guided_amount if last_year_guided_amount > 0 else 0

                # ROI年同比
                roi_yoy = 0
                if last_year_consumption > 0 and consumption > 0:
                    last_year_roi = last_year_guided_amount / last_year_consumption
                    roi_yoy = (roi - last_year_roi) / last_year_roi if last_year_roi > 0 else 0

                exposure_yoy = (exposure - last_year_exposure) / last_year_exposure if last_year_exposure > 0 else 0
                click_yoy = (click - last_year_click) / last_year_click if last_year_click > 0 else 0
                order_volume_yoy = (order_volume - last_year_order_volume) / last_year_order_volume if last_year_order_volume > 0 else 0

                detail_data.append({
                    'dimension': f'第{week_num}周',
                    'consumption': consumption,
                    'consumption_wow': consumption_wow,
                    'consumption_yoy': consumption_yoy,
                    'consumption_progress': consumption_progress,
                    'guided_amount': guided_amount,
                    'guided_amount_wow': guided_amount_wow,
                    'guided_amount_yoy': guided_amount_yoy,
                    'roi': roi,
                    'roi_wow': roi_wow,
                    'roi_yoy': roi_yoy,
                    'exposure': exposure,
                    'exposure_wow': exposure_wow,
                    'exposure_yoy': exposure_yoy,
                    'click': click,
                    'click_wow': click_wow,
                    'click_yoy': click_yoy,
                    'ctr': ctr,
                    'order_volume': order_volume,
                    'order_volume_wow': order_volume_wow,
                    'order_volume_yoy': order_volume_yoy,
                    'order_conversion_rate': order_conversion_rate,
                    'budget': budget,
                    'cpm': cpm,
                    'cpc': cpc
                })

        # 保持数据按倒序排列（最新的周在前）
        # detail_data.reverse()  # 注释掉反转，保持倒序

        return {
            'dimension_type': '整体',
            'data': detail_data
        }

    except Exception as e:
        logger.error(f"Error getting RTB detail weeks data: {e}")
        return {'data': []}


def get_rtb_detail_real(dimension_type, selected_week, brand=''):
    """获取RTB明细数据 - 真实数据"""
    try:
        current_year = datetime.now().year
        selected_week = int(selected_week)  # 确保selected_week是整数
        week_start, week_end = get_week_date_range(current_year, selected_week)

        # 转换日期格式为yyyymmdd
        week_start_yyyymmdd = week_start.replace('-', '')
        week_end_yyyymmdd = week_end.replace('-', '')

        # 构建品牌过滤条件
        brand_filter = ""
        if brand:
            brand_filter = f"AND brand = '{brand}'"

        # 获取上一周数据用于计算周环比
        prev_week = selected_week - 1
        prev_year = current_year
        if prev_week <= 0:
            prev_year = current_year - 1
            last_day_of_prev_year = datetime(prev_year, 12, 31)
            prev_week = last_day_of_prev_year.isocalendar()[1]

        prev_week_start, prev_week_end = get_week_date_range(prev_year, prev_week)
        prev_week_start_yyyymmdd = prev_week_start.replace('-', '')
        prev_week_end_yyyymmdd = prev_week_end.replace('-', '')

        # 获取去年同期数据用于计算年同比
        last_year = current_year - 1
        last_year_week_start, last_year_week_end = get_week_date_range(last_year, selected_week)
        last_year_week_start_yyyymmdd = last_year_week_start.replace('-', '')
        last_year_week_end_yyyymmdd = last_year_week_end.replace('-', '')

        # 根据维度类型选择数据表和分组字段
        if dimension_type == '整体':
            # 整体维度：显示近8个完整周的数据
            return get_rtb_detail_weeks_real(selected_week, brand)
        elif dimension_type == '计划':
            # 计划维度：使用计划数据表，按计划分组
            table_name = 'dwd_rtb_advertising_plan_all_type_mapping_data_d'
            group_field = "COALESCE(plan_name, '未知计划') as dimension"
            group_by_clause = "GROUP BY plan_name"
        elif dimension_type == '商品':
            # 商品维度：使用商品数据表，按商品分组
            table_name = 'dwd_rtb_product_adv_plan_d'
            group_field = "COALESCE(product_name, '未知商品') as dimension"
            group_by_clause = "GROUP BY product_name"
        else:
            logger.warning(f"Unknown dimension type: {dimension_type}")
            return {'data': []}

        # 查询当周数据
        current_week_sql = f"""
        SELECT
            {group_field},
            COALESCE(SUM(consume), 0) as consumption,
            COALESCE(SUM(mapping_budget), 0) as budget,
            COALESCE(SUM(direct_transaction_amount), 0) as guided_amount,
            COALESCE(SUM(exposure), 0) as exposure,
            COALESCE(SUM(click), 0) as click,
            COALESCE(SUM(direct_order_volume), 0) as order_volume,
            COALESCE(SUM(new_user_num), 0) as new_user_num
        FROM {table_name}
        WHERE ds BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}'
        {brand_filter}
        {group_by_clause}
        ORDER BY SUM(consume) DESC
        """

        # 查询上周数据
        prev_week_sql = f"""
        SELECT
            {group_field},
            COALESCE(SUM(consume), 0) as consumption,
            COALESCE(SUM(direct_transaction_amount), 0) as guided_amount,
            COALESCE(SUM(exposure), 0) as exposure,
            COALESCE(SUM(click), 0) as click,
            COALESCE(SUM(direct_order_volume), 0) as order_volume,
            COALESCE(SUM(new_user_num), 0) as new_user_num
        FROM {table_name}
        WHERE ds BETWEEN '{prev_week_start_yyyymmdd}' AND '{prev_week_end_yyyymmdd}'
        {brand_filter}
        {group_by_clause}
        """

        # 查询去年同期数据
        last_year_sql = f"""
        SELECT
            {group_field},
            COALESCE(SUM(consume), 0) as consumption,
            COALESCE(SUM(direct_transaction_amount), 0) as guided_amount,
            COALESCE(SUM(exposure), 0) as exposure,
            COALESCE(SUM(click), 0) as click,
            COALESCE(SUM(direct_order_volume), 0) as order_volume,
            COALESCE(SUM(new_user_num), 0) as new_user_num
        FROM {table_name}
        WHERE ds BETWEEN '{last_year_week_start_yyyymmdd}' AND '{last_year_week_end_yyyymmdd}'
        {brand_filter}
        {group_by_clause}
        """

        current_results = execute_query(current_week_sql)
        prev_results = execute_query(prev_week_sql)
        last_year_results = execute_query(last_year_sql)

        # 将结果转换为字典以便查找
        prev_data_dict = {row['dimension']: row for row in prev_results}
        last_year_data_dict = {row['dimension']: row for row in last_year_results}

        # 处理数据
        detail_data = []
        for row in current_results:
            dimension = row['dimension']

            # 当周数据
            consumption = float(row['consumption'])
            budget = float(row['budget'])
            guided_amount = float(row['guided_amount'])
            exposure = float(row['exposure'])
            click = float(row['click'])
            order_volume = float(row['order_volume'])
            new_user_num = float(row['new_user_num'])

            # 上周数据
            prev_row = prev_data_dict.get(dimension, {})
            prev_consumption = float(prev_row.get('consumption', 0))
            prev_guided_amount = float(prev_row.get('guided_amount', 0))
            prev_exposure = float(prev_row.get('exposure', 0))
            prev_click = float(prev_row.get('click', 0))
            prev_order_volume = float(prev_row.get('order_volume', 0))
            prev_new_user_num = float(prev_row.get('new_user_num', 0))

            # 去年同期数据
            last_year_row = last_year_data_dict.get(dimension, {})
            last_year_consumption = float(last_year_row.get('consumption', 0))
            last_year_guided_amount = float(last_year_row.get('guided_amount', 0))
            last_year_exposure = float(last_year_row.get('exposure', 0))
            last_year_click = float(last_year_row.get('click', 0))
            last_year_order_volume = float(last_year_row.get('order_volume', 0))
            last_year_new_user_num = float(last_year_row.get('new_user_num', 0))

            # 计算各项指标
            consumption_progress = consumption / budget if budget > 0 else 0
            roi = guided_amount / consumption if consumption > 0 else 0
            ctr = click / exposure if exposure > 0 else 0
            order_conversion_rate = order_volume / click if click > 0 else 0
            cpm = consumption / (exposure / 1000) if exposure > 0 else 0
            cpc = consumption / click if click > 0 else 0

            # 计算周环比
            consumption_wow = (consumption - prev_consumption) / prev_consumption if prev_consumption > 0 else 0
            guided_amount_wow = (guided_amount - prev_guided_amount) / prev_guided_amount if prev_guided_amount > 0 else 0

            # ROI周环比
            roi_wow = 0
            if prev_consumption > 0 and consumption > 0:
                prev_roi = prev_guided_amount / prev_consumption
                roi_wow = (roi - prev_roi) / prev_roi if prev_roi > 0 else 0

            exposure_wow = (exposure - prev_exposure) / prev_exposure if prev_exposure > 0 else 0
            click_wow = (click - prev_click) / prev_click if prev_click > 0 else 0
            order_volume_wow = (order_volume - prev_order_volume) / prev_order_volume if prev_order_volume > 0 else 0

            # 计算年同比
            consumption_yoy = (consumption - last_year_consumption) / last_year_consumption if last_year_consumption > 0 else 0
            guided_amount_yoy = (guided_amount - last_year_guided_amount) / last_year_guided_amount if last_year_guided_amount > 0 else 0

            # ROI年同比
            roi_yoy = 0
            if last_year_consumption > 0 and consumption > 0:
                last_year_roi = last_year_guided_amount / last_year_consumption
                roi_yoy = (roi - last_year_roi) / last_year_roi if last_year_roi > 0 else 0

            exposure_yoy = (exposure - last_year_exposure) / last_year_exposure if last_year_exposure > 0 else 0
            click_yoy = (click - last_year_click) / last_year_click if last_year_click > 0 else 0
            order_volume_yoy = (order_volume - last_year_order_volume) / last_year_order_volume if last_year_order_volume > 0 else 0

            row_data = {
                'dimension': dimension,
                'consumption': consumption,
                'consumption_wow': consumption_wow,
                'consumption_yoy': consumption_yoy,
                'consumption_progress': consumption_progress,
                'guided_amount': guided_amount,
                'guided_amount_wow': guided_amount_wow,
                'guided_amount_yoy': guided_amount_yoy,
                'roi': roi,
                'roi_wow': roi_wow,
                'roi_yoy': roi_yoy,
                'exposure': exposure,
                'exposure_wow': exposure_wow,
                'exposure_yoy': exposure_yoy,
                'click': click,
                'click_wow': click_wow,
                'click_yoy': click_yoy,
                'ctr': ctr,
                'order_volume': order_volume,
                'order_volume_wow': order_volume_wow,
                'order_volume_yoy': order_volume_yoy,
                'order_conversion_rate': order_conversion_rate,
                'budget': budget,
                'cpm': cpm,
                'cpc': cpc
            }

            # 只有计划维度才显示新客成本
            if dimension_type == '计划':
                new_user_cost = consumption / new_user_num if new_user_num > 0 else 0
                prev_new_user_cost = prev_consumption / prev_new_user_num if prev_new_user_num > 0 else 0
                last_year_new_user_cost = last_year_consumption / last_year_new_user_num if last_year_new_user_num > 0 else 0

                new_user_cost_wow = (new_user_cost - prev_new_user_cost) / prev_new_user_cost if prev_new_user_cost > 0 else 0
                new_user_cost_yoy = (new_user_cost - last_year_new_user_cost) / last_year_new_user_cost if last_year_new_user_cost > 0 else 0

                row_data.update({
                    'new_user_cost': new_user_cost,
                    'new_user_cost_wow': new_user_cost_wow,
                    'new_user_cost_yoy': new_user_cost_yoy
                })

            detail_data.append(row_data)

        return {
            'dimension_type': dimension_type,
            'data': detail_data
        }

    except Exception as e:
        logger.error(f"Error getting RTB detail data: {e}")
        return {'data': []}


# ==================== 供给表现相关函数 ====================

def get_supply_summary_real(week=None, brand=''):
    """
    获取供给表现概览数据 - 真实数据版本
    只取美团平台数据，供给指标取周六数据，GMV取整周数据

    Args:
        week: 周数，如果为None则使用上一个完整周
        brand: 品牌筛选

    Returns:
        dict: 供给概览数据
    """
    try:
        current_year = datetime.now().year
        current_week = get_current_week_number()

        # 如果没有指定周，使用上一个完整周
        if week is None:
            target_week = current_week - 1
            target_year = current_year
            if target_week <= 0:
                target_week += 52
                target_year -= 1
        else:
            target_week = int(week)
            target_year = current_year

        # 获取目标周的日期范围
        week_start, week_end = get_week_date_range(target_year, target_week)

        # 获取周六日期（供给指标用）
        week_start_date = datetime.strptime(week_start, '%Y-%m-%d')
        saturday_date = week_start_date + timedelta(days=5)  # 周六
        saturday_yyyymmdd = saturday_date.strftime('%Y%m%d')

        # 获取整周日期范围（GMV用）
        week_start_yyyymmdd = week_start.replace('-', '')
        week_end_yyyymmdd = week_end.replace('-', '')

        # 构建品牌过滤条件 - 注意不同表使用不同的品牌字段
        # GMV数据表使用 brand 字段
        gmv_brand_filter = ""
        if brand and brand.strip():
            gmv_brand_filter = f"AND brand = '{brand.strip()}'"

        # 供给数据表使用 collect_brand 字段
        supply_brand_filter = ""
        if brand and brand.strip():
            supply_brand_filter = f"AND collect_brand = '{brand.strip()}'"

        # 1. 获取GMV数据（整周，全量数据表）
        gmv_sql = f"""
        SELECT COALESCE(SUM(gmv), 0) as current_gmv
        FROM dws_o2o_sale_activity_detail_analysis_d
        WHERE ds BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}'
        AND source = '全量'
        AND platform = '美团'
        {gmv_brand_filter}
        """

        gmv_result = execute_query(gmv_sql)
        current_gmv = float(gmv_result[0]['current_gmv']) if gmv_result else 0

        # 2. 获取供给指标数据（周六，使用与趋势图相同的数据源brand_city表，只取美团平台）
        supply_sql = f"""
        SELECT
            COALESCE(SUM(onsale_poi_num), 0) as store_count,
            COALESCE(AVG(poi_permeate_rate), 0) as store_penetration,
            COALESCE(AVG(poi_sold_rate), 0) as store_activity_rate,
            COALESCE(AVG(store_avg_on_sales_sku), 0) as avg_sku_per_store,
            COALESCE(AVG(sku_sold_out_rate), 0) as sku_sold_out_rate
        FROM dws_mt_brand_city_details_daily_data_attribute_d
        WHERE ds = '{saturday_yyyymmdd}'
        AND platform = '美团'
        {supply_brand_filter}
        """

        supply_result = execute_query(supply_sql)

        if supply_result:
            supply_data = supply_result[0]
            store_count = int(supply_data['store_count'])
            store_penetration = float(supply_data['store_penetration'])
            store_activity_rate = float(supply_data['store_activity_rate'])
            avg_sku_per_store = int(supply_data['avg_sku_per_store'])
            sku_sold_out_rate = float(supply_data['sku_sold_out_rate'])
        else:
            # 如果没有数据，返回默认值
            store_count = 0
            store_penetration = 0.0
            store_activity_rate = 0.0
            avg_sku_per_store = 0
            sku_sold_out_rate = 0.0

        # 3. 计算周环比（与上周六对比）
        prev_week = target_week - 1
        prev_year = target_year
        if prev_week <= 0:
            prev_week += 52
            prev_year -= 1

        prev_week_start, _ = get_week_date_range(prev_year, prev_week)
        prev_week_start_date = datetime.strptime(prev_week_start, '%Y-%m-%d')
        prev_saturday_date = prev_week_start_date + timedelta(days=5)
        prev_saturday_yyyymmdd = prev_saturday_date.strftime('%Y%m%d')

        # 上周GMV（整周）
        prev_week_end = (prev_week_start_date + timedelta(days=6)).strftime('%Y%m%d')
        prev_gmv_sql = f"""
        SELECT COALESCE(SUM(gmv), 0) as prev_gmv
        FROM dws_o2o_sale_activity_detail_analysis_d
        WHERE ds BETWEEN '{prev_week_start.replace("-", "")}' AND '{prev_week_end}'
        AND source = '全量'
        AND platform = '美团'
        {gmv_brand_filter}
        """

        prev_gmv_result = execute_query(prev_gmv_sql)
        prev_gmv = float(prev_gmv_result[0]['prev_gmv']) if prev_gmv_result else 0

        # 上周供给指标（周六，使用与趋势图相同的数据源brand_city表，只取美团平台）
        prev_saturday_yyyymmdd = prev_saturday_date.strftime('%Y%m%d')
        prev_supply_sql = f"""
        SELECT
            COALESCE(SUM(onsale_poi_num), 0) as prev_store_count,
            COALESCE(AVG(poi_permeate_rate), 0) as prev_store_penetration,
            COALESCE(AVG(poi_sold_rate), 0) as prev_store_activity_rate,
            COALESCE(AVG(store_avg_on_sales_sku), 0) as prev_avg_sku_per_store,
            COALESCE(AVG(sku_sold_out_rate), 0) as prev_sku_sold_out_rate
        FROM dws_mt_brand_city_details_daily_data_attribute_d
        WHERE ds = '{prev_saturday_yyyymmdd}'
        AND platform = '美团'
        {supply_brand_filter}
        """

        prev_supply_result = execute_query(prev_supply_sql)

        if prev_supply_result:
            prev_supply_data = prev_supply_result[0]
            prev_store_count = int(prev_supply_data['prev_store_count'])
            prev_store_penetration = float(prev_supply_data['prev_store_penetration'])
            prev_store_activity_rate = float(prev_supply_data['prev_store_activity_rate'])
            prev_avg_sku_per_store = int(prev_supply_data['prev_avg_sku_per_store'])
            prev_sku_sold_out_rate = float(prev_supply_data['prev_sku_sold_out_rate'])
        else:
            prev_store_count = 0
            prev_store_penetration = 0.0
            prev_store_activity_rate = 0.0
            prev_avg_sku_per_store = 0
            prev_sku_sold_out_rate = 0.0

        # 计算周环比
        def calculate_wow(current, previous):
            if previous == 0:
                return 0.0
            return round((current - previous) / previous, 4)

        def calculate_pp_wow(current, previous):
            """计算pp格式的周环比（百分点差异）"""
            return round(current - previous, 4)

        gmv_wow = calculate_wow(current_gmv, prev_gmv)
        store_count_wow = calculate_wow(store_count, prev_store_count)
        store_penetration_wow = calculate_pp_wow(store_penetration, prev_store_penetration)
        store_activity_rate_wow = calculate_pp_wow(store_activity_rate, prev_store_activity_rate)
        avg_sku_per_store_wow = calculate_wow(avg_sku_per_store, prev_avg_sku_per_store)
        sku_sold_out_rate_wow = calculate_pp_wow(sku_sold_out_rate, prev_sku_sold_out_rate)

        return {
            'gmv': {
                'value': current_gmv,
                'wow': gmv_wow
            },
            'store_count': {
                'value': store_count,
                'wow': store_count_wow
            },
            'store_penetration': {
                'value': store_penetration,
                'wow': store_penetration_wow  # pp格式
            },
            'store_activity_rate': {
                'value': store_activity_rate,
                'wow': store_activity_rate_wow  # pp格式
            },
            'avg_sku_per_store': {
                'value': avg_sku_per_store,
                'wow': avg_sku_per_store_wow
            },
            'sku_sold_out_rate': {
                'value': sku_sold_out_rate,
                'wow': sku_sold_out_rate_wow  # pp格式
            },
            'week': f"{target_year}-W{target_week:02d}",
            'data_source': 'real'
        }

    except Exception as e:
        logger.error(f"Error getting supply summary: {e}")
        return {
            'gmv': {'value': 0, 'wow': 0},
            'store_count': {'value': 0, 'wow': 0},
            'store_penetration': {'value': 0, 'wow': 0},
            'store_activity_rate': {'value': 0, 'wow': 0},
            'avg_sku_per_store': {'value': 0, 'wow': 0},
            'sku_sold_out_rate': {'value': 0, 'wow': 0},
            'week': '',
            'data_source': 'error',
            'error': str(e)
        }


def get_supply_trends_real(brand=''):
    """
    获取供给趋势数据 - 真实数据版本
    获取最近8个完整周的供给指标趋势数据，供给指标取周六数据

    Args:
        brand: 品牌筛选

    Returns:
        dict: 供给趋势数据
    """
    try:
        current_date = datetime.now()
        current_year = current_date.year
        current_week = current_date.isocalendar()[1]

        # 获取最近8个完整周（不包括当前周）
        trend_data = []

        # 构建品牌过滤条件 - 注意不同表使用不同的品牌字段
        # GMV数据表使用 brand 字段
        gmv_brand_filter = ""
        if brand and brand.strip():
            gmv_brand_filter = f"AND brand = '{brand.strip()}'"

        # 供给数据表使用 collect_brand 字段
        supply_brand_filter = ""
        if brand and brand.strip():
            supply_brand_filter = f"AND collect_brand = '{brand.strip()}'"

        for i in range(8):
            week_num = current_week - 1 - i  # 从上周开始往前推
            year = current_year

            if week_num <= 0:
                year = current_year - 1
                last_day_of_prev_year = datetime(year, 12, 31)
                week_num = last_day_of_prev_year.isocalendar()[1] + week_num

            # 获取该周的日期范围
            week_start, week_end = get_week_date_range(year, week_num)
            week_start_date = datetime.strptime(week_start, '%Y-%m-%d')

            # 获取周六日期（供给指标用）
            saturday_date = week_start_date + timedelta(days=5)  # 周六
            saturday_yyyymmdd = saturday_date.strftime('%Y%m%d')

            # 获取整周日期范围（GMV用）
            week_start_yyyymmdd = week_start.replace('-', '')
            week_end_yyyymmdd = week_end.replace('-', '')

            # 1. 获取GMV数据（整周，全量数据表）
            gmv_sql = f"""
            SELECT COALESCE(SUM(gmv), 0) as gmv
            FROM dws_o2o_sale_activity_detail_analysis_d
            WHERE ds BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}'
            AND source = '全量'
            AND platform = '美团'
            {gmv_brand_filter}
            """

            # 2. 获取供给指标数据（周六，只取美团平台）
            supply_sql = f"""
            SELECT
                COALESCE(SUM(onsale_poi_num), 0) as store_count,
                COALESCE(AVG(poi_permeate_rate), 0) as store_penetration,
                COALESCE(AVG(poi_sold_rate), 0) as store_activity_rate,
                COALESCE(AVG(store_avg_on_sales_sku), 0) as avg_sku_per_store,
                COALESCE(AVG(sku_sold_out_rate), 0) as sku_sold_out_rate
            FROM dws_mt_brand_city_details_daily_data_attribute_d
            WHERE ds = '{saturday_yyyymmdd}'
            AND platform = '美团'
            {supply_brand_filter}
            """

            gmv_result = execute_query(gmv_sql)
            supply_result = execute_query(supply_sql)

            gmv = float(gmv_result[0]['gmv']) if gmv_result else 0

            if supply_result:
                supply_data = supply_result[0]
                store_count = int(supply_data['store_count'])
                store_penetration = float(supply_data['store_penetration'])
                store_activity_rate = float(supply_data['store_activity_rate'])
                avg_sku_per_store = int(supply_data['avg_sku_per_store'])
                sku_sold_out_rate = float(supply_data['sku_sold_out_rate'])
            else:
                store_count = 0
                store_penetration = 0.0
                store_activity_rate = 0.0
                avg_sku_per_store = 0
                sku_sold_out_rate = 0.0

            # 获取上一周数据用于计算周环比
            prev_week = week_num - 1
            prev_year = year
            if prev_week <= 0:
                prev_week += 52
                prev_year -= 1

            prev_week_start, _ = get_week_date_range(prev_year, prev_week)
            prev_week_start_date = datetime.strptime(prev_week_start, '%Y-%m-%d')
            prev_saturday_date = prev_week_start_date + timedelta(days=5)
            prev_saturday_yyyymmdd = prev_saturday_date.strftime('%Y%m%d')

            # 上周GMV（整周）
            prev_week_end = (prev_week_start_date + timedelta(days=6)).strftime('%Y%m%d')
            prev_gmv_sql = f"""
            SELECT COALESCE(SUM(gmv), 0) as prev_gmv
            FROM dws_o2o_sale_activity_detail_analysis_d
            WHERE ds BETWEEN '{prev_week_start.replace("-", "")}' AND '{prev_week_end}'
            AND source = '全量'
            AND platform = '美团'
            {gmv_brand_filter}
            """

            # 上周供给指标（周六，只取美团平台）
            prev_saturday_yyyymmdd = prev_saturday_date.strftime('%Y%m%d')
            prev_supply_sql = f"""
            SELECT
                COALESCE(SUM(onsale_poi_num), 0) as prev_store_count,
                COALESCE(AVG(poi_permeate_rate), 0) as prev_store_penetration,
                COALESCE(AVG(poi_sold_rate), 0) as prev_store_activity_rate,
                COALESCE(AVG(store_avg_on_sales_sku), 0) as prev_avg_sku_per_store,
                COALESCE(AVG(sku_sold_out_rate), 0) as prev_sku_sold_out_rate
            FROM dws_mt_brand_city_details_daily_data_attribute_d
            WHERE ds = '{prev_saturday_yyyymmdd}'
            And platform = '美团'
            {supply_brand_filter}
            """

            prev_gmv_result = execute_query(prev_gmv_sql)
            prev_supply_result = execute_query(prev_supply_sql)

            prev_gmv = float(prev_gmv_result[0]['prev_gmv']) if prev_gmv_result else 0

            if prev_supply_result:
                prev_supply_data = prev_supply_result[0]
                prev_store_count = int(prev_supply_data['prev_store_count'])
                prev_store_penetration = float(prev_supply_data['prev_store_penetration'])
                prev_store_activity_rate = float(prev_supply_data['prev_store_activity_rate'])
                prev_avg_sku_per_store = int(prev_supply_data['prev_avg_sku_per_store'])
                prev_sku_sold_out_rate = float(prev_supply_data['prev_sku_sold_out_rate'])
            else:
                prev_store_count = 0
                prev_store_penetration = 0.0
                prev_store_activity_rate = 0.0
                prev_avg_sku_per_store = 0
                prev_sku_sold_out_rate = 0.0

            # 计算周环比
            def calculate_wow(current, previous):
                if previous == 0:
                    return 0.0
                return round((current - previous) / previous, 4)

            def calculate_pp_wow(current, previous):
                """计算pp格式的周环比（百分点差异）"""
                return round(current - previous, 4)

            gmv_wow = calculate_wow(gmv, prev_gmv)
            store_count_wow = calculate_wow(store_count, prev_store_count)
            store_penetration_wow = calculate_pp_wow(store_penetration, prev_store_penetration)
            store_activity_rate_wow = calculate_pp_wow(store_activity_rate, prev_store_activity_rate)
            avg_sku_per_store_wow = calculate_wow(avg_sku_per_store, prev_avg_sku_per_store)
            sku_sold_out_rate_wow = calculate_pp_wow(sku_sold_out_rate, prev_sku_sold_out_rate)

            trend_data.append({
                'week': week_num,
                'year': year,
                'gmv': gmv,
                'gmv_wow': gmv_wow,
                'store_count': store_count,
                'store_count_wow': store_count_wow,
                'store_penetration': store_penetration,
                'store_penetration_wow': store_penetration_wow,  # pp格式
                'store_activity_rate': store_activity_rate,
                'store_activity_rate_wow': store_activity_rate_wow,  # pp格式
                'avg_sku_per_store': avg_sku_per_store,
                'avg_sku_per_store_wow': avg_sku_per_store_wow,
                'sku_sold_out_rate': sku_sold_out_rate,
                'sku_sold_out_rate_wow': sku_sold_out_rate_wow  # pp格式
            })

        # 反转数据，使其按时间顺序排列（从旧到新）
        trend_data.reverse()

        return {'trend_data': trend_data, 'data_source': 'real'}

    except Exception as e:
        logger.error(f"Error getting supply trends: {e}")
        return {'trend_data': [], 'data_source': 'error', 'error': str(e)}


def get_supply_detail_real(dimension_type='整体', brand='', selected_week=None):
    """
    获取供给明细数据 - 真实数据版本

    Args:
        dimension_type: 维度类型 ('整体', '重点渠道', '重点商品', '子品牌')
        brand: 品牌筛选
        selected_week: 选定周次，如果为None则使用当前周

    Returns:
        dict: 供给明细数据
    """
    try:
        current_date = datetime.now()
        current_year = current_date.year
        current_week = current_date.isocalendar()[1]

        # 如果指定了选定周，使用指定的周次
        if selected_week:
            current_week = selected_week

        # 构建品牌过滤条件
        # GMV数据表使用 brand 字段
        gmv_brand_filter = ""
        if brand and brand.strip():
            gmv_brand_filter = f"AND brand = '{brand.strip()}'"

        # 供给数据表使用 collect_brand 字段
        supply_brand_filter = ""
        if brand and brand.strip():
            supply_brand_filter = f"AND collect_brand = '{brand.strip()}'"

        # 根据维度类型决定处理逻辑
        if dimension_type == '整体':
            # 整体维度：展示近8个完整周的数据，按周次倒序排列
            return get_supply_detail_weekly(current_year, current_week, gmv_brand_filter, supply_brand_filter)
        else:
            # 其他维度：展示选定周的数据
            return get_supply_detail_by_dimension(dimension_type, current_year, current_week, gmv_brand_filter, supply_brand_filter)

    except Exception as e:
        logger.error(f"Error getting supply detail: {e}")
        return {
            'dimension_type': dimension_type,
            'data': [],
            'data_source': 'error',
            'error': str(e)
        }


def get_supply_detail_weekly(current_year, current_week, gmv_brand_filter, supply_brand_filter):
    """
    获取整体维度的近8个完整周数据
    """
    try:
        detail_data = []

        for i in range(8):
            week_num = current_week - i  # 从当前周开始往前推，包括当前周
            year = current_year

            if week_num <= 0:
                year = current_year - 1
                last_day_of_prev_year = datetime(year, 12, 31)
                week_num = last_day_of_prev_year.isocalendar()[1] + week_num

            # 获取该周的日期范围
            week_start, week_end = get_week_date_range(year, week_num)
            week_start_date = datetime.strptime(week_start, '%Y-%m-%d')

            # 获取周六日期（供给指标用）
            saturday_date = week_start_date + timedelta(days=5)  # 周六
            saturday_yyyymmdd = saturday_date.strftime('%Y%m%d')

            # 获取整周日期范围（GMV用）
            week_start_yyyymmdd = week_start.replace('-', '')
            week_end_yyyymmdd = week_end.replace('-', '')

            # 1. 获取GMV数据（整周，全量数据表）
            gmv_sql = f"""
            SELECT COALESCE(SUM(gmv), 0) as gmv
            FROM dws_o2o_sale_activity_detail_analysis_d
            WHERE ds BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}'
            AND source = '全量'
            AND platform = '美团'
            {gmv_brand_filter}
            """

            # 2. 获取供给指标数据（周六，只取美团平台）
            supply_sql = f"""
            SELECT
                COALESCE(SUM(onsale_poi_num), 0) as store_count,
                COALESCE(AVG(poi_permeate_rate), 0) as store_penetration,
                COALESCE(AVG(poi_sold_rate), 0) as store_activity_rate,
                COALESCE(AVG(store_avg_on_sales_sku), 0) as avg_sku_per_store,
                COALESCE(AVG(sku_sold_out_rate), 0) as sku_sold_out_rate
            FROM dws_mt_brand_city_details_daily_data_attribute_d
            WHERE ds = '{saturday_yyyymmdd}'
            AND platform = '美团'
            {supply_brand_filter}
            """

            gmv_result = execute_query(gmv_sql)
            supply_result = execute_query(supply_sql)

            # 获取上一周数据用于计算周环比
            prev_week = week_num - 1
            prev_year = year
            if prev_week <= 0:
                prev_week += 52
                prev_year -= 1

            prev_week_start, _ = get_week_date_range(prev_year, prev_week)
            prev_week_start_date = datetime.strptime(prev_week_start, '%Y-%m-%d')
            prev_week_end = (prev_week_start_date + timedelta(days=6)).strftime('%Y%m%d')
            prev_week_start_yyyymmdd = prev_week_start.replace('-', '')

            # 上周GMV（整周）
            prev_gmv_sql = f"""
            SELECT COALESCE(SUM(gmv), 0) as prev_gmv
            FROM dws_o2o_sale_activity_detail_analysis_d
            WHERE ds BETWEEN '{prev_week_start_yyyymmdd}' AND '{prev_week_end}'
            AND source = '全量'
            AND platform = '美团'
            {gmv_brand_filter}
            """

            prev_gmv_result = execute_query(prev_gmv_sql)

            # 处理数据
            gmv = float(gmv_result[0]['gmv']) if gmv_result else 0
            prev_gmv = float(prev_gmv_result[0]['prev_gmv']) if prev_gmv_result else 0

            supply_data = supply_result[0] if supply_result else {
                'store_count': 0,
                'store_penetration': 0.0,
                'store_activity_rate': 0.0,
                'avg_sku_per_store': 0,
                'sku_sold_out_rate': 0.0
            }

            # 计算GMV周环比
            gmv_wow = (gmv - prev_gmv) / prev_gmv if prev_gmv > 0 else 0.0

            detail_data.append({
                'dimension_value': f'第{week_num}周',
                'gmv': gmv,
                'gmv_wow': round(gmv_wow, 4),
                'store_count': int(supply_data['store_count']),
                'store_penetration': float(supply_data['store_penetration']),
                'store_activity_rate': float(supply_data['store_activity_rate']),
                'avg_sku_per_store': int(supply_data['avg_sku_per_store']),
                'sku_sold_out_rate': float(supply_data['sku_sold_out_rate'])
            })

        # 数据已经是按时间倒序排列（最新的在前），不需要反转

        return {
            'data': detail_data,
            'data_source': 'real'
        }

    except Exception as e:
        logger.error(f"Error getting weekly supply detail: {e}")
        return {
            'data': [],
            'data_source': 'error',
            'error': str(e)
        }


def get_supply_detail_by_dimension(dimension_type, current_year, current_week, gmv_brand_filter, supply_brand_filter):
    """
    获取指定维度的选定周数据
    """
    try:
        # 获取选定周的日期范围
        week_start, week_end = get_week_date_range(current_year, current_week)
        week_start_date = datetime.strptime(week_start, '%Y-%m-%d')
        saturday_date = week_start_date + timedelta(days=5)  # 周六
        saturday_yyyymmdd = saturday_date.strftime('%Y%m%d')
        week_start_yyyymmdd = week_start.replace('-', '')
        week_end_yyyymmdd = week_end.replace('-', '')

        # 根据维度类型选择数据表和分组字段
        if dimension_type == '城市':
            supply_table = 'dws_mt_channel_city_details_daily_data_attribute_d'
            group_field = "standard_city as dimension_value"
            group_by_clause = "GROUP BY standard_city"
            order_by_clause = "ORDER BY SUM(onsale_poi_num) DESC"
            supply_fields = """
                COALESCE(SUM(onsale_poi_num), 0) as store_count,
                COALESCE(AVG(poi_permeate_rate), 0) as store_penetration,
                COALESCE(AVG(poi_sold_rate), 0) as store_activity_rate,
                COALESCE(AVG(store_avg_on_sales_sku), 0) as avg_sku_per_store,
                COALESCE(AVG(sku_sold_out_rate), 0) as sku_sold_out_rate
            """
        elif dimension_type == '重点渠道':
            supply_table = 'dws_mt_channel_city_details_daily_data_attribute_d'
            group_field = "vender_name as dimension_value"
            group_by_clause = "GROUP BY vender_name"
            order_by_clause = "ORDER BY SUM(onsale_poi_num) DESC"
            supply_fields = """
                COALESCE(SUM(onsale_poi_num), 0) as store_count,
                COALESCE(AVG(poi_permeate_rate), 0) as store_penetration,
                COALESCE(AVG(poi_sold_rate), 0) as store_activity_rate,
                COALESCE(AVG(store_avg_on_sales_sku), 0) as avg_sku_per_store,
                COALESCE(AVG(sku_sold_out_rate), 0) as sku_sold_out_rate
            """
        elif dimension_type == '重点商品':
            supply_table = 'dws_t_mt_sales_by_upc_list_attribute_d'
            group_field = "CONCAT(upc, '-', product_name) as dimension_value"
            group_by_clause = "GROUP BY upc, product_name"
            order_by_clause = "ORDER BY SUM(onsale_poi_num) DESC"
            supply_fields = """
                COALESCE(SUM(onsale_poi_num), 0) as store_count,
                COALESCE(AVG(store_sales_rate), 0) as store_penetration,
                COALESCE(AVG(sku_sell_out_rate), 0) as store_activity_rate,
                COALESCE(AVG(store_avg_on_sales_sku), 0) as avg_sku_per_store,
                COALESCE(AVG(sku_sales_out_rate), 0) as sku_sold_out_rate
            """
        elif dimension_type == '子品牌':
            supply_table = 'dws_mt_channel_city_details_daily_data_attribute_d'
            group_field = "collect_sub_brand as dimension_value"
            group_by_clause = "GROUP BY collect_sub_brand"
            order_by_clause = "ORDER BY SUM(onsale_poi_num) DESC"
            supply_fields = """
                COALESCE(SUM(onsale_poi_num), 0) as store_count,
                COALESCE(AVG(poi_permeate_rate), 0) as store_penetration,
                COALESCE(AVG(poi_sold_rate), 0) as store_activity_rate,
                COALESCE(AVG(store_avg_on_sales_sku), 0) as avg_sku_per_store,
                COALESCE(AVG(sku_sold_out_rate), 0) as sku_sold_out_rate
            """
        else:
            return {
                'data': [],
                'data_source': 'error',
                'error': f'Unknown dimension type: {dimension_type}'
            }

        # 对于子品牌维度，先获取供给表的子品牌列表，然后查询对应的GMV
        if dimension_type == '子品牌':
            return get_supply_detail_sub_brand_special(current_year, current_week, gmv_brand_filter, supply_brand_filter)

        # 根据维度类型构建GMV查询的分组字段
        if dimension_type == '城市':
            gmv_group_field = "standard_city as dimension_value"
            gmv_group_by_clause = "GROUP BY standard_city"
        elif dimension_type == '重点渠道':
            gmv_group_field = "vender_name as dimension_value"
            gmv_group_by_clause = "GROUP BY vender_name"
        elif dimension_type == '重点商品':
            gmv_group_field = "CONCAT(upc, '-', product_name) as dimension_value"
            gmv_group_by_clause = "GROUP BY upc, product_name"
        else:
            gmv_group_field = group_field
            gmv_group_by_clause = group_by_clause

        # 1. 获取GMV数据（整周，按维度分组）
        gmv_sql = f"""
        SELECT
            {gmv_group_field},
            COALESCE(SUM(gmv), 0) as gmv
        FROM dws_o2o_sale_activity_detail_analysis_d
        WHERE ds BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}'
        AND source = '全量'
        AND platform = '美团'
        {gmv_brand_filter}
        {gmv_group_by_clause}
        ORDER BY SUM(gmv) DESC
        LIMIT 10
        """

        # 2. 获取供给指标数据（周六，按维度分组）
        supply_sql = f"""
        SELECT
            {group_field},
            {supply_fields}
        FROM {supply_table}
        WHERE ds = '{saturday_yyyymmdd}'
        AND platform = '美团'
        {supply_brand_filter}
        {group_by_clause}
        {order_by_clause}
        LIMIT 10
        """

        gmv_result = execute_query(gmv_sql)
        supply_result = execute_query(supply_sql)

        # 将供给数据转换为字典以便查找
        supply_dict = {}
        if supply_result:
            for row in supply_result:
                dimension_value = row['dimension_value'] or '未知'
                supply_dict[dimension_value] = {
                    'store_count': int(row['store_count']),
                    'store_penetration': float(row['store_penetration']),
                    'store_activity_rate': float(row['store_activity_rate']),
                    'avg_sku_per_store': int(row['avg_sku_per_store']),
                    'sku_sold_out_rate': float(row['sku_sold_out_rate'])
                }

        # 处理数据
        detail_data = []
        if gmv_result:
            for row in gmv_result:
                dimension_value = row['dimension_value'] or '未知'
                gmv = float(row['gmv'])

                # 获取供给指标
                supply_data = supply_dict.get(dimension_value, {
                    'store_count': 0,
                    'store_penetration': 0.0,
                    'store_activity_rate': 0.0,
                    'avg_sku_per_store': 0,
                    'sku_sold_out_rate': 0.0
                })

                detail_data.append({
                    'dimension_value': dimension_value,
                    'gmv': gmv,
                    'gmv_wow': 0.0,  # 非整体维度暂不计算周环比
                    'store_count': supply_data['store_count'],
                    'store_penetration': supply_data['store_penetration'],
                    'store_activity_rate': supply_data['store_activity_rate'],
                    'avg_sku_per_store': supply_data['avg_sku_per_store'],
                    'sku_sold_out_rate': supply_data['sku_sold_out_rate']
                })

        return {
            'data': detail_data,
            'data_source': 'real'
        }

    except Exception as e:
        logger.error(f"Error getting dimension supply detail: {e}")
        return {
            'data': [],
            'data_source': 'error',
            'error': str(e)
        }


def get_supply_detail_sub_brand_special(current_year, current_week, gmv_brand_filter, supply_brand_filter):
    """
    子品牌维度的特殊处理函数
    先从供给表获取子品牌列表，然后查询对应的GMV数据
    """
    try:
        # 获取选定周的日期范围
        week_start, week_end = get_week_date_range(current_year, current_week)
        week_start_date = datetime.strptime(week_start, '%Y-%m-%d')
        saturday_date = week_start_date + timedelta(days=5)  # 周六
        saturday_yyyymmdd = saturday_date.strftime('%Y%m%d')
        week_start_yyyymmdd = week_start.replace('-', '')
        week_end_yyyymmdd = week_end.replace('-', '')

        # 1. 先从供给表获取子品牌列表和供给指标
        supply_sql = f"""
        SELECT
            collect_sub_brand as dimension_value,
            COALESCE(SUM(onsale_poi_num), 0) as store_count,
            COALESCE(AVG(poi_permeate_rate), 0) as store_penetration,
            COALESCE(AVG(poi_sold_rate), 0) as store_activity_rate,
            COALESCE(AVG(store_avg_on_sales_sku), 0) as avg_sku_per_store,
            COALESCE(AVG(sku_sold_out_rate), 0) as sku_sold_out_rate
        FROM dws_mt_channel_city_details_daily_data_attribute_d
        WHERE ds = '{saturday_yyyymmdd}'
        AND platform = '美团'
        {supply_brand_filter}
        AND collect_sub_brand IS NOT NULL
        GROUP BY collect_sub_brand
        ORDER BY SUM(onsale_poi_num) DESC
        LIMIT 10
        """

        supply_result = execute_query(supply_sql)

        if not supply_result:
            return {
                'data': [],
                'data_source': 'real'
            }

        # 2. 获取上一周的供给数据用于计算周环比
        prev_week = current_week - 1
        prev_year = current_year
        if prev_week <= 0:
            prev_week += 52
            prev_year -= 1

        prev_week_start, _ = get_week_date_range(prev_year, prev_week)
        prev_week_start_date = datetime.strptime(prev_week_start, '%Y-%m-%d')
        prev_saturday_date = prev_week_start_date + timedelta(days=5)
        prev_saturday_yyyymmdd = prev_saturday_date.strftime('%Y%m%d')
        prev_week_start_yyyymmdd = prev_week_start.replace('-', '')
        prev_week_end_yyyymmdd = (prev_week_start_date + timedelta(days=6)).strftime('%Y%m%d')

        # 查询上一周的供给数据
        prev_supply_sql = f"""
        SELECT
            collect_sub_brand as dimension_value,
            COALESCE(SUM(onsale_poi_num), 0) as store_count,
            COALESCE(AVG(poi_permeate_rate), 0) as store_penetration,
            COALESCE(AVG(poi_sold_rate), 0) as store_activity_rate,
            COALESCE(AVG(store_avg_on_sales_sku), 0) as avg_sku_per_store,
            COALESCE(AVG(sku_sold_out_rate), 0) as sku_sold_out_rate
        FROM dws_mt_channel_city_details_daily_data_attribute_d
        WHERE ds = '{prev_saturday_yyyymmdd}'
        AND platform = '美团'
        {supply_brand_filter}
        AND collect_sub_brand IS NOT NULL
        GROUP BY collect_sub_brand
        """

        prev_supply_result = execute_query(prev_supply_sql)

        # 将上一周数据转换为字典以便查找
        prev_supply_dict = {}
        if prev_supply_result:
            for row in prev_supply_result:
                prev_supply_dict[row['dimension_value']] = row

        # 3. 为每个子品牌查询GMV数据并计算周环比
        detail_data = []

        for supply_row in supply_result:
            sub_brand_supply = supply_row['dimension_value']

            # 建立子品牌映射关系（供给表 -> GMV表）
            # 供给表中的"圣农"对应GMV表中的"圣农"或"圣农（Sunner）"
            if sub_brand_supply == '圣农':
                # 查询当前周GMV
                gmv_sql = f"""
                SELECT COALESCE(SUM(gmv), 0) as gmv
                FROM dws_o2o_sale_activity_detail_analysis_d
                WHERE ds BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}'
                AND source = '全量'
                AND platform = '美团'
                {gmv_brand_filter}
                AND (sub_brand = '圣农' OR sub_brand = '圣农（Sunner）')
                """

                # 查询上一周GMV
                prev_gmv_sql = f"""
                SELECT COALESCE(SUM(gmv), 0) as gmv
                FROM dws_o2o_sale_activity_detail_analysis_d
                WHERE ds BETWEEN '{prev_week_start_yyyymmdd}' AND '{prev_week_end_yyyymmdd}'
                AND source = '全量'
                AND platform = '美团'
                {gmv_brand_filter}
                AND (sub_brand = '圣农' OR sub_brand = '圣农（Sunner）')
                """
            else:
                # 其他子品牌直接匹配
                gmv_sql = f"""
                SELECT COALESCE(SUM(gmv), 0) as gmv
                FROM dws_o2o_sale_activity_detail_analysis_d
                WHERE ds BETWEEN '{week_start_yyyymmdd}' AND '{week_end_yyyymmdd}'
                AND source = '全量'
                AND platform = '美团'
                {gmv_brand_filter}
                AND sub_brand = '{sub_brand_supply}'
                """

                prev_gmv_sql = f"""
                SELECT COALESCE(SUM(gmv), 0) as gmv
                FROM dws_o2o_sale_activity_detail_analysis_d
                WHERE ds BETWEEN '{prev_week_start_yyyymmdd}' AND '{prev_week_end_yyyymmdd}'
                AND source = '全量'
                AND platform = '美团'
                {gmv_brand_filter}
                AND sub_brand = '{sub_brand_supply}'
                """

            gmv_result = execute_query(gmv_sql)
            prev_gmv_result = execute_query(prev_gmv_sql)

            current_gmv = float(gmv_result[0]['gmv']) if gmv_result else 0
            prev_gmv = float(prev_gmv_result[0]['gmv']) if prev_gmv_result else 0

            # 获取上一周的供给数据
            prev_supply_data = prev_supply_dict.get(sub_brand_supply, {})

            # 计算周环比的辅助函数
            def calculate_wow(current, previous):
                if previous == 0:
                    return 0.0
                return round((current - previous) / previous, 4)

            def calculate_pp_wow(current, previous):
                """计算pp格式的周环比（百分点差异）"""
                return round(current - previous, 4)

            # 计算各项指标的周环比
            gmv_wow = calculate_wow(current_gmv, prev_gmv)
            store_count_wow = calculate_wow(
                int(supply_row['store_count']),
                int(prev_supply_data.get('store_count', 0))
            )
            store_penetration_wow = calculate_pp_wow(
                float(supply_row['store_penetration']),
                float(prev_supply_data.get('store_penetration', 0))
            )
            store_activity_rate_wow = calculate_pp_wow(
                float(supply_row['store_activity_rate']),
                float(prev_supply_data.get('store_activity_rate', 0))
            )
            avg_sku_per_store_wow = calculate_wow(
                int(supply_row['avg_sku_per_store']),
                int(prev_supply_data.get('avg_sku_per_store', 0))
            )
            sku_sold_out_rate_wow = calculate_pp_wow(
                float(supply_row['sku_sold_out_rate']),
                float(prev_supply_data.get('sku_sold_out_rate', 0))
            )

            # 使用GMV表中的显示名称（更完整）
            display_name = '圣农（Sunner）' if sub_brand_supply == '圣农' else sub_brand_supply

            detail_data.append({
                'dimension_value': display_name,
                'gmv': current_gmv,
                'gmv_wow': gmv_wow,
                'store_count': int(supply_row['store_count']),
                'store_count_wow': store_count_wow,
                'store_penetration': float(supply_row['store_penetration']),
                'store_penetration_wow': store_penetration_wow,
                'store_activity_rate': float(supply_row['store_activity_rate']),
                'store_activity_rate_wow': store_activity_rate_wow,
                'avg_sku_per_store': int(supply_row['avg_sku_per_store']),
                'avg_sku_per_store_wow': avg_sku_per_store_wow,
                'sku_sold_out_rate': float(supply_row['sku_sold_out_rate']),
                'sku_sold_out_rate_wow': sku_sold_out_rate_wow
            })

        return {
            'data': detail_data,
            'data_source': 'real'
        }

    except Exception as e:
        logger.error(f"Error getting sub-brand supply detail: {e}")
        return {
            'data': [],
            'data_source': 'error',
            'error': str(e)
        }


