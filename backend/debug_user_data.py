#!/usr/bin/env python3
"""
调试用户数据查询
"""

from database import execute_query
from datetime import datetime, timedelta
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_user_table_structure():
    """检查用户数据表结构"""
    print("=== 检查用户数据表结构 ===")
    
    try:
        # 查看表结构
        sql = """
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'ods_heyin_bi_data_t_mt_user_list_by_day'
        ORDER BY ordinal_position
        """
        
        result = execute_query(sql)
        print(f"表字段数量: {len(result)}")
        
        for row in result:
            print(f"  {row['column_name']}: {row['data_type']} ({'NULL' if row['is_nullable'] == 'YES' else 'NOT NULL'})")
            
    except Exception as e:
        print(f"检查表结构失败: {e}")

def check_user_data_exists():
    """检查用户数据是否存在"""
    print("\n=== 检查用户数据是否存在 ===")
    
    try:
        # 检查表中是否有数据
        sql = "SELECT COUNT(*) as total_count FROM ods_heyin_bi_data_t_mt_user_list_by_day"
        result = execute_query(sql)
        total_count = result[0]['total_count'] if result else 0
        print(f"表中总记录数: {total_count}")
        
        if total_count > 0:
            # 检查日期范围
            sql = "SELECT MIN(dt) as min_date, MAX(dt) as max_date FROM ods_heyin_bi_data_t_mt_user_list_by_day"
            result = execute_query(sql)
            if result:
                print(f"日期范围: {result[0]['min_date']} 到 {result[0]['max_date']}")
            
            # 检查用户状态
            sql = "SELECT userstatus, COUNT(*) as count FROM ods_heyin_bi_data_t_mt_user_list_by_day GROUP BY userstatus"
            result = execute_query(sql)
            print("用户状态分布:")
            for row in result:
                print(f"  {row['userstatus']}: {row['count']}")
            
            # 检查品牌
            sql = "SELECT brand, COUNT(*) as count FROM ods_heyin_bi_data_t_mt_user_list_by_day GROUP BY brand LIMIT 10"
            result = execute_query(sql)
            print("品牌分布（前10个）:")
            for row in result:
                print(f"  {row['brand']}: {row['count']}")
            
            # 检查子品牌
            sql = "SELECT sub_brand, COUNT(*) as count FROM ods_heyin_bi_data_t_mt_user_list_by_day GROUP BY sub_brand LIMIT 10"
            result = execute_query(sql)
            print("子品牌分布（前10个）:")
            for row in result:
                print(f"  {row['sub_brand']}: {row['count']}")
                
    except Exception as e:
        print(f"检查数据存在性失败: {e}")

def check_recent_data():
    """检查最近的数据"""
    print("\n=== 检查最近的数据 ===")
    
    try:
        # 获取最近30天的数据
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        sql = f"""
        SELECT 
            dt,
            userstatus,
            sub_brand,
            brand,
            SUM(usernum) as total_users,
            SUM(brandoriginproductpricegmv) as total_gmv,
            AVG(brandorigincustprice) as avg_price
        FROM ods_heyin_bi_data_t_mt_user_list_by_day
        WHERE dt BETWEEN '{start_date}' AND '{end_date}'
        AND userstatus IN ('新用户', '老用户')
        AND sub_brand = '全部品牌'
        GROUP BY dt, userstatus, sub_brand, brand
        ORDER BY dt DESC, userstatus
        LIMIT 20
        """
        
        result = execute_query(sql)
        print(f"最近30天数据记录数: {len(result)}")
        
        for row in result:
            print(f"  {row['dt']} {row['userstatus']} {row['brand']}: 用户{row['total_users']}, GMV{row['total_gmv']}, 客单价{row['avg_price']}")
            
    except Exception as e:
        print(f"检查最近数据失败: {e}")

def test_specific_week():
    """测试特定周的数据"""
    print("\n=== 测试特定周的数据 ===")
    
    try:
        # 测试第45周的数据（2024年11月4日-10日）
        week_start = '2024-11-04'
        week_end = '2024-11-10'
        
        sql = f"""
        SELECT 
            dt,
            userstatus,
            sub_brand,
            brand,
            SUM(usernum) as total_users,
            SUM(brandoriginproductpricegmv) as total_gmv,
            AVG(brandorigincustprice) as avg_price
        FROM ods_heyin_bi_data_t_mt_user_list_by_day
        WHERE dt BETWEEN '{week_start}' AND '{week_end}'
        AND userstatus IN ('新用户', '老用户')
        AND sub_brand = '全部品牌'
        GROUP BY dt, userstatus, sub_brand, brand
        ORDER BY dt, userstatus
        """
        
        result = execute_query(sql)
        print(f"第45周数据记录数: {len(result)}")
        
        if result:
            for row in result:
                print(f"  {row['dt']} {row['userstatus']} {row['brand']}: 用户{row['total_users']}, GMV{row['total_gmv']}, 客单价{row['avg_price']}")
        else:
            print("  没有找到第45周的数据")
            
            # 尝试查找任何有数据的日期
            sql = """
            SELECT dt, COUNT(*) as count
            FROM ods_heyin_bi_data_t_mt_user_list_by_day
            WHERE userstatus IN ('新用户', '老用户')
            AND sub_brand = '全部品牌'
            GROUP BY dt
            ORDER BY dt DESC
            LIMIT 10
            """
            
            result = execute_query(sql)
            print("最近有数据的日期:")
            for row in result:
                print(f"  {row['dt']}: {row['count']} 条记录")
            
    except Exception as e:
        print(f"测试特定周数据失败: {e}")

def check_data_format():
    """检查数据格式"""
    print("\n=== 检查数据格式 ===")
    
    try:
        sql = """
        SELECT 
            dt,
            userstatus,
            sub_brand,
            brand,
            usernum,
            brandoriginproductpricegmv,
            brandorigincustprice
        FROM ods_heyin_bi_data_t_mt_user_list_by_day
        WHERE usernum > 0
        ORDER BY dt DESC
        LIMIT 5
        """
        
        result = execute_query(sql)
        print(f"有用户数据的记录（前5条）:")
        
        for row in result:
            print(f"  日期: {row['dt']}")
            print(f"  用户状态: {row['userstatus']}")
            print(f"  子品牌: {row['sub_brand']}")
            print(f"  品牌: {row['brand']}")
            print(f"  用户数: {row['usernum']}")
            print(f"  GMV: {row['brandoriginproductpricegmv']}")
            print(f"  客单价: {row['brandorigincustprice']}")
            print("  ---")
            
    except Exception as e:
        print(f"检查数据格式失败: {e}")

if __name__ == "__main__":
    print(f"开始调试用户数据 - {datetime.now()}")
    
    check_user_table_structure()
    check_user_data_exists()
    check_recent_data()
    test_specific_week()
    check_data_format()
    
    print(f"\n调试完成 - {datetime.now()}")
