#!/usr/bin/env python3
"""
调试年同比数据问题
"""

from database import execute_query
from datetime import datetime, timedelta
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_week_date_range(year, week):
    """根据年份和周数获取该周的日期范围"""
    # 使用ISO周计算
    jan_4 = datetime(year, 1, 4)
    week_1_monday = jan_4 - timedelta(days=jan_4.weekday())
    week_start = week_1_monday + timedelta(weeks=week-1)
    week_end = week_start + timedelta(days=6)
    return week_start.strftime('%Y-%m-%d'), week_end.strftime('%Y-%m-%d')

def check_yoy_data():
    """检查年同比数据"""
    print("=== 检查年同比数据 ===")
    
    try:
        # 检查数据的年份范围
        sql = """
        SELECT 
            SUBSTRING(dt, 1, 4) as year,
            COUNT(*) as count,
            MIN(dt) as min_date,
            MAX(dt) as max_date
        FROM ods_heyin_bi_data_t_mt_user_list_by_day
        WHERE userstatus IN ('新用户', '老用户')
        GROUP BY SUBSTRING(dt, 1, 4)
        ORDER BY year
        """
        
        result = execute_query(sql)
        print("数据年份分布:")
        for row in result:
            print(f"  {row['year']}年: {row['count']} 条记录, 日期范围: {row['min_date']} - {row['max_date']}")
        
        # 检查当前年和去年的数据
        current_year = datetime.now().year  # 使用真实的当前年份
        last_year = current_year - 1
        
        print(f"\n检查{current_year}年和{last_year}年的数据:")
        
        # 检查第29周的数据
        current_week = 29
        
        # 当前年第29周
        current_week_start, current_week_end = get_week_date_range(current_year, current_week)
        current_week_start_yyyymmdd = current_week_start.replace('-', '')
        current_week_end_yyyymmdd = current_week_end.replace('-', '')
        
        print(f"\n{current_year}年第{current_week}周 ({current_week_start} 到 {current_week_end}):")
        
        sql = f"""
        SELECT 
            userstatus,
            SUM(usernum) as total_users,
            SUM(brandoriginproductpricegmv) as total_gmv
        FROM ods_heyin_bi_data_t_mt_user_list_by_day
        WHERE dt BETWEEN '{current_week_start_yyyymmdd}' AND '{current_week_end_yyyymmdd}'
        AND userstatus IN ('新用户', '老用户')
        GROUP BY userstatus
        ORDER BY userstatus
        """
        
        result = execute_query(sql)
        for row in result:
            print(f"  {row['userstatus']}: 用户{row['total_users']:,}, GMV¥{row['total_gmv']:,.0f}")
        
        # 去年同期第29周
        last_year_week_start, last_year_week_end = get_week_date_range(last_year, current_week)
        last_year_week_start_yyyymmdd = last_year_week_start.replace('-', '')
        last_year_week_end_yyyymmdd = last_year_week_end.replace('-', '')
        
        print(f"\n{last_year}年第{current_week}周 ({last_year_week_start} 到 {last_year_week_end}):")
        
        sql = f"""
        SELECT 
            userstatus,
            SUM(usernum) as total_users,
            SUM(brandoriginproductpricegmv) as total_gmv
        FROM ods_heyin_bi_data_t_mt_user_list_by_day
        WHERE dt BETWEEN '{last_year_week_start_yyyymmdd}' AND '{last_year_week_end_yyyymmdd}'
        AND userstatus IN ('新用户', '老用户')
        GROUP BY userstatus
        ORDER BY userstatus
        """
        
        result = execute_query(sql)
        if result:
            for row in result:
                print(f"  {row['userstatus']}: 用户{row['total_users']:,}, GMV¥{row['total_gmv']:,.0f}")
        else:
            print(f"  没有找到{last_year}年第{current_week}周的数据")
        
        # 检查去年是否有任何数据
        print(f"\n检查{last_year}年是否有任何用户数据:")
        sql = f"""
        SELECT COUNT(*) as count
        FROM ods_heyin_bi_data_t_mt_user_list_by_day
        WHERE SUBSTRING(dt, 1, 4) = '{last_year}'
        AND userstatus IN ('新用户', '老用户')
        """
        
        result = execute_query(sql)
        if result:
            count = result[0]['count']
            print(f"  {last_year}年总记录数: {count}")
            
            if count == 0:
                print(f"  ❌ {last_year}年没有数据，这就是年同比为0的原因")
            else:
                print(f"  ✅ {last_year}年有数据，需要进一步检查")
        
        # 检查最早的数据日期
        print(f"\n检查最早的数据日期:")
        sql = """
        SELECT MIN(dt) as earliest_date
        FROM ods_heyin_bi_data_t_mt_user_list_by_day
        WHERE userstatus IN ('新用户', '老用户')
        """
        
        result = execute_query(sql)
        if result:
            earliest_date = result[0]['earliest_date']
            print(f"  最早数据日期: {earliest_date}")
            
            if earliest_date:
                earliest_year = int(earliest_date[:4])
                if earliest_year >= current_year:
                    print(f"  ❌ 数据只有{earliest_year}年及以后，没有历史数据用于年同比计算")
                else:
                    print(f"  ✅ 有{earliest_year}年的历史数据")
            
    except Exception as e:
        print(f"检查年同比数据失败: {e}")

def suggest_solution():
    """建议解决方案"""
    print("\n=== 解决方案建议 ===")
    print("1. 如果数据库中确实没有去年的数据，年同比显示为0是正确的")
    print("2. 可以考虑以下处理方式:")
    print("   - 在前端显示'--'或'N/A'而不是0%")
    print("   - 添加数据说明，告知用户年同比需要历史数据")
    print("   - 如果有历史数据，检查日期格式和查询逻辑")
    print("3. 建议在表格中对无法计算的年同比使用特殊标识")

if __name__ == "__main__":
    print(f"开始调试年同比数据 - {datetime.now()}")
    
    check_yoy_data()
    suggest_solution()
    
    print(f"\n调试完成 - {datetime.now()}")
