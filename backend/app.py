from flask import Flask, jsonify, request, send_file
from flask_cors import CORS
from datetime import datetime, timedelta
from openai import OpenAI
import json
import os
import tempfile
import threading
import uuid
from real_data_queries import (
    get_trading_summary_real,
    get_trading_trends_real,
    get_dimension_analysis_real,
    get_dimension_trends_real,
    get_top10_data_real,
    get_activity_summary_real,
    get_activity_trends_real,
    get_activity_platform_charts_real,
    get_activity_detail_real_weeks,
    get_activity_detail_real_dimension,
    get_rtb_summary_real,
    get_rtb_trends_real,
    get_rtb_detail_real,
    get_user_data_summary_real,
    get_user_data_trends_real,
    get_user_data_detail_real,
    get_user_platform_charts_real,
    get_supply_summary_real,
    get_supply_trends_real,
    get_supply_detail_real
)
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
import random

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# Mock数据生成器
def generate_gmv_data(weeks=8):
    """生成GMV趋势数据"""
    data = []
    base_gmv = 1000000  # 基础GMV 100万
    
    for i in range(weeks):
        week_num = 52 - weeks + i + 1  # 当前周之前的8周
        gmv = base_gmv * (0.8 + random.random() * 0.4)  # 80%-120%的随机波动
        yoy = random.uniform(-0.2, 0.3)  # 年同比 -20%到30%
        wow = random.uniform(-0.15, 0.25)  # 周环比 -15%到25%
        
        data.append({
            'week': week_num,
            'gmv': round(gmv, 2),
            'yoy': round(yoy, 3),
            'wow': round(wow, 3)
        })
    
    return data

def generate_platform_data():
    """生成平台数据"""
    platforms = ['美团', '饿了么', '京东到家', '多点', '淘鲜达']
    data = {}
    
    for platform in platforms:
        data[platform] = {
            'gmv': round(random.uniform(500000, 2000000), 2),
            'percentage': round(random.uniform(15, 25), 1)
        }
    
    return data

def generate_dimension_data(dimension_type):
    """生成维度分析数据"""
    dimensions = {
        '子品牌': ['品牌A', '品牌B', '品牌C', '品牌D', '品牌E'],
        '品线': ['生鲜', '食品', '日用', '母婴', '酒水'],
        '渠道类型': ['O2O', 'B2C', 'B2B', '直营', '加盟'],
        '大区': ['华北', '华东', '华南', '西南', '东北']
    }
    
    result = []
    for dim in dimensions[dimension_type]:
        trend_data = []
        for week in range(1, 9):
            gmv = random.uniform(100000, 500000)
            trend_data.append({
                'week': week,
                'gmv': round(gmv, 2)
            })
        
        result.append({
            'dimension': dim,
            'trend': trend_data,
            'total_gmv': sum([t['gmv'] for t in trend_data]),
            'yoy': round(random.uniform(-0.2, 0.3), 3),
            'wow': round(random.uniform(-0.15, 0.25), 3)
        })
    
    return result

def generate_top10_data(category):
    """生成Top10数据"""
    categories = {
        '商品': ['苹果', '香蕉', '牛奶', '面包', '鸡蛋', '大米', '西红柿', '黄瓜', '猪肉', '鸡胸肉'],
        '城市': ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉', '南京', '重庆', '西安'],
        '零售商': ['沃尔玛', '家乐福', '华润万家', '永辉超市', '大润发', '物美', '世纪联华', '卜蜂莲花', '新华都', '人人乐']
    }
    
    items = categories[category]
    data = []
    
    for i, item in enumerate(items):
        gmv = random.uniform(50000, 200000) * (10 - i) / 10  # 排名越高GMV越大
        data.append({
            'rank': i + 1,
            'name': item,
            'gmv': round(gmv, 2),
            'yoy': round(random.uniform(-0.2, 0.3), 3),
            'wow': round(random.uniform(-0.15, 0.25), 3)
        })
    
    return data

@app.route('/api/trading/summary', methods=['GET'])
def get_trading_summary():
    """获取交易数据概览 - 使用真实数据"""
    week = int(request.args.get('week', 45))  # 默认第45周
    platform = request.args.get('platform', '全平台')
    brand = request.args.get('brand', '')  # 品牌参数

    try:
        # 使用真实数据查询
        data = get_trading_summary_real(week, platform, brand)
        logger.info(f"Successfully retrieved trading summary for week {week}, platform {platform}, brand {brand}")
        return jsonify(data)
    except Exception as e:
        logger.error(f"Error getting trading summary: {e}")
        # 如果真实数据查询失败，返回mock数据作为备用
        logger.warning("Falling back to mock data")

        # 当前周数据
        current_week_gmv = random.uniform(8000000, 12000000)

        # MTD数据
        mtd_current = random.uniform(25000000, 35000000)
        mtd_target = 40000000
        mtd_rate = mtd_current / mtd_target

        # YTD数据
        ytd_current = random.uniform(400000000, 500000000)
        ytd_target = 600000000
        ytd_rate = ytd_current / ytd_target

        return jsonify({
            'week_data': {
                'week': week,
                'gmv': round(current_week_gmv, 2),
                'yoy': round(random.uniform(-0.1, 0.2), 3),
                'wow': round(random.uniform(-0.05, 0.15), 3)
            },
            'mtd_data': {
                'current': round(mtd_current, 2),
                'target': mtd_target,
                'rate': round(mtd_rate, 3),
                'yoy': round(random.uniform(-0.1, 0.2), 3),
                'mom': round(random.uniform(-0.05, 0.15), 3)  # Mock数据
            },
            'ytd_data': {
                'current': round(ytd_current, 2),
                'target': ytd_target,
                'rate': round(ytd_rate, 3),
                'yoy': round(random.uniform(-0.05, 0.15), 3)
            },
            'data_source': 'mock'  # 标识这是mock数据
        })

@app.route('/api/trading/trends', methods=['GET'])
def get_trading_trends():
    """获取GMV趋势数据 - 使用真实数据"""
    platform = request.args.get('platform', '全平台')
    brand = request.args.get('brand', '')  # 品牌参数

    try:
        # 使用真实数据查询
        data = get_trading_trends_real(platform, brand)
        logger.info(f"Successfully retrieved trading trends for platform {platform}, brand {brand}")
        return jsonify(data)
    except Exception as e:
        logger.error(f"Error getting trading trends: {e}")
        # 如果真实数据查询失败，返回mock数据作为备用
        logger.warning("Falling back to mock data")

        return jsonify({
            'trend_data': generate_gmv_data(8),
            'platform_data': generate_platform_data() if platform == '全平台' else None,
            'data_source': 'mock'  # 标识这是mock数据
        })

@app.route('/api/trading/dimensions', methods=['GET'])
def get_dimension_analysis():
    """获取维度分析数据 - 使用真实数据"""
    dimension_type = request.args.get('type', '子品牌')
    brand = request.args.get('brand', '')  # 品牌参数
    platform = request.args.get('platform', '全平台')  # 平台参数

    try:
        # 使用真实数据查询
        data = get_dimension_analysis_real(dimension_type, brand, platform)
        logger.info(f"Successfully retrieved dimension analysis for {dimension_type}, brand {brand}, platform {platform}")
        return jsonify(data)
    except Exception as e:
        logger.error(f"Error getting dimension analysis: {e}")
        # 如果真实数据查询失败，返回mock数据作为备用
        logger.warning("Falling back to mock data")

        return jsonify({
            'dimension': dimension_type,
            'data': generate_dimension_data(dimension_type),
            'data_source': 'mock'  # 标识这是mock数据
        })

@app.route('/api/trading/dimension-trends', methods=['GET'])
def get_dimension_trends():
    """获取维度趋势数据 - 用于明细数据表格"""
    dimension_type = request.args.get('type', '子品牌')
    brand = request.args.get('brand', '')
    week = request.args.get('week', None)
    platform = request.args.get('platform', '全平台')  # 平台参数

    try:
        # 使用真实数据查询
        data = get_dimension_trends_real(dimension_type, brand, week, platform)
        logger.info(f"Successfully retrieved dimension trends for {dimension_type}, brand {brand}, week {week}, platform {platform}")
        return jsonify(data)
    except Exception as e:
        logger.error(f"Error getting dimension trends: {e}")
        # 如果真实数据查询失败，返回mock数据作为备用
        logger.warning("Falling back to mock data for dimension trends")

        return jsonify({
            'dimension': dimension_type,
            'data': generate_dimension_data(dimension_type),
            'data_source': 'mock'
        })

@app.route('/api/trading/top10', methods=['GET'])
def get_top10_data():
    """获取Top10数据 - 使用真实数据"""
    category = request.args.get('category', '商品')
    brand = request.args.get('brand', '')  # 品牌参数
    platform = request.args.get('platform', '全平台')  # 平台参数

    try:
        # 使用真实数据查询
        data = get_top10_data_real(brand, platform)
        logger.info(f"Successfully retrieved top10 data for category {category}, brand {brand}, platform {platform}")

        # 根据category返回对应的数据
        if category == '商品':
            result_data = data['products']
        elif category == '门店' or category == '零售商':
            result_data = data['stores']
        elif category == '城市':
            result_data = data['cities']
        else:
            result_data = data['products']  # 默认返回商品数据

        return jsonify({
            'category': category,
            'data': result_data
        })
    except Exception as e:
        logger.error(f"Error getting top10 data: {e}")
        # 如果真实数据查询失败，返回mock数据作为备用
        logger.warning("Falling back to mock data")

        return jsonify({
            'category': category,
            'data': generate_top10_data(category),
            'data_source': 'mock'  # 标识这是mock数据
        })

# 活动数据相关API
@app.route('/api/activity/summary', methods=['GET'])
def get_activity_summary():
    """获取活动数据概览"""
    week_param = request.args.get('week', '28')
    brand = request.args.get('brand', '')
    platform = request.args.get('platform', '全平台')

    # 解析周数参数，支持 "2025-W29" 或 "29" 格式
    if isinstance(week_param, str) and '-W' in week_param:
        week = int(week_param.split('-W')[1])
    else:
        week = int(week_param)

    try:
        data = get_activity_summary_real(week, brand, platform)
        logger.info(f"Successfully retrieved activity summary for week {week}, brand '{brand}', platform '{platform}'")
        return jsonify(data)
    except Exception as e:
        logger.error(f"Error getting activity summary: {e}")
        return jsonify({
            'activity_gmv': {'value': 0, 'yoy': 0},
            'activity_gmv_ratio': {'value': 0, 'yoy': 0},
            'verification_amount': {'value': 0, 'yoy': 0},
            'activity_cost_ratio': {'value': 0, 'yoy': 0},
            'total_cost_ratio': {'value': 0, 'yoy': 0}
        }), 500

@app.route('/api/activity/trends', methods=['GET'])
def get_activity_trends():
    """获取活动趋势数据"""
    brand = request.args.get('brand', '')
    end_week = request.args.get('end_week', type=int)
    platform = request.args.get('platform', '全平台')

    try:
        data = get_activity_trends_real(brand, end_week, platform)
        logger.info(f"Successfully retrieved activity trends for brand '{brand}', end_week {end_week}, platform '{platform}'")
        return jsonify(data)
    except Exception as e:
        logger.error(f"Error getting activity trends: {e}")
        return jsonify({'trend_data': []}), 500

@app.route('/api/activity/charts', methods=['GET'])
def get_activity_charts():
    """获取活动图表数据"""
    brand = request.args.get('brand', '')
    platform = request.args.get('platform', '全平台')

    try:
        # 复用趋势数据作为图表数据
        data = get_activity_trends_real(brand, None, platform)
        logger.info(f"Successfully retrieved activity charts for brand '{brand}', platform '{platform}'")
        return jsonify({'chart_data': data['trend_data']})
    except Exception as e:
        logger.error(f"Error getting activity charts: {e}")
        return jsonify({'chart_data': []}), 500

@app.route('/api/activity/platform-charts', methods=['GET'])
def get_activity_platform_charts():
    """获取活动数据分平台图表数据"""
    brand = request.args.get('brand', '')
    platform = request.args.get('platform', '全平台')

    try:
        data = get_activity_platform_charts_real(brand, platform)
        logger.info(f"Successfully retrieved activity platform charts for brand '{brand}', platform '{platform}'")
        return jsonify(data)
    except Exception as e:
        logger.error(f"Error getting activity platform charts: {e}")
        return jsonify({'platform_data': []}), 500

@app.route('/api/activity/detail', methods=['GET'])
def get_activity_detail():
    """获取活动详细数据"""
    dimension_type = request.args.get('type', '整体')  # 改为"整体"
    selected_week = request.args.get('selected_week', '2024-W47')  # 默认当前周
    brand = request.args.get('brand', '')
    platform = request.args.get('platform', '全平台')
    page = request.args.get('page', 1, type=int)
    page_size = request.args.get('page_size', 20, type=int)

    if dimension_type == '整体':
        # 使用真实数据计算最近8个完整周的数据
        try:
            data = get_activity_detail_real_weeks(brand, platform)
            return jsonify({
                'dimension_type': dimension_type,
                'data': data,
                'total': len(data),
                'page': 1,
                'page_size': len(data)
            })
        except Exception as e:
            logger.error(f"Error getting activity detail real weeks: {e}")
            # 返回空数据
            return jsonify({
                'dimension_type': dimension_type,
                'data': [],
                'total': 0,
                'page': 1,
                'page_size': 0
            }), 500
    else:
        # 按其他维度展示选定周的数据，支持分页
        try:
            data = get_activity_detail_real_dimension(dimension_type, selected_week, brand, page, page_size, platform)
            return jsonify(data)
        except Exception as e:
            logger.error(f"Error getting activity detail real dimension: {e}")
            # 返回空数据
            return jsonify({
                'dimension_type': dimension_type,
                'data': [],
                'total': 0,
                'page': page,
                'page_size': page_size
            }), 500

# RTB投放相关API
@app.route('/api/rtb/summary', methods=['GET'])
def get_rtb_summary():
    """获取RTB投放概览 - 使用真实数据"""
    week = request.args.get('week', 45)
    brand = request.args.get('brand', '')  # 品牌参数

    try:
        # 使用真实数据查询
        data = get_rtb_summary_real(week, brand)
        logger.info(f"Successfully retrieved RTB summary for week {week}, brand {brand}")
        return jsonify(data)
    except Exception as e:
        logger.error(f"Error getting RTB summary: {e}")
        # 如果真实数据查询失败，返回mock数据作为备用
        logger.warning("Falling back to mock data")
        return jsonify({
            'consumption': {
                'amount': round(random.uniform(80000, 150000), 2),
                'wow': round(random.uniform(-0.2, 0.3), 3),  # 周环比
                'progress': round(random.uniform(0.6, 0.95), 3)  # 消耗进度
            },
            't1_guided_amount': {
                'amount': round(random.uniform(800000, 1500000), 2),
                'wow': round(random.uniform(-0.15, 0.25), 3)  # 周环比
            },
            'roi': {
                'value': round(random.uniform(5.0, 12.0), 2),
                'wow': round(random.uniform(-0.3, 0.4), 3)  # 周环比
            }
        })

@app.route('/api/rtb/trends', methods=['GET'])
def get_rtb_trends():
    """获取RTB趋势数据 - 使用真实数据"""
    brand = request.args.get('brand', '')  # 品牌参数

    try:
        # 使用真实数据查询
        data = get_rtb_trends_real(brand)
        logger.info(f"Successfully retrieved RTB trends for brand {brand}")
        return jsonify(data)
    except Exception as e:
        logger.error(f"Error getting RTB trends: {e}")
        # 如果真实数据查询失败，返回mock数据作为备用
        logger.warning("Falling back to mock data")

        weeks = 8
        data = []

        for i in range(weeks):
            week_num = 52 - weeks + i + 1
            consumption = round(random.uniform(80000, 150000), 2)
            guided_amount = round(random.uniform(800000, 1500000), 2)
            roi = round(random.uniform(5.0, 12.0), 2)

            data.append({
                'week': week_num,
                'consumption': consumption,
                'consumption_wow': round(random.uniform(-0.2, 0.3), 3),
                't1_guided_amount': guided_amount,
                't1_guided_amount_wow': round(random.uniform(-0.15, 0.25), 3),
                'roi': roi,
                'roi_wow': round(random.uniform(-0.3, 0.4), 3)
            })

        return jsonify({'trend_data': data})

@app.route('/api/rtb/detail', methods=['GET'])
def get_rtb_detail():
    """获取RTB明细数据 - 使用真实数据"""
    dimension_type = request.args.get('type', '整体')
    selected_week = request.args.get('selected_week', 45)
    brand = request.args.get('brand', '')  # 品牌参数

    try:
        # 使用真实数据查询
        data = get_rtb_detail_real(dimension_type, int(selected_week), brand)
        logger.info(f"Successfully retrieved RTB detail for dimension {dimension_type}, week {selected_week}, brand {brand}")
        return jsonify(data)
    except Exception as e:
        logger.error(f"Error getting RTB detail: {e}")
        # 如果真实数据查询失败，返回mock数据作为备用
        logger.warning("Falling back to mock data")

        if dimension_type == '整体':
            # 按周次展示近8周数据
            data = []
            for i in range(8):
                week_num = 52 - 8 + i + 1
                impressions = random.randint(1000000, 5000000)
                clicks = random.randint(10000, 50000)
                orders = random.randint(500, 2000)

                data.append({
                    'dimension': f'第{week_num}周',
                    'exposure': impressions,
                    'click': clicks,
                    'ctr': round(clicks / impressions, 3),
                    'order_volume': orders,
                    'order_conversion_rate': round(orders / clicks, 3),
                    'guided_amount': round(random.uniform(800000, 1500000), 2),
                    'budget': round(random.uniform(120000, 180000), 2),
                    'consumption': round(random.uniform(80000, 150000), 2),
                    'consumption_progress': round(random.uniform(0.6, 0.95), 3),
                    'cpm': round(random.uniform(15, 35), 2),
                    'cpc': round(random.uniform(2, 8), 2),
                    'roi': round(random.uniform(5, 12), 2)
                })
        elif dimension_type == '计划':
            # 计划维度数据
            plan_names = [
                '新用户拉新计划', '老用户激活计划', '品牌推广计划', '促销活动计划', '节日营销计划',
                '商品推广计划', '精准投放计划', '广撒网计划', '高ROI计划', '测试计划'
            ]

            data = []
            for i, plan_name in enumerate(plan_names):
                impressions = random.randint(100000, 1000000)
                clicks = random.randint(1000, 10000)
                orders = random.randint(50, 500)

                data.append({
                    'dimension': plan_name,
                    'exposure': impressions,
                    'click': clicks,
                    'ctr': round(clicks / impressions, 3),
                    'order_volume': orders,
                    'order_conversion_rate': round(orders / clicks, 3),
                    'guided_amount': round(random.uniform(80000, 300000), 2),
                    'budget': round(random.uniform(15000, 50000), 2),
                    'consumption': round(random.uniform(8000, 35000), 2),
                    'consumption_progress': round(random.uniform(0.5, 1.0), 3),
                    'cpm': round(random.uniform(15, 35), 2),
                    'cpc': round(random.uniform(2, 8), 2),
                    'new_user_cost': round(random.uniform(50, 150), 2),
                    'roi': round(random.uniform(3, 10), 2)
                })
        else:  # 商品维度
            product_names = [
                '有机蔬菜礼盒', '进口水果拼盘', '精选牛肉套餐', '海鲜大礼包', '坚果零食组合',
                '婴幼儿奶粉', '进口红酒', '养生茶叶', '烘焙面包', '新鲜鸡蛋'
            ]

            data = []
            for i, product_name in enumerate(product_names):
                impressions = random.randint(50000, 500000)
                clicks = random.randint(500, 5000)
                orders = random.randint(20, 200)

                data.append({
                    'dimension': product_name,
                    'exposure': impressions,
                    'click': clicks,
                    'ctr': round(clicks / impressions, 3),
                    'order_volume': orders,
                    'order_conversion_rate': round(orders / clicks, 3),
                    'guided_amount': round(random.uniform(30000, 150000), 2),
                    'budget': round(random.uniform(8000, 25000), 2),
                    'consumption': round(random.uniform(3000, 20000), 2),
                    'consumption_progress': round(random.uniform(0.4, 1.0), 3),
                    'cpm': round(random.uniform(15, 35), 2),
                    'cpc': round(random.uniform(2, 8), 2),
                    'roi': round(random.uniform(2, 8), 2)
                })

        return jsonify({
            'dimension_type': dimension_type,
            'data': data
        })

# 供给表现相关API
@app.route('/api/supply/summary', methods=['GET'])
def get_supply_summary():
    """获取供给表现概览 - 使用真实数据"""
    week = request.args.get('week')
    brand = request.args.get('brand', '')  # 品牌参数

    try:
        # 使用真实数据查询
        from real_data_queries import get_supply_summary_real
        data = get_supply_summary_real(week, brand)
        logger.info(f"Successfully retrieved supply summary for week {week}, brand {brand}")
        return jsonify(data)
    except Exception as e:
        logger.error(f"Error getting supply summary: {e}")
        # 如果真实数据查询失败，返回mock数据作为备用
        logger.warning("Falling back to mock data")
        return jsonify({
            'gmv': {
                'value': random.uniform(8000000, 12000000),
                'wow': round(random.uniform(-0.1, 0.2), 3)
            },
            'store_count': {
                'value': random.randint(8000, 15000),
                'wow': round(random.uniform(-0.1, 0.2), 3)  # 周环比
            },
            'store_penetration': {
                'value': round(random.uniform(0.65, 0.85), 3),
                'wow': round(random.uniform(-0.05, 0.08), 3)  # pp
            },
            'store_activity_rate': {
                'value': round(random.uniform(0.75, 0.95), 3),
                'wow': round(random.uniform(-0.03, 0.05), 3)  # pp
            },
            'avg_sku_per_store': {
                'value': random.randint(200, 500),
                'wow': round(random.uniform(-0.15, 0.25), 3)  # 周环比
            },
            'sku_sold_out_rate': {
                'value': round(random.uniform(0.15, 0.35), 3),
                'wow': round(random.uniform(-0.08, 0.12), 3)  # pp
            },
            'data_source': 'mock'
        })

@app.route('/api/supply/trends', methods=['GET'])
def get_supply_trends():
    """获取供给趋势数据 - 使用真实数据"""
    brand = request.args.get('brand', '')  # 品牌参数

    try:
        # 使用真实数据查询
        from real_data_queries import get_supply_trends_real
        data = get_supply_trends_real(brand)
        logger.info(f"Successfully retrieved supply trends for brand {brand}")
        return jsonify(data)
    except Exception as e:
        logger.error(f"Error getting supply trends: {e}")
        # 如果真实数据查询失败，返回mock数据作为备用
        logger.warning("Falling back to mock data")

        weeks = 8
        data = []

        for i in range(weeks):
            week_num = 52 - weeks + i + 1
            store_count = random.randint(8000, 15000)
            store_penetration = round(random.uniform(0.65, 0.85), 3)
            store_activity_rate = round(random.uniform(0.75, 0.95), 3)
            avg_sku_per_store = random.randint(200, 500)
            sku_sold_out_rate = round(random.uniform(0.15, 0.35), 3)

            data.append({
                'week': week_num,
                'gmv': round(random.uniform(8000000, 12000000), 2),
                'gmv_wow': round(random.uniform(-0.1, 0.2), 3),
                'store_count': store_count,
                'store_count_wow': round(random.uniform(-0.1, 0.2), 3),
                'store_penetration': store_penetration,
                'store_penetration_wow': round(random.uniform(-0.05, 0.08), 3),
                'store_activity_rate': store_activity_rate,
                'store_activity_rate_wow': round(random.uniform(-0.03, 0.05), 3),
                'avg_sku_per_store': avg_sku_per_store,
                'avg_sku_per_store_wow': round(random.uniform(-0.15, 0.25), 3),
                'sku_sold_out_rate': sku_sold_out_rate,
                'sku_sold_out_rate_wow': round(random.uniform(-0.08, 0.12), 3)
            })

        return jsonify({'trend_data': data, 'data_source': 'mock'})

@app.route('/api/supply/detail', methods=['GET'])
def get_supply_detail():
    """获取供给明细数据 - 使用真实数据"""
    import urllib.parse
    # 处理中文编码问题
    dimension_type_raw = request.args.get('type', '整体')
    try:
        dimension_type = urllib.parse.unquote(dimension_type_raw, encoding='utf-8')
    except:
        dimension_type = dimension_type_raw

    brand = request.args.get('brand', '')  # 品牌参数
    selected_week = request.args.get('selected_week', type=int)  # 选定周参数
    logger.info(f"Supply detail request - dimension_type: {dimension_type}, brand: {brand}, selected_week: {selected_week}")

    try:
        # 使用真实数据查询
        from real_data_queries import get_supply_detail_real
        data = get_supply_detail_real(dimension_type, brand, selected_week)
        logger.info(f"Successfully retrieved supply detail for dimension {dimension_type}, brand {brand}, week {selected_week}")
        return jsonify(data)
    except Exception as e:
        logger.error(f"Error getting supply detail: {e}")
        # 如果真实数据查询失败，返回mock数据作为备用
        logger.warning("Falling back to mock data")

        def generate_weekly_data():
            data = []
            for i in range(8):
                week_num = 52 - 8 + i + 1
                gmv = round(random.uniform(5000000, 15000000), 2)
                store_count = random.randint(8000, 15000)
                store_penetration = round(random.uniform(0.65, 0.85), 3)
                store_activity_rate = round(random.uniform(0.75, 0.95), 3)
                avg_sku_per_store = random.randint(200, 500)
                sku_sold_out_rate = round(random.uniform(0.15, 0.35), 3)
                data.append({
                    'dimension': f'第{week_num}周',
                    'gmv': gmv,
                    'gmv_ratio': round(random.uniform(0.15, 0.35), 3),
                    'gmv_wow': round(random.uniform(-0.2, 0.3), 3),
                    'store_count': store_count,
                    'store_count_wow': round(random.uniform(-0.1, 0.2), 3),
                    'store_penetration': store_penetration,
                    'store_penetration_wow': round(random.uniform(-0.05, 0.08), 3),
                    'store_activity_rate': store_activity_rate,
                    'store_activity_rate_wow': round(random.uniform(-0.03, 0.05), 3),
                    'avg_sku_per_store': avg_sku_per_store,
                    'avg_sku_per_store_wow': round(random.uniform(-0.15, 0.25), 3),
                    'sku_sold_out_rate': sku_sold_out_rate
                })
            return data
    def generate_channel_data():
        channel_names = [
            '大型商超', '便利店', '社区店', '专卖店', '线上商城',
            '批发市场', '农贸市场', '连锁超市', '会员店', '折扣店'
        ]
        data = []
        for i, channel_name in enumerate(channel_names):
            gmv = round(random.uniform(500000, 3000000), 2)
            store_count = random.randint(500, 3000)
            store_penetration = round(random.uniform(0.6, 0.9), 3)
            store_activity_rate = round(random.uniform(0.7, 0.98), 3)
            avg_sku_per_store = random.randint(150, 600)
            sku_sold_out_rate = round(random.uniform(0.1, 0.4), 3)
            data.append({
                'dimension': channel_name,
                'gmv': gmv,
                'gmv_ratio': round(random.uniform(0.05, 0.25), 3),
                'gmv_wow': round(random.uniform(-0.2, 0.3), 3),
                'store_count': store_count,
                'store_count_wow': round(random.uniform(-0.1, 0.2), 3),
                'store_penetration': store_penetration,
                'store_penetration_wow': round(random.uniform(-0.05, 0.08), 3),
                'store_activity_rate': store_activity_rate,
                'store_activity_rate_wow': round(random.uniform(-0.03, 0.05), 3),
                'avg_sku_per_store': avg_sku_per_store,
                'avg_sku_per_store_wow': round(random.uniform(-0.15, 0.25), 3),
                'sku_sold_out_rate': sku_sold_out_rate
            })
        return data
    def generate_product_data():
        product_names = [
            '有机蔬菜', '进口水果', '新鲜肉类', '海鲜水产', '乳制品',
            '粮油调味', '休闲零食', '饮料酒水', '日用品', '生鲜果蔬'
        ]
        data = []
        for i, product_name in enumerate(product_names):
            gmv = round(random.uniform(300000, 2000000), 2)
            store_count = random.randint(300, 2000)
            store_penetration = round(random.uniform(0.5, 0.8), 3)
            store_activity_rate = round(random.uniform(0.6, 0.9), 3)
            avg_sku_per_store = random.randint(100, 400)
            sku_sold_out_rate = round(random.uniform(0.08, 0.3), 3)
            data.append({
                'dimension': product_name,
                'gmv': gmv,
                'gmv_ratio': round(random.uniform(0.03, 0.15), 3),
                'gmv_wow': round(random.uniform(-0.2, 0.3), 3),
                'store_count': store_count,
                'store_count_wow': round(random.uniform(-0.1, 0.2), 3),
                'store_penetration': store_penetration,
                'store_penetration_wow': round(random.uniform(-0.05, 0.08), 3),
                'store_activity_rate': store_activity_rate,
                'store_activity_rate_wow': round(random.uniform(-0.03, 0.05), 3),
                'avg_sku_per_store': avg_sku_per_store,
                'avg_sku_per_store_wow': round(random.uniform(-0.15, 0.25), 3),
                'sku_sold_out_rate': sku_sold_out_rate
            })
        return data

    # 兼容乱码和多种写法
    if ('整体' in dimension_type) or ('æ' in dimension_type):
        data = generate_weekly_data()
    elif ('重点渠道' in dimension_type) or ('é' in dimension_type):
        data = generate_channel_data()
    else:
        data = generate_product_data()
    return jsonify({
        'dimension_type': dimension_type,
        'data': data
    })

@app.route('/api/user/summary', methods=['GET'])
def get_user_summary():
    """获取用户数据概览（新老客数量、销售额、客单价等）- 真实数据"""
    try:
        week = int(request.args.get('week', 45))
        brand = request.args.get('brand', '')

        # 获取真实用户数据
        user_data = get_user_data_summary_real(week, brand)

        # 计算总数用于占比计算
        total_user_count = user_data['new_user']['count'] + user_data['old_user']['count']
        total_gmv = user_data['new_user']['gmv'] + user_data['old_user']['gmv']
        total_avg_price = user_data['new_user']['avg_price'] + user_data['old_user']['avg_price']

        return jsonify({
            'new_user_count': {
                'value': user_data['new_user']['count'],
                'wow': user_data['new_user']['count_wow'],
                'ratio': user_data['new_user']['count'] / total_user_count if total_user_count > 0 else 0,
                'progress': round(random.uniform(0.6, 0.95), 3)  # 保留进度条的mock数据
            },
            'old_user_count': {
                'value': user_data['old_user']['count'],
                'wow': user_data['old_user']['count_wow'],
                'ratio': user_data['old_user']['count'] / total_user_count if total_user_count > 0 else 0,
                'progress': round(random.uniform(0.6, 0.95), 3)  # 保留进度条的mock数据
            },
            'new_user_gmv': {
                'value': user_data['new_user']['gmv'],
                'wow': user_data['new_user']['gmv_wow'],
                'ratio': user_data['new_user']['gmv'] / total_gmv if total_gmv > 0 else 0,
                'progress': round(random.uniform(0.6, 0.95), 3)  # 保留进度条的mock数据
            },
            'old_user_gmv': {
                'value': user_data['old_user']['gmv'],
                'wow': user_data['old_user']['gmv_wow'],
                'ratio': user_data['old_user']['gmv'] / total_gmv if total_gmv > 0 else 0,
                'progress': round(random.uniform(0.6, 0.95), 3)  # 保留进度条的mock数据
            },
            'new_user_arpu': {
                'value': user_data['new_user']['avg_price'],
                'wow': user_data['new_user']['avg_price_wow'],
                'ratio': user_data['new_user']['avg_price'] / total_avg_price if total_avg_price > 0 else 0,
                'progress': round(random.uniform(0.6, 0.95), 3)  # 保留进度条的mock数据
            },
            'old_user_arpu': {
                'value': user_data['old_user']['avg_price'],
                'wow': user_data['old_user']['avg_price_wow'],
                'ratio': user_data['old_user']['avg_price'] / total_avg_price if total_avg_price > 0 else 0,
                'progress': round(random.uniform(0.6, 0.95), 3)  # 保留进度条的mock数据
            }
        })

    except Exception as e:
        logger.error(f"Error in get_user_summary: {e}")
        # 返回mock数据作为fallback
        new_user_count = random.randint(20000, 40000)
        old_user_count = random.randint(50000, 90000)
        total_user_count = new_user_count + old_user_count
        new_user_gmv = round(random.uniform(2000000, 4000000), 2)
        old_user_gmv = round(random.uniform(6000000, 12000000), 2)
        total_gmv = new_user_gmv + old_user_gmv
        new_user_arpu = round(new_user_gmv / new_user_count, 2)
        old_user_arpu = round(old_user_gmv / old_user_count, 2)

        return jsonify({
            'new_user_count': {
                'value': new_user_count,
                'wow': round(random.uniform(-0.15, 0.25), 3),
                'ratio': round(new_user_count / total_user_count, 3),
                'progress': round(random.uniform(0.6, 0.95), 3)
            },
            'old_user_count': {
                'value': old_user_count,
                'wow': round(random.uniform(-0.1, 0.2), 3),
                'ratio': round(old_user_count / total_user_count, 3),
                'progress': round(random.uniform(0.6, 0.95), 3)
            },
            'new_user_gmv': {
                'value': new_user_gmv,
                'wow': round(random.uniform(-0.2, 0.3), 3),
                'ratio': round(new_user_gmv / total_gmv, 3),
                'progress': round(random.uniform(0.6, 0.95), 3)
            },
            'old_user_gmv': {
                'value': old_user_gmv,
                'wow': round(random.uniform(-0.15, 0.25), 3),
                'ratio': round(old_user_gmv / total_gmv, 3),
                'progress': round(random.uniform(0.6, 0.95), 3)
            },
            'new_user_arpu': {
                'value': new_user_arpu,
                'wow': round(random.uniform(-0.1, 0.2), 3),
                'ratio': round(new_user_arpu / (new_user_arpu + old_user_arpu), 3),
                'progress': round(random.uniform(0.6, 0.95), 3)
            },
            'old_user_arpu': {
                'value': old_user_arpu,
                'wow': round(random.uniform(-0.08, 0.15), 3),
                'ratio': round(old_user_arpu / (new_user_arpu + old_user_arpu), 3),
                'progress': round(random.uniform(0.6, 0.95), 3)
            }
        })

@app.route('/api/user/trends', methods=['GET'])
def get_user_trends():
    """获取用户数据趋势（近8周新老客分布和销售趋势）- 真实数据"""
    try:
        brand = request.args.get('brand', '')

        # 获取真实用户趋势数据
        user_trends = get_user_data_trends_real(brand)

        # 转换数据格式以匹配前端期望的格式
        distribution_data = []
        sales_data = []

        for week_data in user_trends['trend_data']:
            week = week_data['week']
            new_user = week_data['new_user']
            old_user = week_data['old_user']

            # 分布数据
            distribution_data.append({
                'week': week,
                'new_user_count': new_user['usernum'],
                'old_user_count': old_user['usernum'],
                'total_user_count': new_user['usernum'] + old_user['usernum']
            })

            # 销售数据
            sales_data.append({
                'week': week,
                'new_user_gmv': new_user['gmv'],
                'old_user_gmv': old_user['gmv'],
                'total_gmv': new_user['gmv'] + old_user['gmv'],
                'new_user_arpu': new_user['avg_price'],
                'old_user_arpu': old_user['avg_price']
            })

        return jsonify({
            'distribution': distribution_data,
            'sales': sales_data
        })

    except Exception as e:
        logger.error(f"Error in get_user_trends: {e}")
        # 返回mock数据作为fallback
        weeks = list(range(38, 46))  # 第38-45周

        # Mock数据：新老客分布趋势
        distribution_data = []
        for week in weeks:
            new_user_count = random.randint(18000, 42000)
            old_user_count = random.randint(45000, 95000)
            distribution_data.append({
                'week': week,
                'new_user_count': new_user_count,
                'old_user_count': old_user_count,
                'total_user_count': new_user_count + old_user_count
            })

        # Mock数据：销售趋势
        sales_data = []
        for week in weeks:
            new_user_gmv = round(random.uniform(1800000, 4500000), 2)
            old_user_gmv = round(random.uniform(5500000, 13000000), 2)
            new_user_count = random.randint(18000, 42000)
            old_user_count = random.randint(45000, 95000)
            new_user_arpu = round(new_user_gmv / new_user_count, 2) if new_user_count > 0 else 0
            old_user_arpu = round(old_user_gmv / old_user_count, 2) if old_user_count > 0 else 0

            sales_data.append({
                'week': week,
                'new_user_gmv': new_user_gmv,
                'old_user_gmv': old_user_gmv,
                'total_gmv': new_user_gmv + old_user_gmv,
                'new_user_arpu': new_user_arpu,
                'old_user_arpu': old_user_arpu
            })

        return jsonify({
            'distribution': distribution_data,
            'sales': sales_data
        })

@app.route('/api/user/detail', methods=['GET'])
def get_user_detail():
    """获取用户数据明细（近8周新老客各项明细）- 真实数据"""
    try:
        brand = request.args.get('brand', '')

        # 获取真实用户明细数据
        detail_data = get_user_data_detail_real(brand)

        return jsonify({
            'data': detail_data,
            'columns': [
                { 'title': '周次', 'dataIndex': 'week', 'key': 'week', 'width': 80 },
                { 'title': '新客数量', 'dataIndex': 'new_user_count', 'key': 'new_user_count', 'width': 100 },
                { 'title': '新客占比', 'dataIndex': 'new_user_ratio', 'key': 'new_user_ratio', 'width': 100 },
                { 'title': '新客年同比', 'dataIndex': 'new_user_yoy', 'key': 'new_user_yoy', 'width': 100 },
                { 'title': '新客周环比', 'dataIndex': 'new_user_wow', 'key': 'new_user_wow', 'width': 100 },
                { 'title': '新客销售额', 'dataIndex': 'new_user_gmv', 'key': 'new_user_gmv', 'width': 120 },
                { 'title': '新客销售额占比', 'dataIndex': 'new_user_gmv_ratio', 'key': 'new_user_gmv_ratio', 'width': 130 },
                { 'title': '新客销售额年同比', 'dataIndex': 'new_user_gmv_yoy', 'key': 'new_user_gmv_yoy', 'width': 140 },
                { 'title': '新客销售额周环比', 'dataIndex': 'new_user_gmv_wow', 'key': 'new_user_gmv_wow', 'width': 140 },
                { 'title': '新客客单价', 'dataIndex': 'new_user_arpu', 'key': 'new_user_arpu', 'width': 120 },
                { 'title': '新客客单价年同比', 'dataIndex': 'new_user_arpu_yoy', 'key': 'new_user_arpu_yoy', 'width': 140 },
                { 'title': '新客客单价周环比', 'dataIndex': 'new_user_arpu_wow', 'key': 'new_user_arpu_wow', 'width': 140 },
                { 'title': '老客数量', 'dataIndex': 'old_user_count', 'key': 'old_user_count', 'width': 100 },
                { 'title': '老客占比', 'dataIndex': 'old_user_ratio', 'key': 'old_user_ratio', 'width': 100 },
                { 'title': '老客年同比', 'dataIndex': 'old_user_yoy', 'key': 'old_user_yoy', 'width': 100 },
                { 'title': '老客周环比', 'dataIndex': 'old_user_wow', 'key': 'old_user_wow', 'width': 100 },
                { 'title': '老客销售额', 'dataIndex': 'old_user_gmv', 'key': 'old_user_gmv', 'width': 120 },
                { 'title': '老客销售额占比', 'dataIndex': 'old_user_gmv_ratio', 'key': 'old_user_gmv_ratio', 'width': 130 },
                { 'title': '老客销售额年同比', 'dataIndex': 'old_user_gmv_yoy', 'key': 'old_user_gmv_yoy', 'width': 140 },
                { 'title': '老客销售额周环比', 'dataIndex': 'old_user_gmv_wow', 'key': 'old_user_gmv_wow', 'width': 140 },
                { 'title': '老客客单价', 'dataIndex': 'old_user_arpu', 'key': 'old_user_arpu', 'width': 120 },
                { 'title': '老客客单价年同比', 'dataIndex': 'old_user_arpu_yoy', 'key': 'old_user_arpu_yoy', 'width': 140 },
                { 'title': '老客客单价周环比', 'dataIndex': 'old_user_arpu_wow', 'key': 'old_user_arpu_wow', 'width': 140 },
                { 'title': '总用户数', 'dataIndex': 'total_user_count', 'key': 'total_user_count', 'width': 100 },
                { 'title': '总销售额', 'dataIndex': 'total_gmv', 'key': 'total_gmv', 'width': 120 }
            ]
        })

    except Exception as e:
        logger.error(f"Error in get_user_detail: {e}")
        # 返回mock数据作为fallback
        weeks = list(range(38, 46))  # 第38-45周

        detail_data = []
        for week in weeks:
            # Mock数据：新客数据
            new_user_count = random.randint(18000, 42000)
            new_user_gmv = round(random.uniform(1800000, 4500000), 2)
            new_user_arpu = round(new_user_gmv / new_user_count, 2) if new_user_count > 0 else 0

            # Mock数据：老客数据
            old_user_count = random.randint(45000, 95000)
            old_user_gmv = round(random.uniform(5500000, 13000000), 2)
            old_user_arpu = round(old_user_gmv / old_user_count, 2) if old_user_count > 0 else 0

            # 计算总计
            total_user_count = new_user_count + old_user_count
            total_gmv = new_user_gmv + old_user_gmv

            # 计算占比
            new_user_ratio = round(new_user_count / total_user_count, 3) if total_user_count > 0 else 0
            old_user_ratio = round(old_user_count / total_user_count, 3) if total_user_count > 0 else 0
            new_user_gmv_ratio = round(new_user_gmv / total_gmv, 3) if total_gmv > 0 else 0
            old_user_gmv_ratio = round(old_user_gmv / total_gmv, 3) if total_gmv > 0 else 0

            # Mock环比数据
            new_user_yoy = round(random.uniform(-0.2, 0.3), 3)
            new_user_wow = round(random.uniform(-0.2, 0.3), 3)
            old_user_yoy = round(random.uniform(-0.15, 0.25), 3)
            old_user_wow = round(random.uniform(-0.15, 0.25), 3)
            new_user_gmv_yoy = round(random.uniform(-0.25, 0.35), 3)
            new_user_gmv_wow = round(random.uniform(-0.25, 0.35), 3)
            old_user_gmv_yoy = round(random.uniform(-0.2, 0.3), 3)
            old_user_gmv_wow = round(random.uniform(-0.2, 0.3), 3)
            new_user_arpu_yoy = round(random.uniform(-0.1, 0.2), 3)
            new_user_arpu_wow = round(random.uniform(-0.1, 0.2), 3)
            old_user_arpu_yoy = round(random.uniform(-0.1, 0.2), 3)
            old_user_arpu_wow = round(random.uniform(-0.1, 0.2), 3)

            detail_data.append({
                'week': week,
                'new_user_count': new_user_count,
                'new_user_ratio': new_user_ratio,
                'new_user_yoy': new_user_yoy,
                'new_user_wow': new_user_wow,
                'new_user_gmv': new_user_gmv,
                'new_user_gmv_ratio': new_user_gmv_ratio,
                'new_user_gmv_yoy': new_user_gmv_yoy,
                'new_user_gmv_wow': new_user_gmv_wow,
                'new_user_arpu': new_user_arpu,
                'new_user_arpu_yoy': new_user_arpu_yoy,
                'new_user_arpu_wow': new_user_arpu_wow,
                'old_user_count': old_user_count,
                'old_user_ratio': old_user_ratio,
                'old_user_yoy': old_user_yoy,
                'old_user_wow': old_user_wow,
                'old_user_gmv': old_user_gmv,
                'old_user_gmv_ratio': old_user_gmv_ratio,
                'old_user_gmv_yoy': old_user_gmv_yoy,
                'old_user_gmv_wow': old_user_gmv_wow,
                'old_user_arpu': old_user_arpu,
                'old_user_arpu_yoy': old_user_arpu_yoy,
                'old_user_arpu_wow': old_user_arpu_wow,
                'total_user_count': total_user_count,
                'total_gmv': total_gmv
            })

        return jsonify({
            'data': detail_data,
            'columns': [
                { 'title': '周次', 'dataIndex': 'week', 'key': 'week', 'width': 80 },
                { 'title': '新客数量', 'dataIndex': 'new_user_count', 'key': 'new_user_count', 'width': 100 },
                { 'title': '新客占比', 'dataIndex': 'new_user_ratio', 'key': 'new_user_ratio', 'width': 100 },
                { 'title': '新客年同比', 'dataIndex': 'new_user_yoy', 'key': 'new_user_yoy', 'width': 100 },
                { 'title': '新客周环比', 'dataIndex': 'new_user_wow', 'key': 'new_user_wow', 'width': 100 },
                { 'title': '新客销售额', 'dataIndex': 'new_user_gmv', 'key': 'new_user_gmv', 'width': 120 },
                { 'title': '新客销售额占比', 'dataIndex': 'new_user_gmv_ratio', 'key': 'new_user_gmv_ratio', 'width': 130 },
                { 'title': '新客销售额年同比', 'dataIndex': 'new_user_gmv_yoy', 'key': 'new_user_gmv_yoy', 'width': 140 },
                { 'title': '新客销售额周环比', 'dataIndex': 'new_user_gmv_wow', 'key': 'new_user_gmv_wow', 'width': 140 },
                { 'title': '新客客单价', 'dataIndex': 'new_user_arpu', 'key': 'new_user_arpu', 'width': 120 },
                { 'title': '新客客单价年同比', 'dataIndex': 'new_user_arpu_yoy', 'key': 'new_user_arpu_yoy', 'width': 140 },
                { 'title': '新客客单价周环比', 'dataIndex': 'new_user_arpu_wow', 'key': 'new_user_arpu_wow', 'width': 140 },
                { 'title': '老客数量', 'dataIndex': 'old_user_count', 'key': 'old_user_count', 'width': 100 },
                { 'title': '老客占比', 'dataIndex': 'old_user_ratio', 'key': 'old_user_ratio', 'width': 100 },
                { 'title': '老客年同比', 'dataIndex': 'old_user_yoy', 'key': 'old_user_yoy', 'width': 100 },
                { 'title': '老客周环比', 'dataIndex': 'old_user_wow', 'key': 'old_user_wow', 'width': 100 },
                { 'title': '老客销售额', 'dataIndex': 'old_user_gmv', 'key': 'old_user_gmv', 'width': 120 },
                { 'title': '老客销售额占比', 'dataIndex': 'old_user_gmv_ratio', 'key': 'old_user_gmv_ratio', 'width': 130 },
                { 'title': '老客销售额年同比', 'dataIndex': 'old_user_gmv_yoy', 'key': 'old_user_gmv_yoy', 'width': 140 },
                { 'title': '老客销售额周环比', 'dataIndex': 'old_user_gmv_wow', 'key': 'old_user_gmv_wow', 'width': 140 },
                { 'title': '老客客单价', 'dataIndex': 'old_user_arpu', 'key': 'old_user_arpu', 'width': 120 },
                { 'title': '老客客单价年同比', 'dataIndex': 'old_user_arpu_yoy', 'key': 'old_user_arpu_yoy', 'width': 140 },
                { 'title': '老客客单价周环比', 'dataIndex': 'old_user_arpu_wow', 'key': 'old_user_arpu_wow', 'width': 140 },
                { 'title': '总用户数', 'dataIndex': 'total_user_count', 'key': 'total_user_count', 'width': 100 },
                { 'title': '总销售额', 'dataIndex': 'total_gmv', 'key': 'total_gmv', 'width': 120 }
            ]
        })

@app.route('/api/user/platform-charts', methods=['GET'])
def get_user_platform_charts():
    """获取用户数据分平台图表数据 - 真实数据"""
    try:
        brand = request.args.get('brand', '')

        # 获取真实用户分平台数据
        platform_data = get_user_platform_charts_real(brand)

        logger.info(f"Successfully retrieved user platform charts for brand '{brand}'")
        return jsonify(platform_data)

    except Exception as e:
        logger.error(f"Error getting user platform charts: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({'status': 'ok', 'timestamp': datetime.now().isoformat()})

@app.route('/api/debug/brands', methods=['GET'])
def debug_brands():
    """调试API：查看数据库中的品牌数据"""
    try:
        # 查询所有品牌
        brands_sql = """
        SELECT DISTINCT brand, COUNT(*) as count
        FROM dws_o2o_sales_d
        WHERE brand IS NOT NULL AND brand != ''
        GROUP BY brand
        ORDER BY count DESC
        LIMIT 20
        """

        from database import execute_query
        brands_result = execute_query(brands_sql)

        # 查询圣农品牌的具体数据
        shengnong_sql = """
        SELECT brand, sub_brand, COUNT(*) as count, SUM(gmv) as total_gmv
        FROM dws_o2o_sales_d
        WHERE brand = '圣农'
        GROUP BY brand, sub_brand
        ORDER BY total_gmv DESC
        """

        shengnong_result = execute_query(shengnong_sql)

        return jsonify({
            'all_brands': brands_result,
            'shengnong_data': shengnong_result
        })

    except Exception as e:
        logger.error(f"Debug brands error: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/debug/areas', methods=['GET'])
def debug_areas():
    """调试大区数据"""
    try:
        brand = request.args.get('brand', '')

        # 构建品牌过滤条件
        brand_filter = ""
        if brand:
            brand_filter = f"AND brand = '{brand}'"

        # 查询所有大区数据
        areas_sql = f"""
        SELECT area, COUNT(*) as count, SUM(gmv) as total_gmv
        FROM dws_o2o_sales_d
        WHERE area IS NOT NULL AND area != ''
        {brand_filter}
        GROUP BY area
        ORDER BY total_gmv DESC
        """

        from database import execute_query
        areas_data = execute_query(areas_sql)

        # 查询第28周的大区数据
        from real_data_queries import get_week_date_range
        week_start, week_end = get_week_date_range(2025, 28)

        week28_areas_sql = f"""
        SELECT area, SUM(gmv) as total_gmv
        FROM dws_o2o_sales_d
        WHERE area IS NOT NULL AND area != ''
        AND ds BETWEEN '{week_start}' AND '{week_end}'
        {brand_filter}
        GROUP BY area
        ORDER BY total_gmv DESC
        """

        week28_areas = execute_query(week28_areas_sql)

        return jsonify({
            'all_areas': areas_data,
            'week28_areas': week28_areas,
            'brand_filter': brand,
            'week_range': f"{week_start} to {week_end}"
        })

    except Exception as e:
        logger.error(f"Error in debug areas: {str(e)}")
        return jsonify({'error': str(e)}), 500

# Excel导出进度存储
export_progress_store = {}

# Excel导出相关API
@app.route('/api/export/excel', methods=['POST'])
def export_excel():
    """导出Excel文件 - 支持进度跟踪"""
    try:
        data = request.get_json()
        page_name = data.get('page_name')  # 页面名称：交易表现、用户表现、活动表现、RTB表现、供给表现
        week = data.get('week')
        brand = data.get('brand', '')
        ai_insights = data.get('ai_insights', '')  # AI洞察内容
        chart_images = data.get('chart_images', {})  # 图表截图数据
        use_progress = data.get('use_progress', False)  # 是否使用进度跟踪

        logger.info(f"开始导出Excel - 页面: {page_name}, 周次: {week}, 品牌: {brand}, AI洞察: {'有' if ai_insights else '无'}, 图表: {'有' if chart_images else '无'}, 进度跟踪: {use_progress}")

        # 验证参数
        if not page_name or not week:
            return jsonify({
                'success': False,
                'error': '缺少必要参数：page_name 和 week'
            }), 400

        # 支持的页面列表
        supported_pages = ['交易表现', '用户表现', '活动表现', 'RTB表现', '供给表现']
        if page_name not in supported_pages:
            return jsonify({
                'success': False,
                'error': f'不支持的页面类型: {page_name}，支持的页面: {supported_pages}'
            }), 400

        # 如果使用进度跟踪，启动后台任务
        if use_progress:
            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 初始化进度
            export_progress_store[task_id] = {
                'progress': 0,
                'status': 'starting',
                'message': f'正在准备导出{page_name}数据...',
                'result': None,
                'error': None
            }

            # 启动后台任务
            thread = threading.Thread(
                target=export_single_excel_background,
                args=(task_id, page_name, week, brand, ai_insights, chart_images)
            )
            thread.daemon = True
            thread.start()

            return jsonify({
                'success': True,
                'task_id': task_id,
                'message': f'{page_name}导出任务已启动'
            })
        else:
            # 直接导出（原有逻辑）
            from excel_export import export_page_data

            # 导出数据，传递AI洞察内容和图表截图
            filepath, filename = export_page_data(page_name, week, brand, ai_insights, chart_images)

            # 返回文件
            return send_file(
                filepath,
                as_attachment=True,
                download_name=filename,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )

    except Exception as e:
        logger.error(f"Excel导出失败: {e}")
        return jsonify({
            'success': False,
            'error': f'Excel导出失败: {str(e)}'
        }), 500

@app.route('/api/export/excel/all', methods=['POST'])
def export_all_excel():
    """启动批量导出任务"""
    try:
        data = request.get_json()
        week = data.get('week')
        brand = data.get('brand', '')
        chart_images = data.get('chart_images', {})  # 接收图表截图数据

        logger.info(f"开始导出所有页面Excel - 周次: {week}, 品牌: {brand}, 图表: {'有' if chart_images else '无'}")

        if not week:
            return jsonify({
                'success': False,
                'error': '缺少必要参数：week'
            }), 400

        # 生成任务ID
        task_id = str(uuid.uuid4())

        # 初始化进度
        export_progress_store[task_id] = {
            'progress': 0,
            'status': 'starting',
            'message': '正在初始化批量导出...',
            'result': None,
            'error': None
        }

        # 启动后台任务
        thread = threading.Thread(
            target=export_all_excel_background,
            args=(task_id, week, brand, chart_images)
        )
        thread.daemon = True
        thread.start()

        return jsonify({
            'success': True,
            'task_id': task_id,
            'message': '批量导出任务已启动'
        })

    except Exception as e:
        logger.error(f"启动批量Excel导出失败: {e}")
        return jsonify({
            'success': False,
            'error': f'启动批量Excel导出失败: {str(e)}'
        }), 500

@app.route('/api/export/excel/progress/<task_id>', methods=['GET'])
def get_export_progress(task_id):
    """获取导出进度"""
    if task_id not in export_progress_store:
        return jsonify({
            'success': False,
            'error': '任务不存在'
        }), 404

    progress_info = export_progress_store[task_id]

    return jsonify({
        'success': True,
        'progress': progress_info['progress'],
        'status': progress_info['status'],
        'message': progress_info['message'],
        'result': progress_info.get('result'),
        'error': progress_info.get('error')
    })

@app.route('/api/export/excel/download/<task_id>', methods=['GET'])
def download_export_result(task_id):
    """下载导出结果"""
    if task_id not in export_progress_store:
        return jsonify({
            'success': False,
            'error': '任务不存在'
        }), 404

    progress_info = export_progress_store[task_id]

    if progress_info['status'] != 'completed' or not progress_info.get('result'):
        return jsonify({
            'success': False,
            'error': '任务未完成或无结果文件'
        }), 400

    result = progress_info['result']
    filepath = result['filepath']
    filename = result['filename']

    if not os.path.exists(filepath):
        return jsonify({
            'success': False,
            'error': '结果文件不存在'
        }), 404

    # 根据文件扩展名确定MIME类型
    if filename.endswith('.zip'):
        mimetype = 'application/zip'
    else:
        mimetype = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

    return send_file(
        filepath,
        as_attachment=True,
        download_name=filename,
        mimetype=mimetype
    )

def export_all_excel_background(task_id, week, brand, chart_images=None):
    """后台批量导出任务"""
    try:
        # 导入必要模块
        from excel_export import export_page_data
        import zipfile

        # 所有页面
        pages = ['交易表现', '用户表现', '活动表现', 'RTB表现', '供给表现']
        total_pages = len(pages)

        logger.info(f"批量导出任务开始 - 任务ID: {task_id}, 周次: {week}, 品牌: {brand}, 图表: {'有' if chart_images else '无'}")

        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        zip_filename = f"WeeklyReport_Week{week}.zip"
        zip_filepath = os.path.join(temp_dir, zip_filename)
        display_filename = f"周报数据第{week}周.zip"

        # 更新进度：开始导出
        export_progress_store[task_id].update({
            'progress': 10,
            'status': 'exporting',
            'message': '正在导出页面数据...'
        })

        exported_files = []

        with zipfile.ZipFile(zip_filepath, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for i, page_name in enumerate(pages):
                try:
                    # 更新进度
                    progress = 10 + (i * 70 // total_pages)
                    export_progress_store[task_id].update({
                        'progress': progress,
                        'message': f'正在导出{page_name}数据...'
                    })

                    # 导出单个页面，传递对应的图表数据
                    page_chart_images = chart_images if page_name == '交易表现' else {}
                    filepath, display_filename = export_page_data(page_name, week, brand, '', page_chart_images)

                    # 为压缩包生成安全的文件名（避免斜杠被当作目录分隔符）
                    safe_zip_filename = display_filename.replace('/', '-').replace('\\', '-')

                    # 添加到zip文件
                    zipf.write(filepath, safe_zip_filename)
                    exported_files.append(safe_zip_filename)

                    # 删除临时文件
                    os.remove(filepath)

                    logger.info(f"已添加{page_name}到压缩包")

                except Exception as e:
                    logger.error(f"导出{page_name}失败: {e}")
                    continue

        # 更新进度：完成
        export_progress_store[task_id].update({
            'progress': 100,
            'status': 'completed',
            'message': f'批量导出完成，共导出{len(exported_files)}个文件',
            'result': {
                'filepath': zip_filepath,
                'filename': display_filename,
                'exported_files': exported_files
            }
        })

        logger.info(f"批量导出完成: {zip_filepath}")

    except Exception as e:
        logger.error(f"批量导出后台任务失败: {e}")
        export_progress_store[task_id].update({
            'progress': 0,
            'status': 'error',
            'message': f'批量导出失败: {str(e)}',
            'error': str(e)
        })

def export_single_excel_background(task_id, page_name, week, brand, ai_insights='', chart_images=None):
    """后台单页面导出任务"""
    try:
        # 导入必要模块
        from excel_export import export_page_data

        # 更新进度：开始导出
        export_progress_store[task_id].update({
            'progress': 20,
            'status': 'exporting',
            'message': f'正在收集{page_name}数据...'
        })

        # 更新进度：数据收集中
        export_progress_store[task_id].update({
            'progress': 50,
            'status': 'exporting',
            'message': f'正在生成{page_name}Excel文件...'
        })

        # 导出数据
        filepath, filename = export_page_data(page_name, week, brand, ai_insights, chart_images)

        # 更新进度：完成
        export_progress_store[task_id].update({
            'progress': 100,
            'status': 'completed',
            'message': f'{page_name}导出完成',
            'result': {
                'filepath': filepath,
                'filename': filename
            }
        })

        logger.info(f"单页面导出完成: {filepath}")

    except Exception as e:
        logger.error(f"单页面导出后台任务失败: {e}")
        export_progress_store[task_id].update({
            'progress': 0,
            'status': 'error',
            'message': f'{page_name}导出失败: {str(e)}',
            'error': str(e)
        })

@app.route('/test-yoy-wow')
def test_yoy_wow():
    """测试年同比和周环比显示页面"""
    return send_file('test_yoy_wow_display.html')

@app.route('/test-shengnong-top10')
def test_shengnong_top10():
    """测试圣农品牌Top10数据页面"""
    return send_file('test_frontend_data.html')

# AI洞察相关API
import uuid
import threading
import time

# 全局变量存储进度信息
progress_store = {}

@app.route('/api/ai-insights', methods=['POST'])
def generate_ai_insights():
    """生成AI洞察分析"""
    try:
        data = request.get_json()
        selected_week = data.get('selectedWeek')
        brand = data.get('brand', '')
        selected_platform = data.get('selectedPlatform', '全平台')  # 获取选择的平台

        # 生成唯一的任务ID
        task_id = str(uuid.uuid4())

        # 初始化进度
        progress_store[task_id] = {
            'progress': 0,
            'status': 'starting',
            'message': '正在初始化...',
            'result': None,
            'error': None
        }

        logger.info(f"Starting AI insights generation for week {selected_week}, brand {brand}, task_id {task_id}")

        # 启动后台任务
        thread = threading.Thread(
            target=generate_insights_background,
            args=(task_id, selected_week, brand, selected_platform)
        )
        thread.daemon = True
        thread.start()

        return jsonify({
            'success': True,
            'task_id': task_id,
            'message': 'AI洞察生成任务已启动'
        })

    except Exception as e:
        logger.error(f"Error starting AI insights generation: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/ai-insights/progress/<task_id>', methods=['GET'])
def get_insights_progress(task_id):
    """获取AI洞察生成进度"""
    if task_id not in progress_store:
        return jsonify({
            'success': False,
            'error': '任务不存在'
        }), 404

    progress_info = progress_store[task_id]

    # 如果任务完成，清理进度信息（可选）
    if progress_info['progress'] >= 100 and progress_info.get('result'):
        # 保留结果一段时间后再清理
        pass

    return jsonify({
        'success': True,
        'progress': progress_info['progress'],
        'status': progress_info['status'],
        'message': progress_info['message'],
        'result': progress_info.get('result'),
        'error': progress_info.get('error')
    })

@app.route('/api/ai-insights/save', methods=['POST'])
def save_ai_insights():
    """保存编辑后的AI洞察"""
    try:
        data = request.get_json()
        insights_content = data.get('insights', '')
        selected_week = data.get('selectedWeek')
        brand = data.get('brand', '')

        # 这里可以将洞察内容保存到数据库或文件
        # 目前先返回成功状态
        logger.info(f"Saved AI insights for week {selected_week}, brand {brand}")

        return jsonify({
            'success': True,
            'message': '洞察内容已保存'
        })

    except Exception as e:
        logger.error(f"Error saving AI insights: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/ai-insights/debug-data', methods=['POST'])
def debug_ai_data_collection():
    """调试AI数据收集 - 返回原始收集的数据"""
    try:
        data = request.get_json()
        selected_week = data.get('selectedWeek')
        brand = data.get('brand', '')
        selected_platform = data.get('selectedPlatform', '全平台')

        logger.info(f"Debug data collection for week {selected_week}, brand {brand}, platform {selected_platform}")

        # 直接调用数据收集函数，不生成AI洞察
        collected_data = collect_all_module_data_with_progress_debug(selected_week, brand, selected_platform)

        return jsonify({
            'success': True,
            'data': collected_data,
            'collected_at': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error in debug data collection: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def generate_insights_background(task_id, selected_week, brand, selected_platform='全平台'):
    """后台生成AI洞察的任务函数"""
    try:
        # 步骤1: 开始数据收集 (10%)
        progress_store[task_id].update({
            'progress': 10,
            'status': 'collecting_data',
            'message': '正在收集业务数据...'
        })

        # 收集所有5个模块的数据
        insights_data = collect_all_module_data_with_progress(task_id, selected_week, brand, selected_platform)

        # 步骤6: 开始AI分析 (80%)
        progress_store[task_id].update({
            'progress': 80,
            'status': 'ai_analyzing',
            'message': '正在进行AI智能分析...'
        })

        # 调用AI生成洞察
        ai_insights = generate_insights_with_ai(insights_data, selected_week, brand)

        # 清理AI输出，去掉markdown标识和多余文本
        cleaned_insights = clean_ai_output(ai_insights)

        # 步骤7: 完成 (100%)
        progress_store[task_id].update({
            'progress': 100,
            'status': 'completed',
            'message': 'AI洞察生成完成',
            'result': {
                'insights': cleaned_insights,
                'data_collected_at': datetime.now().isoformat()
            }
        })

        logger.info(f"AI insights generation completed for task {task_id}")

    except Exception as e:
        logger.error(f"Error in background AI insights generation: {e}")
        progress_store[task_id].update({
            'progress': 0,
            'status': 'error',
            'message': f'生成失败: {str(e)}',
            'error': str(e)
        })

def clean_ai_output(ai_output):
    """清理AI输出，去掉markdown标识和多余文本"""
    if not ai_output:
        return ai_output

    # 去掉开头的markdown标识
    lines = ai_output.split('\n')
    cleaned_lines = []

    # 跳过开头的markdown标识行
    start_index = 0
    for i, line in enumerate(lines):
        if line.strip().lower() in ['```markdown', '```', 'markdown']:
            start_index = i + 1
            break

    # 从有效内容开始处理
    for i in range(start_index, len(lines)):
        line = lines[i].strip()

        # 跳过结尾的markdown标识和备注说明
        if line.lower() in ['```', '```markdown']:
            break
        if line.startswith('注：') or line.startswith('备注：') or line.startswith('说明：'):
            break
        if '以上分析' in line or '数据来源' in line or '免责声明' in line:
            break

        cleaned_lines.append(lines[i])

    return '\n'.join(cleaned_lines).strip()

def collect_all_module_data_with_progress(task_id, selected_week, brand, selected_platform='全平台'):
    """带进度更新的数据收集函数"""
    try:
        # 解析周次 - 处理不同的周次格式
        week_num = int(selected_week) if isinstance(selected_week, str) and selected_week.isdigit() else selected_week

        collected_data = {}

        # 步骤2: 收集交易数据 (20%)
        progress_store[task_id].update({
            'progress': 20,
            'status': 'collecting_trading',
            'message': '正在收集交易数据...'
        })
        try:
            trading_data = {}

            # 收集交易概览数据 - 使用选择的平台
            try:
                trading_summary = get_trading_summary_real(week_num, selected_platform, brand)
                trading_data['summary'] = trading_summary
                logger.info(f"Trading summary collected successfully for platform {selected_platform}, week {week_num}")
            except Exception as e:
                logger.error(f"Error collecting trading summary: {e}")
                trading_data['summary'] = {'error': str(e)}

            # 收集交易趋势数据 - 使用选择的平台
            try:
                trading_trends = get_trading_trends_real(selected_platform, brand)
                trading_data['trends'] = trading_trends
                logger.info(f"Trading trends collected successfully for platform {selected_platform}")
            except Exception as e:
                logger.error(f"Error collecting trading trends: {e}")
                trading_data['trends'] = {'error': str(e)}

            # 收集维度分析数据
            try:
                trading_dimensions = get_dimension_analysis_real('子品牌', brand)
                trading_data['dimensions'] = trading_dimensions
                logger.info(f"Trading dimensions collected successfully")
            except Exception as e:
                logger.error(f"Error collecting trading dimensions: {e}")
                trading_data['dimensions'] = {'error': str(e)}

            # 收集Top10数据
            try:
                trading_top10 = get_top10_data_real('商品', brand)
                trading_data['top10'] = trading_top10
                logger.info(f"Trading top10 collected successfully")
            except Exception as e:
                logger.error(f"Error collecting trading top10: {e}")
                trading_data['top10'] = {'error': str(e)}

            collected_data['trading'] = trading_data

        except Exception as e:
            logger.error(f"Error collecting trading data: {e}")
            collected_data['trading'] = {'error': str(e)}

        # 步骤3: 收集活动数据 (35%)
        progress_store[task_id].update({
            'progress': 35,
            'status': 'collecting_activity',
            'message': '正在收集活动数据...'
        })
        try:
            activity_data = {}

            # 收集活动概览数据 - 使用周数
            try:
                activity_summary = get_activity_summary_real(week_num, brand)
                activity_data['summary'] = activity_summary
                logger.info(f"Activity summary collected successfully for week {week_num}")
            except Exception as e:
                logger.error(f"Error collecting activity summary: {e}")
                activity_data['summary'] = {'error': str(e)}

            # 收集活动趋势数据 - 传递周数
            try:
                activity_trends = get_activity_trends_real(brand, week_num)
                activity_data['trends'] = activity_trends
                logger.info(f"Activity trends collected successfully")
            except Exception as e:
                logger.error(f"Error collecting activity trends: {e}")
                activity_data['trends'] = {'error': str(e)}

            # 收集活动平台图表数据
            try:
                activity_platform_charts = get_activity_platform_charts_real(brand)
                activity_data['platform_charts'] = activity_platform_charts
                logger.info(f"Activity platform charts collected successfully")
            except Exception as e:
                logger.error(f"Error collecting activity platform charts: {e}")
                activity_data['platform_charts'] = {'error': str(e)}

            # 收集活动明细数据
            try:
                activity_detail = get_activity_detail_real_weeks(brand)
                activity_data['detail'] = activity_detail
                logger.info(f"Activity detail collected successfully")
            except Exception as e:
                logger.error(f"Error collecting activity detail: {e}")
                activity_data['detail'] = {'error': str(e)}

            collected_data['activity'] = activity_data

        except Exception as e:
            logger.error(f"Error collecting activity data: {e}")
            collected_data['activity'] = {'error': str(e)}

        # 步骤4: 收集RTB和供给数据 (50%)
        progress_store[task_id].update({
            'progress': 50,
            'status': 'collecting_rtb_supply',
            'message': '正在收集RTB投放和供给数据...'
        })
        try:
            rtb_data = {}

            # 收集RTB概览数据
            try:
                rtb_summary = get_rtb_summary_real(week_num, brand)
                rtb_data['summary'] = rtb_summary
                logger.info(f"RTB summary collected successfully for week {week_num}")
            except Exception as e:
                logger.error(f"Error collecting RTB summary: {e}")
                rtb_data['summary'] = {'error': str(e)}

            # 收集RTB趋势数据
            try:
                rtb_trends = get_rtb_trends_real(brand)
                rtb_data['trends'] = rtb_trends
                logger.info(f"RTB trends collected successfully")
            except Exception as e:
                logger.error(f"Error collecting RTB trends: {e}")
                rtb_data['trends'] = {'error': str(e)}

            # 收集RTB明细数据
            try:
                rtb_detail = get_rtb_detail_real('整体', week_num, brand)
                rtb_data['detail'] = rtb_detail
                logger.info(f"RTB detail collected successfully for week {week_num}")
            except Exception as e:
                logger.error(f"Error collecting RTB detail: {e}")
                rtb_data['detail'] = {'error': str(e)}

            collected_data['rtb'] = rtb_data

        except Exception as e:
            logger.error(f"Error collecting RTB data: {e}")
            collected_data['rtb'] = {'error': str(e)}

        try:
            supply_data = {}

            # 收集供给概览数据
            try:
                supply_summary = get_supply_summary_real(week_num, brand)
                supply_data['summary'] = supply_summary
                logger.info(f"Supply summary collected successfully for week {week_num}")
            except Exception as e:
                logger.error(f"Error collecting supply summary: {e}")
                supply_data['summary'] = {'error': str(e)}

            # 收集供给趋势数据
            try:
                supply_trends = get_supply_trends_real(brand)
                supply_data['trends'] = supply_trends
                logger.info(f"Supply trends collected successfully")
            except Exception as e:
                logger.error(f"Error collecting supply trends: {e}")
                supply_data['trends'] = {'error': str(e)}

            # 收集供给明细数据
            try:
                supply_detail = get_supply_detail_real('整体', brand, week_num)
                supply_data['detail'] = supply_detail
                logger.info(f"Supply detail collected successfully for week {week_num}")
            except Exception as e:
                logger.error(f"Error collecting supply detail: {e}")
                supply_data['detail'] = {'error': str(e)}

            collected_data['supply'] = supply_data

        except Exception as e:
            logger.error(f"Error collecting supply data: {e}")
            collected_data['supply'] = {'error': str(e)}

        # 步骤5: 收集用户数据 (65%)
        progress_store[task_id].update({
            'progress': 65,
            'status': 'collecting_user',
            'message': '正在收集用户数据...'
        })
        try:
            user_data = {}

            # 收集用户概览数据
            try:
                user_summary = get_user_data_summary_real(week_num, brand)
                user_data['summary'] = user_summary
                logger.info(f"User summary collected successfully for week {week_num}")
            except Exception as e:
                logger.error(f"Error collecting user summary: {e}")
                user_data['summary'] = {'error': str(e)}

            # 收集用户趋势数据
            try:
                user_trends = get_user_data_trends_real(brand)
                user_data['trends'] = user_trends
                logger.info(f"User trends collected successfully")
            except Exception as e:
                logger.error(f"Error collecting user trends: {e}")
                user_data['trends'] = {'error': str(e)}

            # 收集用户明细数据
            try:
                user_detail = get_user_data_detail_real(brand)
                user_data['detail'] = user_detail
                logger.info(f"User detail collected successfully")
            except Exception as e:
                logger.error(f"Error collecting user detail: {e}")
                user_data['detail'] = {'error': str(e)}

            collected_data['user'] = user_data

        except Exception as e:
            logger.error(f"Error collecting user data: {e}")
            collected_data['user'] = {'error': str(e)}

        return collected_data

    except Exception as e:
        logger.error(f"Error collecting module data: {e}")
        return {'error': str(e)}

def collect_all_module_data_with_progress_debug(selected_week, brand, selected_platform='全平台'):
    """调试版本的数据收集函数 - 不使用进度更新"""
    try:
        # 解析周次 - 处理不同的周次格式
        week_num = int(selected_week) if isinstance(selected_week, str) and selected_week.isdigit() else selected_week

        collected_data = {}

        # 收集交易数据
        try:
            trading_data = {}

            # 收集交易概览数据 - 使用选择的平台
            try:
                trading_summary = get_trading_summary_real(week_num, selected_platform, brand)
                trading_data['summary'] = trading_summary
                logger.info(f"[DEBUG] Trading summary collected: GMV={trading_summary.get('week_data', {}).get('gmv', 'N/A')}, WOW={trading_summary.get('week_data', {}).get('wow', 'N/A')}")
            except Exception as e:
                logger.error(f"[DEBUG] Error collecting trading summary: {e}")
                trading_data['summary'] = {'error': str(e)}

            # 收集交易趋势数据
            try:
                trading_trends = get_trading_trends_real(selected_platform, brand)
                trading_data['trends'] = trading_trends
                logger.info(f"[DEBUG] Trading trends collected successfully")
            except Exception as e:
                logger.error(f"[DEBUG] Error collecting trading trends: {e}")
                trading_data['trends'] = {'error': str(e)}

            # 收集维度分析数据
            try:
                trading_dimensions = get_dimension_analysis_real('子品牌', brand)
                trading_data['dimensions'] = trading_dimensions
                logger.info(f"[DEBUG] Trading dimensions collected successfully")
            except Exception as e:
                logger.error(f"[DEBUG] Error collecting trading dimensions: {e}")
                trading_data['dimensions'] = {'error': str(e)}

            # 收集Top10数据
            try:
                trading_top10 = get_top10_data_real('商品', brand)
                trading_data['top10'] = trading_top10
                logger.info(f"[DEBUG] Trading top10 collected successfully")
            except Exception as e:
                logger.error(f"[DEBUG] Error collecting trading top10: {e}")
                trading_data['top10'] = {'error': str(e)}

            collected_data['trading'] = trading_data

        except Exception as e:
            logger.error(f"[DEBUG] Error collecting trading data: {e}")
            collected_data['trading'] = {'error': str(e)}

        # 收集活动数据
        try:
            activity_data = {}

            # 收集活动概览数据
            try:
                activity_summary = get_activity_summary_real(week_num, brand)
                activity_data['summary'] = activity_summary
                logger.info(f"[DEBUG] Activity summary collected successfully")
            except Exception as e:
                logger.error(f"[DEBUG] Error collecting activity summary: {e}")
                activity_data['summary'] = {'error': str(e)}

            # 收集活动趋势数据
            try:
                activity_trends = get_activity_trends_real(brand, week_num)
                activity_data['trends'] = activity_trends
                logger.info(f"[DEBUG] Activity trends collected successfully")
            except Exception as e:
                logger.error(f"[DEBUG] Error collecting activity trends: {e}")
                activity_data['trends'] = {'error': str(e)}

            # 收集活动平台图表数据
            
            try:
                activity_platform_charts = get_activity_platform_charts_real(brand)
                activity_data['platform_charts'] = activity_platform_charts
                logger.info(f"[DEBUG] Activity platform charts collected successfully")
            except Exception as e:
                logger.error(f"[DEBUG] Error collecting activity platform charts: {e}")
                activity_data['platform_charts'] = {'error': str(e)}

            # 收集活动明细数据
            try:
                activity_detail = get_activity_detail_real_weeks(brand)
                activity_data['detail'] = activity_detail
                logger.info(f"[DEBUG] Activity detail collected successfully")
            except Exception as e:
                logger.error(f"[DEBUG] Error collecting activity detail: {e}")
                activity_data['detail'] = {'error': str(e)}

            collected_data['activity'] = activity_data

        except Exception as e:
            logger.error(f"[DEBUG] Error collecting activity data: {e}")
            collected_data['activity'] = {'error': str(e)}

        # 收集RTB数据
        try:
            rtb_data = {}

            # 收集RTB概览数据
            try:
                rtb_summary = get_rtb_summary_real(week_num, brand)
                rtb_data['summary'] = rtb_summary
                logger.info(f"[DEBUG] RTB summary collected successfully")
            except Exception as e:
                logger.error(f"[DEBUG] Error collecting RTB summary: {e}")
                rtb_data['summary'] = {'error': str(e)}

            # 收集RTB趋势数据
            try:
                rtb_trends = get_rtb_trends_real(brand)
                rtb_data['trends'] = rtb_trends
                logger.info(f"[DEBUG] RTB trends collected successfully")
            except Exception as e:
                logger.error(f"[DEBUG] Error collecting RTB trends: {e}")
                rtb_data['trends'] = {'error': str(e)}

            # 收集RTB明细数据
            try:
                rtb_detail = get_rtb_detail_real('整体', week_num, brand)
                rtb_data['detail'] = rtb_detail
                logger.info(f"[DEBUG] RTB detail collected successfully")
            except Exception as e:
                logger.error(f"[DEBUG] Error collecting RTB detail: {e}")
                rtb_data['detail'] = {'error': str(e)}

            collected_data['rtb'] = rtb_data

        except Exception as e:
            logger.error(f"[DEBUG] Error collecting RTB data: {e}")
            collected_data['rtb'] = {'error': str(e)}

        # 收集供给数据
        try:
            supply_data = {}

            # 收集供给概览数据
            try:
                supply_summary = get_supply_summary_real(week_num, brand)
                supply_data['summary'] = supply_summary
                logger.info(f"[DEBUG] Supply summary collected successfully")
            except Exception as e:
                logger.error(f"[DEBUG] Error collecting supply summary: {e}")
                supply_data['summary'] = {'error': str(e)}

            # 收集供给趋势数据
            try:
                supply_trends = get_supply_trends_real(brand)
                supply_data['trends'] = supply_trends
                logger.info(f"[DEBUG] Supply trends collected successfully")
            except Exception as e:
                logger.error(f"[DEBUG] Error collecting supply trends: {e}")
                supply_data['trends'] = {'error': str(e)}

            # 收集供给明细数据
            try:
                supply_detail = get_supply_detail_real('整体', brand, week_num)
                supply_data['detail'] = supply_detail
                logger.info(f"[DEBUG] Supply detail collected successfully")
            except Exception as e:
                logger.error(f"[DEBUG] Error collecting supply detail: {e}")
                supply_data['detail'] = {'error': str(e)}

            collected_data['supply'] = supply_data

        except Exception as e:
            logger.error(f"[DEBUG] Error collecting supply data: {e}")
            collected_data['supply'] = {'error': str(e)}

        # 收集用户数据
        try:
            user_data = {}

            # 收集用户概览数据
            try:
                user_summary = get_user_data_summary_real(week_num, brand)
                user_data['summary'] = user_summary
                logger.info(f"[DEBUG] User summary collected successfully")
            except Exception as e:
                logger.error(f"[DEBUG] Error collecting user summary: {e}")
                user_data['summary'] = {'error': str(e)}

            # 收集用户趋势数据
            try:
                user_trends = get_user_data_trends_real(brand)
                user_data['trends'] = user_trends
                logger.info(f"[DEBUG] User trends collected successfully")
            except Exception as e:
                logger.error(f"[DEBUG] Error collecting user trends: {e}")
                user_data['trends'] = {'error': str(e)}

            # 收集用户明细数据
            try:
                user_detail = get_user_data_detail_real(brand)
                user_data['detail'] = user_detail
                logger.info(f"[DEBUG] User detail collected successfully")
            except Exception as e:
                logger.error(f"[DEBUG] Error collecting user detail: {e}")
                user_data['detail'] = {'error': str(e)}

            collected_data['user'] = user_data

        except Exception as e:
            logger.error(f"[DEBUG] Error collecting user data: {e}")
            collected_data['user'] = {'error': str(e)}

        return collected_data

    except Exception as e:
        logger.error(f"[DEBUG] Error collecting module data: {e}")
        return {'error': str(e)}

def collect_all_module_data(selected_week, brand):
    """收集所有5个模块的数据"""
    try:
        # 解析周次
        week_num = int(selected_week) if isinstance(selected_week, str) and selected_week.isdigit() else selected_week

        collected_data = {}

        # 1. 交易数据模块
        try:
            trading_data = {
                'summary': get_trading_summary_real(week_num, '全平台', brand),
                'trends': get_trading_trends_real('全平台', brand),
                'dimensions': get_dimension_analysis_real('子品牌', brand),
                'top10': get_top10_data_real('商品', brand)
            }
            collected_data['trading'] = trading_data
        except Exception as e:
            logger.error(f"Error collecting trading data: {e}")
            collected_data['trading'] = {'error': str(e)}

        # 2. 活动数据模块
        try:
            activity_data = {
                'summary': get_activity_summary_real(week_num, brand),
                'trends': get_activity_trends_real(brand),
                'platform_charts': get_activity_platform_charts_real(brand),
                'detail': get_activity_detail_real_weeks(brand)
            }
            collected_data['activity'] = activity_data
        except Exception as e:
            logger.error(f"Error collecting activity data: {e}")
            collected_data['activity'] = {'error': str(e)}

        # 3. RTB投放模块
        try:
            rtb_data = {
                'summary': get_rtb_summary_real(week_num, brand),
                'trends': get_rtb_trends_real(brand),
                'detail': get_rtb_detail_real('整体', f'2024-W{week_num:02d}', brand)
            }
            collected_data['rtb'] = rtb_data
        except Exception as e:
            logger.error(f"Error collecting RTB data: {e}")
            collected_data['rtb'] = {'error': str(e)}

        # 4. 供给表现模块
        try:
            supply_data = {
                'summary': get_supply_summary_real(week_num, brand),
                'trends': get_supply_trends_real(brand),
                'detail': get_supply_detail_real('整体', brand, week_num)
            }
            collected_data['supply'] = supply_data
        except Exception as e:
            logger.error(f"Error collecting supply data: {e}")
            collected_data['supply'] = {'error': str(e)}

        # 5. 用户数据模块
        try:
            user_data = {
                'summary': get_user_data_summary_real(week_num, brand),
                'trends': get_user_data_trends_real(brand),
                'detail': get_user_data_detail_real(brand)
            }
            collected_data['user'] = user_data
        except Exception as e:
            logger.error(f"Error collecting user data: {e}")
            collected_data['user'] = {'error': str(e)}

        return collected_data

    except Exception as e:
        logger.error(f"Error collecting module data: {e}")
        return {'error': str(e)}

def generate_insights_with_ai(data, selected_week, brand):
    """使用DeepSeek AI生成洞察"""
    import time

    max_retries = 3
    retry_delay = 2

    for attempt in range(max_retries):
        try:
            # 初始化OpenAI客户端，增加超时设置
            client = OpenAI(
                api_key="1efe50eb-2407-4f4c-8fd3-275db7202fb8",
                base_url="https://ark.cn-beijing.volces.com/api/v3",
                timeout=60.0  # 设置60秒超时
            )

            # 构建提示词
            prompt = build_analysis_prompt(data, selected_week, brand)

            logger.info(f"Attempting AI call, attempt {attempt + 1}/{max_retries}")

            # 调用AI，增加更多参数控制
            response = client.chat.completions.create(
                model="deepseek-v3-250324",
                messages=[
                    {"role": "system", "content": "你是一个资深的零售业务分析专家，擅长对零售行业数据进行深度分析洞察"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=4000,  # 限制输出长度
                presence_penalty=0.1,
                frequency_penalty=0.1
            )

            answer = response.choices[0].message.content.strip()
            logger.info("AI insights generated successfully")
            return answer

        except Exception as e:
            logger.error(f"AI call attempt {attempt + 1} failed: {e}")

            if attempt < max_retries - 1:
                logger.info(f"Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
                retry_delay *= 2  # 指数退避
            else:
                logger.error("All AI call attempts failed")
                return f"AI洞察生成失败: 网络连接超时或服务暂时不可用，请稍后重试。错误详情: {str(e)}"

def build_analysis_prompt(data, selected_week, brand):
    """构建AI分析提示词"""

    # 简化数据结构，只保留关键指标
    simplified_data = {}

    for module, module_data in data.items():
        if isinstance(module_data, dict) and 'error' not in module_data:
            simplified_data[module] = {}
            for key, value in module_data.items():
                if isinstance(value, dict):
                    # 只保留数值型数据，避免过长的文本
                    simplified_value = {}
                    for k, v in value.items():
                        if isinstance(v, (int, float)) or (isinstance(v, str) and len(v) < 100):
                            simplified_value[k] = v
                    simplified_data[module][key] = simplified_value
                else:
                    simplified_data[module][key] = value
        else:
            simplified_data[module] = {'status': 'data_unavailable'}

    prompt = f"""
你是一个资深的零售业务分析专家，请对{brand if brand else '全品牌'}第{selected_week}周的业务数据进行深度分析。

## 重要说明
1. **GMV定义**：本分析中的GMV总和特指交易数据模块中的GMV，即整体交易规模，不是活动GMV
2. **指标名称**：严格使用页面上实际存在的指标名称，不要创造新指标
3. **数据真实性**：所有数据必须基于实际收集的数据，不能捏造数据
4. **分析原则**：依据已有数据展开洞察，挖掘数据背后的业务逻辑和改进机会

## 各页面实际指标名称
- **交易数据页面**：GMV总和、GMV MTD、GMV YTD、年同比、周环比、月环比
- **活动数据页面**：活动GMV、活动GMV占比、核销金额、活动费比、全量费比
- **RTB投放页面**：消耗、T+1引导成交金额、ROI、消耗进度、曝光数、点击数、点击率、订单量、订单转化率
- **供给表现页面**：门店数量、门店渗透率、门店活跃度、SKU数量、SKU售罄率
- **用户数据页面**：用户规模、新用户数、ARPU、用户活跃度

## 分析框架说明
请按照以下4个部分的结构进行深度分析，重点关注交易波动的原因归因和影响因子分析。

## 数据概览
- 品牌：{brand if brand else '全品牌'}
- 分析周次：第{selected_week}周

请按以下结构输出分析报告：

# Part1：周度交易达成情况及同环比情况，及对比近8周异动

基于交易数据页面的实际指标，深度分析：
- **GMV总和**：当周GMV总和数值、年同比、周环比变化，与近8周数据对比分析异动情况
- **GMV MTD/YTD**：当前月度/年度达成情况，达成率分析
- **平台分布**：各平台GMV贡献占比及趋势变化，识别平台层面的异动
- **维度表现**：子品牌等维度的GMV贡献分析，识别维度层面的异动
- **异动识别**：对比近8周数据，识别本周交易表现的显著异动点

# Part2：影响因子的表现

## 2.1 用户表现
基于用户数据页面的实际指标，分析交易波动是否由用户因素导致：
- **用户规模变化**：用户规模指标的变化对GMV总和的影响
- **新用户数变化**：新用户数指标的变化对GMV总和的影响
- **ARPU变化**：ARPU（平均用户价值）指标的变化对GMV总和的影响
- **用户活跃度变化**：用户活跃度指标的变化对GMV总和的影响
- **归因结论**：用户表现对本周GMV总和波动的贡献度分析

## 2.2 投资表现

### 2.2.1 活动表现
基于活动数据页面的实际指标，分析交易波动是否由活动投资导致：
- **活动GMV波动**：活动GMV指标变化对整体GMV总和的影响
- **活动GMV占比波动**：活动GMV占比指标变化对整体GMV总和的影响
- **核销金额波动**：核销金额指标变化对活动GMV的影响
- **活动费比变化**：活动费比指标变化对活动效果的影响
- **全量费比变化**：全量费比指标变化对整体投资效率的影响
- **归因结论**：活动表现对本周GMV总和波动的贡献度分析

### 2.2.2 RTB表现
基于RTB投放页面的实际指标，分析交易波动是否由RTB投资导致：
- **消耗波动**：消耗指标变化对投放规模的影响
- **T+1引导成交金额波动**：T+1引导成交金额指标变化对整体GMV总和的影响
- **ROI变化**：ROI指标变化对投放效果的影响
- **消耗进度变化**：消耗进度指标变化对投放节奏的影响
- **曝光数、点击数、点击率变化**：这些指标变化对投放效果的影响
- **订单量、订单转化率变化**：这些指标变化对最终转化的影响
- **归因结论**：RTB表现对本周GMV总和波动的贡献度分析

## 2.3 供给表现
基于供给表现页面的实际指标，分析交易波动是否由供给因素导致：
- **门店数量变化**：门店数量指标变化对GMV总和的影响
- **门店渗透率变化**：门店渗透率指标变化对GMV总和的影响
- **门店活跃度变化**：门店活跃度指标变化对GMV总和的影响
- **SKU数量变化**：SKU数量指标变化对GMV总和的影响
- **SKU售罄率变化**：SKU售罄率指标变化对GMV总和的负向影响
- **归因结论**：供给表现对本周GMV总和波动的贡献度分析

# Part3：影响维度表现

重点关注影响GMV总和波动的关键维度：
- **头部区域表现**：哪些重点区域的GMV总和波动对整体影响最大（基于现有区域数据）
- **头部渠道表现**：哪些重点平台/渠道的GMV总和波动对整体影响最大
- **头部商品表现**：哪些重点商品/品类的GMV总和波动对整体影响最大（基于Top10等数据）
- **维度交叉分析**：不同维度之间的交叉影响分析
- **关键驱动因素**：识别对GMV总和波动贡献最大的维度组合

# Part4：策略建议

基于以上分析，提出针对性的策略建议：
- **短期优化策略**：针对本周识别的关键问题，提出1-2周内可执行的改进措施
- **中期发展策略**：针对趋势性问题，提出1-2个月内的优化方向
- **长期战略建议**：针对结构性问题，提出长期的战略调整建议
- **优先级排序**：按照影响程度和执行难度，对建议进行优先级排序
- **预期效果**：基于数据分析，预估各项建议的潜在效果

## 输出要求
- 使用markdown格式
- 所有百分比数字用**粗体**标注
- 正增长用+号标识，负增长直接显示负号
- 严格使用页面上实际存在的指标名称：
  * 交易数据：GMV总和、GMV MTD、GMV YTD、年同比、周环比、月环比
  * 活动数据：活动GMV、活动GMV占比、核销金额、活动费比、全量费比
  * RTB投放：消耗、T+1引导成交金额、ROI、消耗进度、曝光数、点击数、点击率、订单量、订单转化率
  * 供给表现：门店数量、门店渗透率、门店活跃度、SKU数量、SKU售罄率
  * 用户数据：用户规模、新用户数、ARPU、用户活跃度
- 绝对不要使用页面上不存在的指标名称
- 每个洞察点都要有具体数据支撑，基于实际收集的数据进行分析
- 不要捏造数据，所有数据必须来自实际收集的数据
- 如果某个模块有数据，就不应该出现"无有效数据"的情况
- 重点关注GMV总和（交易数据中的GMV）的表现和驱动因素
- 深度挖掘各影响因子对GMV总和波动的贡献度，进行归因分析

数据详情：
{json.dumps(simplified_data, ensure_ascii=False, indent=2)[:4000]}...
"""
    return prompt

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5002)