#!/usr/bin/env python3
"""
调试年同比和周环比计算逻辑
"""

from database import execute_query
from real_data_queries import (
    get_dimension_trends_real,
    get_current_week_number,
    get_week_date_range
)
from datetime import datetime
import json

def debug_yoy_wow_calculation():
    """调试年同比和周环比计算"""
    print("🔍 调试年同比和周环比计算逻辑...")
    
    # 获取当前周信息
    current_year = datetime.now().year
    current_week = get_current_week_number()
    base_week = current_week - 1
    if base_week <= 0:
        base_week = 52
        current_year -= 1
    
    print(f"📅 基准周: {current_year}年第{base_week}周")
    
    # 测试一个具体的大区维度
    test_dimension = "SCCL"
    
    print(f"\n🎯 测试维度: {test_dimension}")
    
    # 获取基准周的日期范围
    week_start, week_end = get_week_date_range(current_year, base_week)
    print(f"📅 基准周日期范围: {week_start} 到 {week_end}")
    
    # 查询基准周数据
    current_week_sql = f"""
    SELECT SUM(gmv) as gmv, COUNT(*) as count
    FROM dws_o2o_sales_d
    WHERE area = '{test_dimension}'
    AND ds BETWEEN '{week_start}' AND '{week_end}'
    """
    
    current_result = execute_query(current_week_sql)
    current_gmv = float(current_result[0]['gmv']) if current_result and current_result[0]['gmv'] else 0
    print(f"📊 {test_dimension} 第{base_week}周 GMV: {current_gmv:,.2f}")
    
    # 查询去年同期数据
    yoy_year = current_year - 1
    yoy_week_start, yoy_week_end = get_week_date_range(yoy_year, base_week)
    print(f"📅 去年同期日期范围: {yoy_week_start} 到 {yoy_week_end}")
    
    yoy_week_sql = f"""
    SELECT SUM(gmv) as gmv, COUNT(*) as count
    FROM dws_o2o_sales_d
    WHERE area = '{test_dimension}'
    AND ds BETWEEN '{yoy_week_start}' AND '{yoy_week_end}'
    """
    
    yoy_result = execute_query(yoy_week_sql)
    yoy_gmv = float(yoy_result[0]['gmv']) if yoy_result and yoy_result[0]['gmv'] else 0
    print(f"📊 {test_dimension} 去年第{base_week}周 GMV: {yoy_gmv:,.2f}")
    
    # 计算年同比
    if yoy_gmv > 0:
        yoy_rate = (current_gmv - yoy_gmv) / yoy_gmv
        print(f"📈 年同比: {yoy_rate:.4f} ({yoy_rate*100:.2f}%)")
    else:
        print("⚠️  去年同期无数据，无法计算年同比")
    
    # 查询上周数据
    wow_week = base_week - 1
    wow_year = current_year
    if wow_week <= 0:
        wow_week = 52
        wow_year -= 1
    
    wow_week_start, wow_week_end = get_week_date_range(wow_year, wow_week)
    print(f"📅 上周日期范围: {wow_week_start} 到 {wow_week_end}")
    
    wow_week_sql = f"""
    SELECT SUM(gmv) as gmv, COUNT(*) as count
    FROM dws_o2o_sales_d
    WHERE area = '{test_dimension}'
    AND ds BETWEEN '{wow_week_start}' AND '{wow_week_end}'
    """
    
    wow_result = execute_query(wow_week_sql)
    wow_gmv = float(wow_result[0]['gmv']) if wow_result and wow_result[0]['gmv'] else 0
    print(f"📊 {test_dimension} 第{wow_week}周 GMV: {wow_gmv:,.2f}")
    
    # 计算周环比
    if wow_gmv > 0:
        wow_rate = (current_gmv - wow_gmv) / wow_gmv
        print(f"📈 周环比: {wow_rate:.4f} ({wow_rate*100:.2f}%)")
    else:
        print("⚠️  上周无数据，无法计算周环比")
    
    print("\n" + "="*60)
    print("检查数据库中的历史数据范围")
    print("="*60)
    
    # 检查数据库中的数据范围
    data_range_sql = f"""
    SELECT 
        MIN(ds) as min_date,
        MAX(ds) as max_date,
        COUNT(DISTINCT ds) as date_count,
        SUM(gmv) as total_gmv
    FROM dws_o2o_sales_d
    WHERE area = '{test_dimension}'
    """
    
    range_result = execute_query(data_range_sql)
    if range_result:
        min_date = range_result[0]['min_date']
        max_date = range_result[0]['max_date']
        date_count = range_result[0]['date_count']
        total_gmv = float(range_result[0]['total_gmv'])
        
        print(f"📊 {test_dimension} 数据范围:")
        print(f"  - 最早日期: {min_date}")
        print(f"  - 最晚日期: {max_date}")
        print(f"  - 日期数量: {date_count:,}")
        print(f"  - 总GMV: {total_gmv:,.2f}")
    
    # 检查去年数据是否存在
    print(f"\n🔍 检查{yoy_year}年数据是否存在:")
    yoy_data_sql = f"""
    SELECT 
        COUNT(*) as record_count,
        SUM(gmv) as total_gmv,
        MIN(ds) as min_date,
        MAX(ds) as max_date
    FROM dws_o2o_sales_d
    WHERE area = '{test_dimension}'
    AND ds >= '{yoy_year}-01-01'
    AND ds < '{yoy_year + 1}-01-01'
    """
    
    yoy_data_result = execute_query(yoy_data_sql)
    if yoy_data_result and yoy_data_result[0]['record_count'] > 0:
        record_count = yoy_data_result[0]['record_count']
        total_gmv = float(yoy_data_result[0]['total_gmv'])
        min_date = yoy_data_result[0]['min_date']
        max_date = yoy_data_result[0]['max_date']
        
        print(f"✅ {yoy_year}年有数据:")
        print(f"  - 记录数: {record_count:,}")
        print(f"  - 总GMV: {total_gmv:,.2f}")
        print(f"  - 日期范围: {min_date} 到 {max_date}")
    else:
        print(f"❌ {yoy_year}年无数据")
    
    print("\n" + "="*60)
    print("测试API返回的年同比和周环比")
    print("="*60)
    
    # 测试API返回的数据
    try:
        api_result = get_dimension_trends_real('大区', '', None)
        
        if 'data' in api_result:
            for item in api_result['data']:
                if item['dimension'] == test_dimension:
                    print(f"📊 API返回的{test_dimension}数据:")
                    
                    # 找到基准周的数据
                    for trend in item['trend']:
                        if trend['week'] == base_week:
                            api_gmv = trend['gmv']
                            api_yoy = trend['yoy']
                            api_wow = trend['wow']
                            
                            print(f"  - 第{base_week}周 GMV: {api_gmv:,.2f}")
                            print(f"  - 年同比: {api_yoy:.4f} ({api_yoy*100:.2f}%)")
                            print(f"  - 周环比: {api_wow:.4f} ({api_wow*100:.2f}%)")
                            
                            # 比较计算结果
                            if abs(api_gmv - current_gmv) < 0.01:
                                print("✅ GMV数据一致")
                            else:
                                print(f"⚠️  GMV数据不一致: API={api_gmv:,.2f}, 直接查询={current_gmv:,.2f}")
                            
                            break
                    break
        
    except Exception as e:
        print(f"❌ API调用失败: {e}")

if __name__ == "__main__":
    debug_yoy_wow_calculation()
