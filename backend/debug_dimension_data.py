#!/usr/bin/env python3
"""
调试维度数据问题
"""

import requests
import json

def debug_dimension_data():
    """调试维度数据"""
    
    base_url = "http://localhost:5001/api/activity/detail"
    brand = "圣农"
    
    print("🔍 调试维度数据问题")
    print("=" * 60)
    
    # 测试整体维度
    print("\n1. 测试整体维度")
    print("-" * 40)
    
    response = requests.get(f"{base_url}?type=整体&brand={brand}")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 整体维度返回数据:")
        print(f"   - 维度类型: {data['dimension_type']}")
        print(f"   - 数据条数: {len(data['data'])}")
        print(f"   - 第一条数据:")
        if len(data['data']) > 0:
            first_item = data['data'][0]
            print(f"     维度: {first_item['dimension']}")
            print(f"     活动GMV: ¥{first_item['activity_gmv']:,.2f}")
            print(f"     包含字段: {list(first_item.keys())}")
    else:
        print(f"❌ 整体维度API调用失败: {response.status_code}")
    
    # 测试城市维度
    print("\n2. 测试城市维度")
    print("-" * 40)
    
    response = requests.get(f"{base_url}?type=城市&brand={brand}&page=1&page_size=3")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 城市维度返回数据:")
        print(f"   - 维度类型: {data['dimension_type']}")
        print(f"   - 数据条数: {len(data['data'])}")
        print(f"   - 总数据量: {data['total']}")
        print(f"   - 前3条数据:")
        for i, item in enumerate(data['data'][:3]):
            print(f"     {i+1}. 维度: {item['dimension']}")
            print(f"        活动GMV: ¥{item['activity_gmv']:,.2f}")
            print(f"        GMV占比: {item['gmv_ratio']:.2%}")
    else:
        print(f"❌ 城市维度API调用失败: {response.status_code}")
    
    # 测试零售商维度
    print("\n3. 测试零售商维度")
    print("-" * 40)
    
    response = requests.get(f"{base_url}?type=零售商&brand={brand}&page=1&page_size=3")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 零售商维度返回数据:")
        print(f"   - 维度类型: {data['dimension_type']}")
        print(f"   - 数据条数: {len(data['data'])}")
        print(f"   - 总数据量: {data['total']}")
        print(f"   - 前3条数据:")
        for i, item in enumerate(data['data'][:3]):
            print(f"     {i+1}. 维度: {item['dimension']}")
            print(f"        活动GMV: ¥{item['activity_gmv']:,.2f}")
            print(f"        GMV占比: {item['gmv_ratio']:.2%}")
    else:
        print(f"❌ 零售商维度API调用失败: {response.status_code}")
    
    # 测试活动机制维度
    print("\n4. 测试活动机制维度")
    print("-" * 40)

    response = requests.get(f"{base_url}?type=活动机制&brand={brand}&page=1&page_size=3")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 活动机制维度返回数据:")
        print(f"   - 维度类型: {data['dimension_type']}")
        print(f"   - 数据条数: {len(data['data'])}")
        print(f"   - 总数据量: {data['total']}")
        print(f"   - 前3条数据:")
        for i, item in enumerate(data['data'][:3]):
            print(f"     {i+1}. 维度: {item['dimension']}")
            print(f"        活动GMV: ¥{item['activity_gmv']:,.2f}")
            print(f"        GMV占比: {item['gmv_ratio']:.2%}")
            print(f"        包含字段: {list(item.keys())}")
    else:
        print(f"❌ 活动机制维度API调用失败: {response.status_code}")
    
    print("\n" + "=" * 60)
    print("🎉 维度数据调试完成！")

if __name__ == "__main__":
    debug_dimension_data()
