"""
Excel导出功能模块
支持五个页面的数据导出：交易表现、用户表现、活动表现、RTB表现、供给表现
"""

import os
import tempfile
from datetime import datetime, timedelta
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from openpyxl.chart import Pie<PERSON><PERSON>, LineChart, Reference
from openpyxl.drawing.image import Image
import logging
import base64
from io import BytesIO

from real_data_queries import (
    get_trading_summary_real,
    get_trading_trends_real,
    get_dimension_analysis_real,
    get_dimension_trends_real,
    get_top10_data_real,
    get_activity_summary_real,
    get_activity_trends_real,
    get_activity_platform_charts_real,
    get_activity_detail_real_weeks,
    get_activity_detail_real_dimension,
    get_rtb_summary_real,
    get_rtb_trends_real,
    get_rtb_detail_real,
    get_user_data_summary_real,
    get_user_data_trends_real,
    get_user_data_detail_real,
    get_user_platform_charts_real,
    get_supply_summary_real,
    get_supply_trends_real,
    get_supply_detail_real
)

logger = logging.getLogger(__name__)

# 平台配色方案
PLATFORM_COLORS = {
    '多点': 'FF6F00',
    '美团': 'FFD161', 
    '饿了么': '0086FF',
    '京东到家': '01af00',
    '淘鲜达': 'FF5500'
}

# 样式定义
HEADER_FONT = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
HEADER_FILL = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
DATA_FONT = Font(name='微软雅黑', size=10)
BORDER = Border(
    left=Side(style='thin'),
    right=Side(style='thin'),
    top=Side(style='thin'),
    bottom=Side(style='thin')
)

def get_week_date_range(year, week):
    """根据年份和周数获取该周的日期范围"""
    jan_4 = datetime(year, 1, 4)
    week_1_monday = jan_4 - timedelta(days=jan_4.weekday())
    week_start = week_1_monday + timedelta(weeks=week-1)
    week_end = week_start + timedelta(days=6)
    return week_start.strftime('%m/%d'), week_end.strftime('%m/%d')

def format_number(value, is_percentage=False, is_currency=False):
    """格式化数字显示"""
    if value is None:
        return '-'
    
    try:
        num_value = float(value)
        if is_percentage:
            return f"{num_value:.1%}"
        elif is_currency:
            return f"{num_value:,.0f}"
        else:
            return f"{num_value:,.2f}"
    except (ValueError, TypeError):
        return str(value)

def apply_cell_style(cell, font=None, fill=None, alignment=None, border=None):
    """应用单元格样式"""
    if font:
        cell.font = font
    if fill:
        cell.fill = fill
    if alignment:
        cell.alignment = alignment
    if border:
        cell.border = border

def create_header_row(ws, headers, row_num=1):
    """创建表头行"""
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=row_num, column=col, value=header)
        apply_cell_style(cell, HEADER_FONT, HEADER_FILL,
                        Alignment(horizontal='center', vertical='center'), BORDER)

    # 调整列宽
    for col in range(1, len(headers) + 1):
        ws.column_dimensions[get_column_letter(col)].width = 15



def insert_chart_image(ws, image_data, row, col, title="图表"):
    """在Excel中插入图表截图"""
    try:
        if not image_data:
            logger.warning(f"图表截图数据为空: {title}")
            return row

        # 如果是base64编码的图片，先解码
        if isinstance(image_data, str) and image_data.startswith('data:image'):
            # 移除data:image/png;base64,前缀
            base64_data = image_data.split(',')[1]
            image_bytes = base64.b64decode(base64_data)
        else:
            image_bytes = image_data

        # 创建图片对象
        img_stream = BytesIO(image_bytes)
        img = Image(img_stream)

        # 设置图片大小（可以根据需要调整）
        img.width = 600
        img.height = 400

        # 添加标题
        ws.cell(row=row, column=col, value=title).font = Font(size=12, bold=True)

        # 插入图片（在标题下方）
        img.anchor = f'{get_column_letter(col)}{row + 1}'
        ws.add_image(img)

        logger.info(f"成功插入图表截图: {title}")

        # 返回下一个可用行（图片高度约20行）
        return row + 22

    except Exception as e:
        logger.error(f"插入图表截图失败: {title}, 错误: {e}")
        # 如果插入失败，至少添加一个占位符
        ws.cell(row=row, column=col, value=f"{title}（图表截图插入失败）")
        return row + 2

def export_trading_data(week, brand='', ai_insights='', chart_images=None):
    """导出交易表现数据"""
    logger.info(f"开始导出交易表现数据 - 周次: {week}, 品牌: {brand}")
    if chart_images:
        logger.info(f"接收到图表截图: {list(chart_images.keys())}")
    else:
        logger.info("未接收到图表截图数据")

    wb = Workbook()

    # 获取有数据的平台列表
    platforms = ['美团', '饿了么', '京东到家', '多点', '淘鲜达']

    # 删除默认工作表
    wb.remove(wb.active)

    # 首先创建AI洞察工作表
    if ai_insights:
        try:
            ai_ws = wb.create_sheet(title='AI洞察分析')
            ai_ws.cell(row=1, column=1, value='AI洞察分析').font = Font(size=16, bold=True)
            ai_ws.cell(row=2, column=1, value=f'分析周次: 第{week}周').font = Font(size=12)
            ai_ws.cell(row=3, column=1, value=f'品牌筛选: {brand if brand else "全品牌"}').font = Font(size=12)

            # 将AI洞察内容按行分割并写入Excel
            lines = ai_insights.split('\n')
            current_row = 5

            for line in lines:
                if line.strip():  # 跳过空行
                    # 检查是否是标题行（以#开头）
                    if line.strip().startswith('#'):
                        # 移除#符号并设置为粗体
                        title_text = line.strip().lstrip('#').strip()
                        ai_ws.cell(row=current_row, column=1, value=title_text).font = Font(size=14, bold=True)
                    else:
                        ai_ws.cell(row=current_row, column=1, value=line.strip())
                    current_row += 1
                else:
                    current_row += 1  # 保留空行

            # 调整列宽
            ai_ws.column_dimensions['A'].width = 100
            logger.info("AI洞察工作表创建成功")
        except Exception as e:
            logger.error(f"创建AI洞察工作表失败: {e}")

    # 标记是否有任何平台有数据
    has_data = False
    platform_summary = {}  # 存储各平台汇总数据

    for platform in platforms:
        try:
            # 检查平台是否有数据
            summary_data = get_trading_summary_real(week, platform, brand)
            if not summary_data:
                logger.warning(f"{platform}平台无数据")
                continue

            # 只有当平台有实际数据时才创建工作表
            week_gmv = summary_data.get('week_data', {}).get('gmv', 0)
            if week_gmv is None or week_gmv == 0:
                logger.info(f"{platform}平台GMV为0，跳过")
                continue

            has_data = True

            # 收集平台汇总数据
            week_data = summary_data.get('week_data', {})
            platform_summary[platform] = {
                'gmv': week_data.get('gmv', 0),
                'yoy': week_data.get('yoy', 0),
                'wow': week_data.get('wow', 0)
            }

            # 创建平台工作表
            ws = wb.create_sheet(title=platform)
            
            # 1. 概览数据
            ws.cell(row=1, column=1, value=f"{platform} - 交易表现概览").font = Font(size=14, bold=True)
            
            # 周数据
            week_data = summary_data.get('week_data', {})
            headers = ['指标', '数值', '年同比', '周环比']
            create_header_row(ws, headers, 3)
            
            ws.cell(row=4, column=1, value='周GMV')
            ws.cell(row=4, column=2, value=format_number(week_data.get('gmv'), is_currency=True))
            ws.cell(row=4, column=3, value=format_number(week_data.get('yoy'), is_percentage=True))
            ws.cell(row=4, column=4, value=format_number(week_data.get('wow'), is_percentage=True))
            
            # MTD数据
            mtd_data = summary_data.get('mtd_data', {})
            ws.cell(row=5, column=1, value='MTD GMV')
            ws.cell(row=5, column=2, value=format_number(mtd_data.get('current'), is_currency=True))
            ws.cell(row=5, column=3, value=format_number(mtd_data.get('yoy'), is_percentage=True))
            ws.cell(row=5, column=4, value=format_number(mtd_data.get('mom'), is_percentage=True))
            
            # YTD数据
            ytd_data = summary_data.get('ytd_data', {})
            ws.cell(row=6, column=1, value='YTD GMV')
            ws.cell(row=6, column=2, value=format_number(ytd_data.get('current'), is_currency=True))
            ws.cell(row=6, column=3, value=format_number(ytd_data.get('yoy'), is_percentage=True))
            ws.cell(row=6, column=4, value='-')
            
            # 2. 趋势数据
            current_row = 8  # 初始化current_row变量
            trends_data = get_trading_trends_real(platform, brand)
            if trends_data and trends_data.get('trend_data'):
                ws.cell(row=current_row, column=1, value='GMV趋势分析').font = Font(size=12, bold=True)
                current_row += 2

                trend_headers = ['周次', 'GMV', '年同比', '周环比']
                create_header_row(ws, trend_headers, current_row)
                current_row += 1

                # 填充趋势数据
                for trend in trends_data['trend_data']:
                    ws.cell(row=current_row, column=1, value=f"第{trend.get('week')}周")
                    ws.cell(row=current_row, column=2, value=format_number(trend.get('gmv'), is_currency=True))
                    ws.cell(row=current_row, column=3, value=format_number(trend.get('yoy'), is_percentage=True))
                    ws.cell(row=current_row, column=4, value=format_number(trend.get('wow'), is_percentage=True))
                    current_row += 1

                current_row += 1  # 添加空行

                # 插入趋势图表截图（如果有的话）
                trend_chart_data = None
                if chart_images:
                    # 优先查找平台特定的趋势图
                    platform_trend_key = f"{platform}_trend_chart"
                    if chart_images.get(platform_trend_key):
                        trend_chart_data = chart_images[platform_trend_key]
                        logger.info(f"使用平台特定趋势图: {platform_trend_key}")
                    elif chart_images.get('trend_chart'):
                        trend_chart_data = chart_images['trend_chart']
                        logger.info("使用通用趋势图: trend_chart")

                if trend_chart_data:
                    current_row = insert_chart_image(ws, trend_chart_data, current_row, 1, f'{platform}GMV趋势图表')
                else:
                    logger.warning(f"未找到{platform}平台的趋势图数据")
                    current_row += 2  # 如果没有图表，留出一些空间

            # 3. 维度分析明细数据 - 显示近8周各维度数据
            dimensions = ['子品牌', '品线', '渠道类型', '大区']
            # current_row已经在上面的趋势图表部分设置了

            for dimension in dimensions:
                try:
                    dim_trends_data = get_dimension_trends_real(dimension, brand, None, platform)
                    if dim_trends_data and dim_trends_data.get('data'):
                        # 过滤掉没有有效数据的维度
                        valid_dimensions = [item for item in dim_trends_data['data']
                                          if item.get('trend') and len(item.get('trend', [])) > 0]

                        if valid_dimensions:  # 只有当有有效数据时才创建表格
                            ws.cell(row=current_row, column=1, value=f'{dimension}维度分析').font = Font(size=12, bold=True)
                            current_row += 1

                            # 构建表头：时间 + 各维度的GMV、占比、年同比、周环比
                            headers = ['时间']
                            for dim_item in valid_dimensions:
                                dim_name = dim_item.get('dimension', '')
                                headers.extend([
                                    f'{dim_name}_GMV',
                                    f'{dim_name}_占比',
                                    f'{dim_name}_年同比',
                                    f'{dim_name}_周环比'
                                ])

                            create_header_row(ws, headers, current_row + 1)
                            current_row += 2

                            # 获取周数据（假设所有维度的周数据长度一致）
                            weeks_data = valid_dimensions[0].get('trend', [])

                            # 按周生成数据行
                            for week_idx, week_data in enumerate(weeks_data):
                                week_no = week_data.get('week', week_idx + 1)

                                # 计算该周所有维度的GMV总和（用于占比计算）
                                week_total_gmv = 0
                                for dim_item in valid_dimensions:
                                    if week_idx < len(dim_item.get('trend', [])):
                                        week_total_gmv += dim_item['trend'][week_idx].get('gmv', 0)

                                # 填充数据
                                col = 1
                                ws.cell(row=current_row, column=col, value=f'第{week_no}周')
                                col += 1

                                for dim_item in valid_dimensions:
                                    if week_idx < len(dim_item.get('trend', [])):
                                        week_trend = dim_item['trend'][week_idx]
                                        gmv = week_trend.get('gmv', 0)
                                        gmv_ratio = (gmv / week_total_gmv) if week_total_gmv > 0 else 0
                                        yoy = week_trend.get('yoy', 0)
                                        wow = week_trend.get('wow', 0)
                                    else:
                                        gmv = 0
                                        gmv_ratio = 0
                                        yoy = 0
                                        wow = 0

                                    ws.cell(row=current_row, column=col, value=format_number(gmv, is_currency=True))
                                    ws.cell(row=current_row, column=col + 1, value=format_number(gmv_ratio, is_percentage=True))
                                    ws.cell(row=current_row, column=col + 2, value=format_number(yoy, is_percentage=True))
                                    ws.cell(row=current_row, column=col + 3, value=format_number(wow, is_percentage=True))
                                    col += 4

                                current_row += 1

                            # 插入维度分析图表截图（如果有的话）
                            dimension_chart_mapping = {
                                '子品牌': 'subbrand_chart',
                                '品线': 'productline_chart',
                                '渠道类型': 'channel_chart',
                                '大区': 'region_chart'
                            }
                            dimension_chart_key = dimension_chart_mapping.get(dimension)

                            # 优先查找平台特定的图表，然后查找通用图表
                            platform_chart_key = f"{platform}_{dimension_chart_key}"
                            chart_data = None

                            if chart_images:
                                if chart_images.get(platform_chart_key):
                                    chart_data = chart_images[platform_chart_key]
                                    logger.info(f"使用平台特定图表: {platform_chart_key}")
                                elif chart_images.get(dimension_chart_key):
                                    chart_data = chart_images[dimension_chart_key]
                                    logger.info(f"使用通用图表: {dimension_chart_key}")

                            if chart_data:
                                current_row = insert_chart_image(ws, chart_data, current_row + 1, 1, f'{platform}平台{dimension}分析图表')
                            else:
                                logger.warning(f"未找到{platform}平台{dimension}维度的图表数据")
                                current_row += 2
                        else:
                            logger.info(f"{platform}平台{dimension}维度无有效数据")
                except Exception as e:
                    logger.error(f"获取{dimension}维度趋势数据失败: {e}")
                    continue
            
            # 4. Top10数据 - 只显示有数据的类别
            try:
                top10_data = get_top10_data_real(brand, platform)
                if top10_data:
                    categories = ['products', 'stores', 'cities']
                    category_names = ['商品', '零售商', '城市']

                    for cat, cat_name in zip(categories, category_names):
                        if cat in top10_data and top10_data[cat]:
                            # 过滤掉GMV为0或空的数据
                            valid_items = [item for item in top10_data[cat][:10]
                                         if item.get('gmv') and item.get('gmv') > 0]

                            if valid_items:  # 只有当有有效数据时才创建表格
                                ws.cell(row=current_row, column=1, value=f'Top10{cat_name}').font = Font(size=12, bold=True)
                                current_row += 1

                                top_headers = ['排名', '名称', 'GMV', '年同比', '周环比']
                                create_header_row(ws, top_headers, current_row + 1)
                                current_row += 2

                                for item in valid_items:
                                    ws.cell(row=current_row, column=1, value=item.get('rank'))
                                    ws.cell(row=current_row, column=2, value=item.get('name'))
                                    ws.cell(row=current_row, column=3, value=format_number(item.get('gmv'), is_currency=True))
                                    ws.cell(row=current_row, column=4, value=format_number(item.get('yoy'), is_percentage=True))
                                    ws.cell(row=current_row, column=5, value=format_number(item.get('wow'), is_percentage=True))
                                    current_row += 1

                                current_row += 2
                            else:
                                logger.info(f"{platform}平台Top10{cat_name}无有效数据")
            except Exception as e:
                logger.error(f"获取Top10数据失败: {e}")
                
        except Exception as e:
            logger.error(f"导出{platform}交易数据失败: {e}")
            continue

    # 创建汇总工作表（如果有数据的话）
    if has_data and platform_summary:
        try:
            summary_ws = wb.create_sheet(title='平台汇总', index=0)  # 插入到第一个位置

            # 标题
            summary_ws.cell(row=1, column=1, value='平台GMV汇总分析').font = Font(size=16, bold=True)
            summary_ws.cell(row=2, column=1, value=f'第{week}周 - {brand if brand else "全品牌"}').font = Font(size=12)

            # 平台汇总数据表
            headers = ['平台', 'GMV', '占比', '年同比', '周环比']
            create_header_row(summary_ws, headers, 4)

            # 计算总GMV
            total_gmv = sum(data['gmv'] for data in platform_summary.values())

            current_row = 5

            # 填充平台数据
            for platform, data in platform_summary.items():
                gmv = data['gmv']
                ratio = (gmv / total_gmv * 100) if total_gmv > 0 else 0

                summary_ws.cell(row=current_row, column=1, value=platform)
                summary_ws.cell(row=current_row, column=2, value=format_number(gmv, is_currency=True))
                summary_ws.cell(row=current_row, column=3, value=format_number(ratio, is_percentage=True))
                summary_ws.cell(row=current_row, column=4, value=format_number(data['yoy'], is_percentage=True))
                summary_ws.cell(row=current_row, column=5, value=format_number(data['wow'], is_percentage=True))
                current_row += 1

            current_row += 2

            # 插入平台GMV占比图表截图（如果有的话）
            platform_chart_data = None
            if chart_images:
                # 优先查找全平台的平台占比图
                all_platform_chart_key = "全平台_platform_chart"
                if chart_images.get(all_platform_chart_key):
                    platform_chart_data = chart_images[all_platform_chart_key]
                    logger.info(f"使用全平台平台占比图: {all_platform_chart_key}")
                elif chart_images.get('platform_chart'):
                    platform_chart_data = chart_images['platform_chart']
                    logger.info("使用通用平台占比图: platform_chart")

            if platform_chart_data:
                current_row = insert_chart_image(summary_ws, platform_chart_data, current_row, 1, '平台GMV占比图表')

            # 插入GMV趋势图表截图（如果有的话）
            trend_chart_data = None
            if chart_images:
                # 优先查找全平台的趋势图
                all_trend_chart_key = "全平台_trend_chart"
                if chart_images.get(all_trend_chart_key):
                    trend_chart_data = chart_images[all_trend_chart_key]
                    logger.info(f"使用全平台趋势图: {all_trend_chart_key}")
                elif chart_images.get('trend_chart'):
                    trend_chart_data = chart_images['trend_chart']
                    logger.info("使用通用趋势图: trend_chart")

            if trend_chart_data:
                current_row = insert_chart_image(summary_ws, trend_chart_data, current_row, 1, 'GMV趋势图表')

            # 调整列宽
            summary_ws.column_dimensions['A'].width = 12
            summary_ws.column_dimensions['B'].width = 15
            summary_ws.column_dimensions['C'].width = 10
            summary_ws.column_dimensions['D'].width = 10
            summary_ws.column_dimensions['E'].width = 10

        except Exception as e:
            logger.error(f"创建汇总工作表失败: {e}")

    # 创建全平台工作表（如果有数据的话）
    if has_data:
        try:
            all_platform_ws = wb.create_sheet(title='全平台', index=1)  # 插入到第二个位置

            # 获取全平台数据
            all_platform_data = get_trading_summary_real(week, '全平台', brand)

            if all_platform_data:
                # 标题
                all_platform_ws.cell(row=1, column=1, value='全平台交易表现').font = Font(size=16, bold=True)
                all_platform_ws.cell(row=2, column=1, value=f'第{week}周 - {brand if brand else "全品牌"}').font = Font(size=12)

                # 1. 概览数据
                current_row = 4
                week_data = all_platform_data.get('week_data', {})
                mtd_data = all_platform_data.get('mtd_data', {})
                ytd_data = all_platform_data.get('ytd_data', {})

                all_platform_ws.cell(row=current_row, column=1, value='概览数据').font = Font(size=12, bold=True)
                current_row += 2

                overview_headers = ['指标', '数值', '年同比', '周环比/月环比']
                create_header_row(all_platform_ws, overview_headers, current_row)
                current_row += 1

                # 周数据
                all_platform_ws.cell(row=current_row, column=1, value=f'第{week}周GMV')
                all_platform_ws.cell(row=current_row, column=2, value=format_number(week_data.get('gmv'), is_currency=True))
                all_platform_ws.cell(row=current_row, column=3, value=format_number(week_data.get('yoy'), is_percentage=True))
                all_platform_ws.cell(row=current_row, column=4, value=format_number(week_data.get('wow'), is_percentage=True))
                current_row += 1

                # MTD数据
                all_platform_ws.cell(row=current_row, column=1, value='MTD GMV')
                all_platform_ws.cell(row=current_row, column=2, value=format_number(mtd_data.get('current'), is_currency=True))
                all_platform_ws.cell(row=current_row, column=3, value=format_number(mtd_data.get('yoy'), is_percentage=True))
                all_platform_ws.cell(row=current_row, column=4, value=format_number(mtd_data.get('mom'), is_percentage=True))
                current_row += 1

                # YTD数据
                all_platform_ws.cell(row=current_row, column=1, value='YTD GMV')
                all_platform_ws.cell(row=current_row, column=2, value=format_number(ytd_data.get('current'), is_currency=True))
                all_platform_ws.cell(row=current_row, column=3, value=format_number(ytd_data.get('yoy'), is_percentage=True))
                all_platform_ws.cell(row=current_row, column=4, value='-')
                current_row += 2

                # 2. 趋势数据
                trends_data = get_trading_trends_real('全平台', brand)
                if trends_data and trends_data.get('trend_data'):
                    all_platform_ws.cell(row=current_row, column=1, value='GMV趋势分析').font = Font(size=12, bold=True)
                    current_row += 2

                    trend_headers = ['周次', 'GMV', '年同比', '周环比']
                    create_header_row(all_platform_ws, trend_headers, current_row)
                    current_row += 1

                    # 填充趋势数据
                    for trend in trends_data['trend_data']:
                        all_platform_ws.cell(row=current_row, column=1, value=f"第{trend.get('week')}周")
                        all_platform_ws.cell(row=current_row, column=2, value=format_number(trend.get('gmv'), is_currency=True))
                        all_platform_ws.cell(row=current_row, column=3, value=format_number(trend.get('yoy'), is_percentage=True))
                        all_platform_ws.cell(row=current_row, column=4, value=format_number(trend.get('wow'), is_percentage=True))
                        current_row += 1

                    current_row += 1  # 添加空行

                    # 插入趋势图表截图（如果有的话）
                    if chart_images and chart_images.get('trend_chart'):
                        current_row = insert_chart_image(all_platform_ws, chart_images['trend_chart'], current_row, 1, '全平台GMV趋势图表')
                    else:
                        current_row += 2  # 如果没有图表，留出一些空间

                # 3. 维度分析明细数据 - 显示近8周各维度数据（使用和单平台一样的格式）
                dimensions = ['子品牌', '品线', '渠道类型', '大区']

                for dimension in dimensions:
                    try:
                        dim_trends_data = get_dimension_trends_real(dimension, brand, None, '全平台')
                        if dim_trends_data and dim_trends_data.get('data'):
                            # 过滤掉没有有效数据的维度
                            valid_dimensions = [item for item in dim_trends_data['data']
                                              if item.get('trend') and len(item.get('trend', [])) > 0]

                            if valid_dimensions:  # 只有当有有效数据时才创建表格
                                all_platform_ws.cell(row=current_row, column=1, value=f'{dimension}分析').font = Font(size=12, bold=True)
                                current_row += 1

                                # 构建表头：时间 + 各维度的GMV、占比、年同比、周环比
                                headers = ['时间']
                                for dim_item in valid_dimensions:
                                    dim_name = dim_item.get('dimension', '')
                                    headers.extend([
                                        f'{dim_name}_GMV',
                                        f'{dim_name}_占比',
                                        f'{dim_name}_年同比',
                                        f'{dim_name}_周环比'
                                    ])

                                create_header_row(all_platform_ws, headers, current_row + 1)
                                current_row += 2

                                # 获取周数据（假设所有维度的周数据长度一致）
                                weeks_data = valid_dimensions[0].get('trend', [])

                                # 按周生成数据行
                                for week_idx, week_data in enumerate(weeks_data):
                                    week_no = week_data.get('week', week_idx + 1)

                                    # 计算该周所有维度的GMV总和（用于占比计算）
                                    week_total_gmv = 0
                                    for dim_item in valid_dimensions:
                                        if week_idx < len(dim_item.get('trend', [])):
                                            week_total_gmv += dim_item['trend'][week_idx].get('gmv', 0)

                                    # 填充数据
                                    all_platform_ws.cell(row=current_row, column=1, value=f'第{week_no}周')
                                    col = 1

                                    for dim_item in valid_dimensions:
                                        if week_idx < len(dim_item.get('trend', [])):
                                            trend_data = dim_item['trend'][week_idx]
                                            gmv = trend_data.get('gmv', 0)
                                            ratio = (gmv / week_total_gmv * 100) if week_total_gmv > 0 else 0
                                            yoy = trend_data.get('yoy', 0)
                                            wow = trend_data.get('wow', 0)

                                            all_platform_ws.cell(row=current_row, column=col + 1, value=format_number(gmv, is_currency=True))
                                            all_platform_ws.cell(row=current_row, column=col + 2, value=format_number(ratio, is_percentage=True))
                                            all_platform_ws.cell(row=current_row, column=col + 3, value=format_number(yoy, is_percentage=True))
                                            all_platform_ws.cell(row=current_row, column=col + 4, value=format_number(wow, is_percentage=True))
                                        col += 4

                                    current_row += 1

                                # 插入维度分析图表截图（如果有的话）
                                dimension_chart_mapping = {
                                    '子品牌': 'subbrand_chart',
                                    '品线': 'productline_chart',
                                    '渠道类型': 'channel_chart',
                                    '大区': 'region_chart'
                                }
                                dimension_chart_key = dimension_chart_mapping.get(dimension)

                                # 优先查找全平台特定的图表，然后查找通用图表
                                all_platform_chart_key = f"全平台_{dimension_chart_key}"
                                chart_data = None

                                if chart_images:
                                    if chart_images.get(all_platform_chart_key):
                                        chart_data = chart_images[all_platform_chart_key]
                                        logger.info(f"使用全平台特定图表: {all_platform_chart_key}")
                                    elif chart_images.get(dimension_chart_key):
                                        chart_data = chart_images[dimension_chart_key]
                                        logger.info(f"使用通用图表: {dimension_chart_key}")

                                if chart_data:
                                    current_row = insert_chart_image(all_platform_ws, chart_data, current_row + 1, 1, f'全平台{dimension}分析图表')
                                else:
                                    logger.warning(f"未找到全平台{dimension}维度的图表数据")
                                    current_row += 2
                            else:
                                logger.info(f"全平台{dimension}维度无有效数据")
                        else:
                            logger.info(f"全平台{dimension}维度数据为空")
                    except Exception as e:
                        logger.error(f"获取全平台{dimension}维度趋势数据失败: {e}")
                        continue

                # 4. Top10数据 - 只显示有数据的类别
                try:
                    top10_data = get_top10_data_real(brand, '全平台')
                    if top10_data:
                        categories = ['products', 'stores', 'cities']
                        category_names = ['商品', '零售商', '城市']

                        for cat, cat_name in zip(categories, category_names):
                            if cat in top10_data and top10_data[cat]:
                                # 过滤掉GMV为0或空的数据
                                valid_items = [item for item in top10_data[cat][:10]
                                             if item.get('gmv') and item.get('gmv') > 0]

                                if valid_items:
                                    all_platform_ws.cell(row=current_row, column=1, value=f'Top10{cat_name}分析').font = Font(size=12, bold=True)
                                    current_row += 1

                                    # 创建表头
                                    if cat == 'products':
                                        headers = ['排名', '商品名称', 'GMV', '年同比', '周环比']
                                    elif cat == 'stores':
                                        headers = ['排名', '零售商名称', 'GMV', '年同比', '周环比']
                                    else:  # cities
                                        headers = ['排名', '城市名称', 'GMV', '年同比', '周环比']

                                    create_header_row(all_platform_ws, headers, current_row + 1)
                                    current_row += 2

                                    # 填充数据
                                    for i, item in enumerate(valid_items, 1):
                                        all_platform_ws.cell(row=current_row, column=1, value=i)

                                        # 根据类别选择名称字段
                                        if cat == 'products':
                                            name = item.get('product_name', '')
                                        elif cat == 'stores':
                                            name = item.get('store_name', '')
                                        else:  # cities
                                            name = item.get('city_name', '')

                                        all_platform_ws.cell(row=current_row, column=2, value=name)
                                        all_platform_ws.cell(row=current_row, column=3, value=format_number(item.get('gmv'), is_currency=True))
                                        all_platform_ws.cell(row=current_row, column=4, value=format_number(item.get('yoy'), is_percentage=True))
                                        all_platform_ws.cell(row=current_row, column=5, value=format_number(item.get('wow'), is_percentage=True))
                                        current_row += 1

                                    current_row += 2
                    else:
                        logger.info("全平台Top10数据为空")
                except Exception as e:
                    logger.error(f"获取全平台Top10数据失败: {e}")
                    pass

                # 调整列宽
                all_platform_ws.column_dimensions['A'].width = 15
                all_platform_ws.column_dimensions['B'].width = 15
                all_platform_ws.column_dimensions['C'].width = 12
                all_platform_ws.column_dimensions['D'].width = 12

        except Exception as e:
            logger.error(f"创建全平台工作表失败: {e}")

    # 如果没有任何平台有数据，创建一个默认工作表
    if not has_data:
        default_ws = wb.create_sheet(title='数据说明')
        default_ws.cell(row=1, column=1, value='交易表现数据').font = Font(size=14, bold=True)
        default_ws.cell(row=3, column=1, value=f'第{week}周暂无交易数据')
        default_ws.cell(row=4, column=1, value=f'品牌筛选: {brand if brand else "全品牌"}')

    # 如果没有AI洞察内容但有数据，创建一个空的AI洞察工作表
    if not ai_insights and has_data:
        try:
            ai_ws = wb.create_sheet(title='AI洞察分析')
            ai_ws.cell(row=1, column=1, value='AI洞察分析').font = Font(size=16, bold=True)
            ai_ws.cell(row=3, column=1, value='本工作表用于存放AI洞察内容')
            ai_ws.cell(row=4, column=1, value='可通过系统生成AI洞察后复制粘贴到此处')
            ai_ws.column_dimensions['A'].width = 100
        except Exception as e:
            logger.error(f"创建AI洞察工作表失败: {e}")

    return wb

def export_user_data(week, brand=''):
    """导出用户表现数据"""
    logger.info(f"开始导出用户表现数据 - 周次: {week}, 品牌: {brand}")

    wb = Workbook()
    platforms = ['美团', '饿了么', '京东到家', '多点', '淘鲜达']
    wb.remove(wb.active)

    # 用户数据通常不按平台分，所以我们只创建一个汇总工作表
    try:
        summary_data = get_user_data_summary_real(week, brand)
        if summary_data:
            ws = wb.create_sheet(title='用户数据汇总')

            # 1. 用户概览数据
            ws.cell(row=1, column=1, value="用户表现概览").font = Font(size=14, bold=True)

            headers = ['用户类型', '数量', '占比', '周环比', 'GMV', 'GMV占比', 'GMV周环比', '客单价', '客单价周环比']
            create_header_row(ws, headers, 3)

            # 新客数据
            new_user = summary_data.get('new_user', {})
            ws.cell(row=4, column=1, value='新客')
            ws.cell(row=4, column=2, value=format_number(new_user.get('count')))
            ws.cell(row=4, column=3, value=format_number(new_user.get('count_ratio'), is_percentage=True))
            ws.cell(row=4, column=4, value=format_number(new_user.get('count_wow'), is_percentage=True))
            ws.cell(row=4, column=5, value=format_number(new_user.get('gmv'), is_currency=True))
            ws.cell(row=4, column=6, value=format_number(new_user.get('gmv_ratio'), is_percentage=True))
            ws.cell(row=4, column=7, value=format_number(new_user.get('gmv_wow'), is_percentage=True))
            ws.cell(row=4, column=8, value=format_number(new_user.get('avg_price'), is_currency=True))
            ws.cell(row=4, column=9, value=format_number(new_user.get('avg_price_wow'), is_percentage=True))

            # 老客数据
            old_user = summary_data.get('old_user', {})
            ws.cell(row=5, column=1, value='老客')
            ws.cell(row=5, column=2, value=format_number(old_user.get('count')))
            ws.cell(row=5, column=3, value=format_number(old_user.get('count_ratio'), is_percentage=True))
            ws.cell(row=5, column=4, value=format_number(old_user.get('count_wow'), is_percentage=True))
            ws.cell(row=5, column=5, value=format_number(old_user.get('gmv'), is_currency=True))
            ws.cell(row=5, column=6, value=format_number(old_user.get('gmv_ratio'), is_percentage=True))
            ws.cell(row=5, column=7, value=format_number(old_user.get('gmv_wow'), is_percentage=True))
            ws.cell(row=5, column=8, value=format_number(old_user.get('avg_price'), is_currency=True))
            ws.cell(row=5, column=9, value=format_number(old_user.get('avg_price_wow'), is_percentage=True))

            # 插入用户概览卡片截图（如果有的话）
            current_row = 6
            if chart_images:
                for i in range(1, 6):  # 用户指标卡片
                    card_key = f'user_card_{i}'
                    if chart_images.get(card_key):
                        current_row = insert_chart_image(ws, chart_images[card_key], current_row, 1, f'用户指标卡片{i}')
                    else:
                        current_row += 1

            # 2. 用户趋势数据
            trends_data = get_user_data_trends_real(brand)
            if trends_data and trends_data.get('trend_data'):
                ws.cell(row=7, column=1, value='用户趋势分析').font = Font(size=12, bold=True)

                trend_headers = ['周次', '新客数量', '老客数量', '新客GMV', '老客GMV', '新客客单价', '老客客单价']
                create_header_row(ws, trend_headers, 9)

                for i, trend in enumerate(trends_data['trend_data'], 10):
                    new_user_data = trend.get('new_user', {})
                    old_user_data = trend.get('old_user', {})

                    ws.cell(row=i, column=1, value=f"第{trend.get('week')}周")
                    ws.cell(row=i, column=2, value=format_number(new_user_data.get('usernum')))
                    ws.cell(row=i, column=3, value=format_number(old_user_data.get('usernum')))
                    ws.cell(row=i, column=4, value=format_number(new_user_data.get('gmv'), is_currency=True))
                    ws.cell(row=i, column=5, value=format_number(old_user_data.get('gmv'), is_currency=True))
                    ws.cell(row=i, column=6, value=format_number(new_user_data.get('avg_price'), is_currency=True))
                    ws.cell(row=i, column=7, value=format_number(old_user_data.get('avg_price'), is_currency=True))

                # 插入用户趋势图表截图（如果有的话）
                current_row = 18
                if chart_images and chart_images.get('user_trend_chart'):
                    current_row = insert_chart_image(ws, chart_images['user_trend_chart'], current_row, 1, '用户趋势图表')
                else:
                    current_row += 2

            # 3. 用户明细数据
            detail_data = get_user_data_detail_real(brand)
            if detail_data:
                ws.cell(row=20, column=1, value='用户明细数据').font = Font(size=12, bold=True)

                detail_headers = ['周次', '新客数量', '新客占比', '新客年同比', '新客周环比',
                               '老客数量', '老客占比', '老客年同比', '老客周环比',
                               '新客GMV', '新客GMV占比', '老客GMV', '老客GMV占比']
                create_header_row(ws, detail_headers, 22)

                for i, detail in enumerate(detail_data, 23):
                    ws.cell(row=i, column=1, value=f"第{detail.get('week')}周")
                    ws.cell(row=i, column=2, value=format_number(detail.get('new_user_count')))
                    ws.cell(row=i, column=3, value=format_number(detail.get('new_user_ratio'), is_percentage=True))
                    ws.cell(row=i, column=4, value=format_number(detail.get('new_user_yoy'), is_percentage=True))
                    ws.cell(row=i, column=5, value=format_number(detail.get('new_user_wow'), is_percentage=True))
                    ws.cell(row=i, column=6, value=format_number(detail.get('old_user_count')))
                    ws.cell(row=i, column=7, value=format_number(detail.get('old_user_ratio'), is_percentage=True))
                    ws.cell(row=i, column=8, value=format_number(detail.get('old_user_yoy'), is_percentage=True))
                    ws.cell(row=i, column=9, value=format_number(detail.get('old_user_wow'), is_percentage=True))
                    ws.cell(row=i, column=10, value=format_number(detail.get('new_user_gmv'), is_currency=True))
                    ws.cell(row=i, column=11, value=format_number(detail.get('new_user_gmv_ratio'), is_percentage=True))
                    ws.cell(row=i, column=12, value=format_number(detail.get('old_user_gmv'), is_currency=True))
                    ws.cell(row=i, column=13, value=format_number(detail.get('old_user_gmv_ratio'), is_percentage=True))
        else:
            # 如果没有数据，创建说明工作表
            ws = wb.create_sheet(title='数据说明')
            ws.cell(row=1, column=1, value='用户表现数据').font = Font(size=14, bold=True)
            ws.cell(row=3, column=1, value=f'第{week}周暂无用户数据')
            ws.cell(row=4, column=1, value=f'品牌筛选: {brand if brand else "全品牌"}')

    except Exception as e:
        logger.error(f"导出用户数据失败: {e}")
        # 创建错误说明工作表
        ws = wb.create_sheet(title='错误说明')
        ws.cell(row=1, column=1, value='用户表现数据').font = Font(size=14, bold=True)
        ws.cell(row=3, column=1, value=f'第{week}周用户数据导出失败')
        ws.cell(row=4, column=1, value=f'错误信息: {str(e)}')

    return wb

def export_activity_data(week, brand='', chart_images=None):
    """导出活动表现数据"""
    logger.info(f"开始导出活动表现数据 - 周次: {week}, 品牌: {brand}")
    if chart_images:
        logger.info(f"接收到活动图表截图: {list(chart_images.keys())}")

    wb = Workbook()
    platforms = ['美团', '饿了么', '京东到家', '多点', '淘鲜达']
    wb.remove(wb.active)

    has_data = False

    for platform in platforms:
        try:
            # 检查平台是否有数据
            summary_data = get_activity_summary_real(week, brand, platform)
            if not summary_data:
                logger.warning(f"{platform}平台活动数据为空")
                continue

            has_data = True

            ws = wb.create_sheet(title=platform)

            # 1. 活动概览数据
            ws.cell(row=1, column=1, value=f"{platform} - 活动表现概览").font = Font(size=14, bold=True)

            headers = ['指标', '数值', '年同比']
            create_header_row(ws, headers, 3)

            ws.cell(row=4, column=1, value='活动GMV')
            ws.cell(row=4, column=2, value=format_number(summary_data.get('activity_gmv', {}).get('value'), is_currency=True))
            ws.cell(row=4, column=3, value=format_number(summary_data.get('activity_gmv', {}).get('yoy'), is_percentage=True))

            ws.cell(row=5, column=1, value='活动GMV占比')
            ws.cell(row=5, column=2, value=format_number(summary_data.get('activity_gmv_ratio', {}).get('value'), is_percentage=True))
            ws.cell(row=5, column=3, value=format_number(summary_data.get('activity_gmv_ratio', {}).get('yoy'), is_percentage=True))

            ws.cell(row=6, column=1, value='核销金额')
            ws.cell(row=6, column=2, value=format_number(summary_data.get('verification_amount', {}).get('value'), is_currency=True))
            ws.cell(row=6, column=3, value=format_number(summary_data.get('verification_amount', {}).get('yoy'), is_percentage=True))

            ws.cell(row=7, column=1, value='活动费比')
            ws.cell(row=7, column=2, value=format_number(summary_data.get('activity_cost_ratio', {}).get('value'), is_percentage=True))
            ws.cell(row=7, column=3, value=format_number(summary_data.get('activity_cost_ratio', {}).get('yoy'), is_percentage=True))

            ws.cell(row=8, column=1, value='总费比')
            ws.cell(row=8, column=2, value=format_number(summary_data.get('total_cost_ratio', {}).get('value'), is_percentage=True))
            ws.cell(row=8, column=3, value=format_number(summary_data.get('total_cost_ratio', {}).get('yoy'), is_percentage=True))

            # 插入活动概览卡片截图（如果有的话）
            current_row = 9
            if chart_images:
                for i in range(1, 6):  # 5个活动指标卡片
                    card_key = f'activity_card_{i}'
                    if chart_images.get(card_key):
                        current_row = insert_chart_image(ws, chart_images[card_key], current_row, 1, f'{platform}活动指标卡片{i}')
                    else:
                        current_row += 1

            # 2. 活动趋势数据
            trends_data = get_activity_trends_real(brand, None, platform)
            if trends_data and trends_data.get('trend_data'):
                ws.cell(row=10, column=1, value='活动趋势分析').font = Font(size=12, bold=True)

                trend_headers = ['周次', '活动GMV', '活动GMV占比', '核销金额', '活动费比', '总费比']
                create_header_row(ws, trend_headers, 12)

                for i, trend in enumerate(trends_data['trend_data'], 13):
                    ws.cell(row=i, column=1, value=f"第{trend.get('week')}周")
                    ws.cell(row=i, column=2, value=format_number(trend.get('activity_gmv'), is_currency=True))
                    ws.cell(row=i, column=3, value=format_number(trend.get('activity_gmv_ratio'), is_percentage=True))
                    ws.cell(row=i, column=4, value=format_number(trend.get('verification_amount'), is_currency=True))
                    ws.cell(row=i, column=5, value=format_number(trend.get('activity_cost_ratio'), is_percentage=True))
                    ws.cell(row=i, column=6, value=format_number(trend.get('total_cost_ratio'), is_percentage=True))

                # 插入活动趋势图表截图（如果有的话）
                current_row = 22
                if chart_images and chart_images.get('activity_trend_chart'):
                    current_row = insert_chart_image(ws, chart_images['activity_trend_chart'], current_row, 1, f'{platform}活动趋势图表')
                else:
                    current_row += 2  # 如果没有图表，留出一些空间

            # 3. 活动明细数据 - 按周次
            detail_weeks = get_activity_detail_real_weeks(brand, platform)
            if detail_weeks:
                ws.cell(row=25, column=1, value='活动明细数据（按周次）').font = Font(size=12, bold=True)

                detail_headers = ['周次', '活动GMV', '活动GMV占比', '年同比', '周环比',
                               '核销金额', '活动费比', '总费比']
                create_header_row(ws, detail_headers, 27)

                for i, detail in enumerate(detail_weeks, 28):
                    ws.cell(row=i, column=1, value=f"第{detail.get('week')}周")
                    ws.cell(row=i, column=2, value=format_number(detail.get('activity_gmv'), is_currency=True))
                    ws.cell(row=i, column=3, value=format_number(detail.get('activity_gmv_ratio'), is_percentage=True))
                    ws.cell(row=i, column=4, value=format_number(detail.get('activity_gmv_yoy'), is_percentage=True))
                    ws.cell(row=i, column=5, value=format_number(detail.get('activity_gmv_wow'), is_percentage=True))
                    ws.cell(row=i, column=6, value=format_number(detail.get('verification_amount'), is_currency=True))
                    ws.cell(row=i, column=7, value=format_number(detail.get('activity_cost_ratio'), is_percentage=True))
                    ws.cell(row=i, column=8, value=format_number(detail.get('total_cost_ratio'), is_percentage=True))

            # 4. 活动明细数据 - 按维度
            dimensions = ['子品牌', '品线', '渠道类型', '大区']
            current_row = 40

            for dimension in dimensions:
                try:
                    dim_data = get_activity_detail_real_dimension(dimension, f'2024-W{week}', brand, 1, 100, platform)
                    if dim_data and dim_data.get('data'):
                        ws.cell(row=current_row, column=1, value=f'活动数据-{dimension}维度').font = Font(size=12, bold=True)
                        current_row += 1

                        dim_headers = ['维度值', '活动GMV', '活动GMV占比', '核销金额', '活动费比', '总费比']
                        create_header_row(ws, dim_headers, current_row + 1)
                        current_row += 2

                        for item in dim_data['data']:
                            ws.cell(row=current_row, column=1, value=item.get('dimension'))
                            ws.cell(row=current_row, column=2, value=format_number(item.get('activity_gmv'), is_currency=True))
                            ws.cell(row=current_row, column=3, value=format_number(item.get('activity_gmv_ratio'), is_percentage=True))
                            ws.cell(row=current_row, column=4, value=format_number(item.get('verification_amount'), is_currency=True))
                            ws.cell(row=current_row, column=5, value=format_number(item.get('activity_cost_ratio'), is_percentage=True))
                            ws.cell(row=current_row, column=6, value=format_number(item.get('total_cost_ratio'), is_percentage=True))
                            current_row += 1

                        current_row += 2
                except Exception as e:
                    logger.error(f"获取活动{dimension}维度数据失败: {e}")
                    continue

        except Exception as e:
            logger.error(f"导出{platform}活动数据失败: {e}")
            continue

    # 如果没有任何平台有数据，创建一个默认工作表
    if not has_data:
        default_ws = wb.create_sheet(title='数据说明')
        default_ws.cell(row=1, column=1, value='活动表现数据').font = Font(size=14, bold=True)
        default_ws.cell(row=3, column=1, value=f'第{week}周暂无活动数据')
        default_ws.cell(row=4, column=1, value=f'品牌筛选: {brand if brand else "全品牌"}')

    return wb

def export_rtb_data(week, brand='', chart_images=None):
    """导出RTB表现数据"""
    logger.info(f"开始导出RTB表现数据 - 周次: {week}, 品牌: {brand}")
    if chart_images:
        logger.info(f"接收到RTB图表截图: {list(chart_images.keys())}")

    wb = Workbook()
    wb.remove(wb.active)

    try:
        # RTB数据通常不按平台分，创建一个汇总工作表
        summary_data = get_rtb_summary_real(week, brand)
        if summary_data:
            ws = wb.create_sheet(title='RTB投放汇总')

            # 1. RTB概览数据
            ws.cell(row=1, column=1, value="RTB投放概览").font = Font(size=14, bold=True)

            headers = ['指标', '数值', '周环比', '进度/ROI']
            create_header_row(ws, headers, 3)

            ws.cell(row=4, column=1, value='消耗金额')
            ws.cell(row=4, column=2, value=format_number(summary_data.get('consumption', {}).get('amount'), is_currency=True))
            ws.cell(row=4, column=3, value=format_number(summary_data.get('consumption', {}).get('wow'), is_percentage=True))
            ws.cell(row=4, column=4, value=format_number(summary_data.get('consumption', {}).get('progress'), is_percentage=True))

            ws.cell(row=5, column=1, value='T1引导金额')
            ws.cell(row=5, column=2, value=format_number(summary_data.get('t1_guided_amount', {}).get('amount'), is_currency=True))
            ws.cell(row=5, column=3, value=format_number(summary_data.get('t1_guided_amount', {}).get('wow'), is_percentage=True))
            ws.cell(row=5, column=4, value='-')

            ws.cell(row=6, column=1, value='ROI')
            ws.cell(row=6, column=2, value=format_number(summary_data.get('roi', {}).get('value')))
            ws.cell(row=6, column=3, value=format_number(summary_data.get('roi', {}).get('wow'), is_percentage=True))
            ws.cell(row=6, column=4, value='-')

            # 插入RTB概览卡片截图（如果有的话）
            current_row = 7
            if chart_images:
                for i in range(1, 6):  # RTB指标卡片
                    card_key = f'rtb_card_{i}'
                    if chart_images.get(card_key):
                        current_row = insert_chart_image(ws, chart_images[card_key], current_row, 1, f'RTB指标卡片{i}')
                    else:
                        current_row += 1

            # 2. RTB趋势数据
            trends_data = get_rtb_trends_real(brand)
            if trends_data and trends_data.get('trend_data'):
                ws.cell(row=8, column=1, value='RTB趋势分析').font = Font(size=12, bold=True)

                trend_headers = ['周次', '消耗金额', '消耗周环比', 'T1引导金额', 'T1引导周环比', 'ROI', 'ROI周环比']
                create_header_row(ws, trend_headers, 10)

                for i, trend in enumerate(trends_data['trend_data'], 11):
                    ws.cell(row=i, column=1, value=f"第{trend.get('week')}周")
                    ws.cell(row=i, column=2, value=format_number(trend.get('consumption'), is_currency=True))
                    ws.cell(row=i, column=3, value=format_number(trend.get('consumption_wow'), is_percentage=True))
                    ws.cell(row=i, column=4, value=format_number(trend.get('t1_guided_amount'), is_currency=True))
                    ws.cell(row=i, column=5, value=format_number(trend.get('t1_guided_amount_wow'), is_percentage=True))
                    ws.cell(row=i, column=6, value=format_number(trend.get('roi')))
                    ws.cell(row=i, column=7, value=format_number(trend.get('roi_wow'), is_percentage=True))

                # 插入RTB趋势图表截图（如果有的话）
                current_row = 20
                if chart_images and chart_images.get('rtb_trend_chart'):
                    current_row = insert_chart_image(ws, chart_images['rtb_trend_chart'], current_row, 1, 'RTB趋势图表')
                else:
                    current_row += 2

            # 3. RTB明细数据 - 按维度
            dimensions = ['整体', '计划', '商品']
            current_row = 25

            for dimension in dimensions:
                try:
                    detail_data = get_rtb_detail_real(dimension, week, brand)
                    if detail_data and detail_data.get('data'):
                        ws.cell(row=current_row, column=1, value=f'RTB明细-{dimension}维度').font = Font(size=12, bold=True)
                        current_row += 1

                        if dimension == '整体':
                            detail_headers = ['维度值', '曝光量', '点击量', 'CTR', '订单量', '订单转化率',
                                           '引导金额', '预算', '消耗', '消耗进度', 'CPM', 'CPC', 'ROI']
                        elif dimension == '计划':
                            detail_headers = ['计划名称', '曝光量', '点击量', 'CTR', '订单量', '订单转化率',
                                           '引导金额', '预算', '消耗', '消耗进度', 'CPM', 'CPC', '新客成本', 'ROI']
                        else:  # 商品
                            detail_headers = ['商品名称', '曝光量', '点击量', 'CTR', '订单量', '订单转化率',
                                           '引导金额', '预算', '消耗', '消耗进度', 'CPM', 'CPC', 'ROI']

                        create_header_row(ws, detail_headers, current_row + 1)
                        current_row += 2

                        for item in detail_data['data']:
                            col = 1
                            ws.cell(row=current_row, column=col, value=item.get('dimension'))
                            col += 1
                            ws.cell(row=current_row, column=col, value=format_number(item.get('exposure')))
                            col += 1
                            ws.cell(row=current_row, column=col, value=format_number(item.get('click')))
                            col += 1
                            ws.cell(row=current_row, column=col, value=format_number(item.get('ctr'), is_percentage=True))
                            col += 1
                            ws.cell(row=current_row, column=col, value=format_number(item.get('order_volume')))
                            col += 1
                            ws.cell(row=current_row, column=col, value=format_number(item.get('order_conversion_rate'), is_percentage=True))
                            col += 1
                            ws.cell(row=current_row, column=col, value=format_number(item.get('guided_amount'), is_currency=True))
                            col += 1
                            ws.cell(row=current_row, column=col, value=format_number(item.get('budget'), is_currency=True))
                            col += 1
                            ws.cell(row=current_row, column=col, value=format_number(item.get('consumption'), is_currency=True))
                            col += 1
                            ws.cell(row=current_row, column=col, value=format_number(item.get('consumption_progress'), is_percentage=True))
                            col += 1
                            ws.cell(row=current_row, column=col, value=format_number(item.get('cpm'), is_currency=True))
                            col += 1
                            ws.cell(row=current_row, column=col, value=format_number(item.get('cpc'), is_currency=True))
                            col += 1

                            if dimension == '计划' and 'new_user_cost' in item:
                                ws.cell(row=current_row, column=col, value=format_number(item.get('new_user_cost'), is_currency=True))
                                col += 1

                            ws.cell(row=current_row, column=col, value=format_number(item.get('roi')))
                            current_row += 1

                        current_row += 2
                except Exception as e:
                    logger.error(f"获取RTB{dimension}维度数据失败: {e}")
                    continue
        else:
            # 如果没有数据，创建说明工作表
            ws = wb.create_sheet(title='数据说明')
            ws.cell(row=1, column=1, value='RTB投放数据').font = Font(size=14, bold=True)
            ws.cell(row=3, column=1, value=f'第{week}周暂无RTB数据')
            ws.cell(row=4, column=1, value=f'品牌筛选: {brand if brand else "全品牌"}')

    except Exception as e:
        logger.error(f"导出RTB数据失败: {e}")
        # 创建错误说明工作表
        ws = wb.create_sheet(title='错误说明')
        ws.cell(row=1, column=1, value='RTB投放数据').font = Font(size=14, bold=True)
        ws.cell(row=3, column=1, value=f'第{week}周RTB数据导出失败')
        ws.cell(row=4, column=1, value=f'错误信息: {str(e)}')

    return wb

def export_supply_data(week, brand='', chart_images=None):
    """导出供给表现数据"""
    logger.info(f"开始导出供给表现数据 - 周次: {week}, 品牌: {brand}")
    if chart_images:
        logger.info(f"接收到供给图表截图: {list(chart_images.keys())}")

    wb = Workbook()
    wb.remove(wb.active)

    try:
        # 供给数据通常不按平台分，创建一个汇总工作表
        summary_data = get_supply_summary_real(week, brand)
        if summary_data:
            ws = wb.create_sheet(title='供给表现汇总')

            # 1. 供给概览数据
            ws.cell(row=1, column=1, value="供给表现概览").font = Font(size=14, bold=True)

            headers = ['指标', '数值', '周环比']
            create_header_row(ws, headers, 3)

            ws.cell(row=4, column=1, value='GMV')
            ws.cell(row=4, column=2, value=format_number(summary_data.get('gmv', {}).get('value'), is_currency=True))
            ws.cell(row=4, column=3, value=format_number(summary_data.get('gmv', {}).get('wow'), is_percentage=True))

            ws.cell(row=5, column=1, value='门店数量')
            ws.cell(row=5, column=2, value=format_number(summary_data.get('store_count', {}).get('value')))
            ws.cell(row=5, column=3, value=format_number(summary_data.get('store_count', {}).get('wow'), is_percentage=True))

            ws.cell(row=6, column=1, value='门店渗透率')
            ws.cell(row=6, column=2, value=format_number(summary_data.get('store_penetration', {}).get('value'), is_percentage=True))
            ws.cell(row=6, column=3, value=format_number(summary_data.get('store_penetration', {}).get('wow'), is_percentage=True))

            ws.cell(row=7, column=1, value='门店活跃率')
            ws.cell(row=7, column=2, value=format_number(summary_data.get('store_activity_rate', {}).get('value'), is_percentage=True))
            ws.cell(row=7, column=3, value=format_number(summary_data.get('store_activity_rate', {}).get('wow'), is_percentage=True))

            ws.cell(row=8, column=1, value='平均SKU数/门店')
            ws.cell(row=8, column=2, value=format_number(summary_data.get('avg_sku_per_store', {}).get('value')))
            ws.cell(row=8, column=3, value=format_number(summary_data.get('avg_sku_per_store', {}).get('wow'), is_percentage=True))

            ws.cell(row=9, column=1, value='SKU缺货率')
            ws.cell(row=9, column=2, value=format_number(summary_data.get('sku_sold_out_rate', {}).get('value'), is_percentage=True))
            ws.cell(row=9, column=3, value=format_number(summary_data.get('sku_sold_out_rate', {}).get('wow'), is_percentage=True))

            # 插入供给概览卡片截图（如果有的话）
            current_row = 10
            if chart_images:
                for i in range(1, 6):  # 供给指标卡片
                    card_key = f'supply_card_{i}'
                    if chart_images.get(card_key):
                        current_row = insert_chart_image(ws, chart_images[card_key], current_row, 1, f'供给指标卡片{i}')
                    else:
                        current_row += 1

            # 2. 供给趋势数据
            trends_data = get_supply_trends_real(brand)
            if trends_data and trends_data.get('trend_data'):
                ws.cell(row=11, column=1, value='供给趋势分析').font = Font(size=12, bold=True)

                trend_headers = ['周次', 'GMV', 'GMV周环比', '门店数量', '门店数量周环比',
                               '门店渗透率', '门店渗透率周环比', '门店活跃率', '门店活跃率周环比',
                               '平均SKU数/门店', 'SKU数周环比', 'SKU缺货率', 'SKU缺货率周环比']
                create_header_row(ws, trend_headers, 13)

                for i, trend in enumerate(trends_data['trend_data'], 14):
                    ws.cell(row=i, column=1, value=f"第{trend.get('week')}周")
                    ws.cell(row=i, column=2, value=format_number(trend.get('gmv'), is_currency=True))
                    ws.cell(row=i, column=3, value=format_number(trend.get('gmv_wow'), is_percentage=True))
                    ws.cell(row=i, column=4, value=format_number(trend.get('store_count')))
                    ws.cell(row=i, column=5, value=format_number(trend.get('store_count_wow'), is_percentage=True))
                    ws.cell(row=i, column=6, value=format_number(trend.get('store_penetration'), is_percentage=True))
                    ws.cell(row=i, column=7, value=format_number(trend.get('store_penetration_wow'), is_percentage=True))
                    ws.cell(row=i, column=8, value=format_number(trend.get('store_activity_rate'), is_percentage=True))
                    ws.cell(row=i, column=9, value=format_number(trend.get('store_activity_rate_wow'), is_percentage=True))
                    ws.cell(row=i, column=10, value=format_number(trend.get('avg_sku_per_store')))
                    ws.cell(row=i, column=11, value=format_number(trend.get('avg_sku_per_store_wow'), is_percentage=True))
                    ws.cell(row=i, column=12, value=format_number(trend.get('sku_sold_out_rate'), is_percentage=True))
                    ws.cell(row=i, column=13, value=format_number(trend.get('sku_sold_out_rate_wow'), is_percentage=True))

                # 插入供给趋势图表截图（如果有的话）
                current_row = 25
                if chart_images and chart_images.get('supply_trend_chart'):
                    current_row = insert_chart_image(ws, chart_images['supply_trend_chart'], current_row, 1, '供给趋势图表')
                else:
                    current_row += 2

            # 3. 供给明细数据 - 按维度
            dimensions = ['整体', '重点渠道', '重点商品']
            current_row = 30

            for dimension in dimensions:
                try:
                    detail_data = get_supply_detail_real(dimension, brand, week)
                    if detail_data and detail_data.get('data'):
                        ws.cell(row=current_row, column=1, value=f'供给明细-{dimension}维度').font = Font(size=12, bold=True)
                        current_row += 1

                        detail_headers = ['维度值', 'GMV', 'GMV占比', 'GMV周环比', '门店数量', '门店数量周环比',
                                        '门店渗透率', '门店渗透率周环比', '门店活跃率', '门店活跃率周环比',
                                        '平均SKU数/门店', 'SKU数周环比', 'SKU缺货率']
                        create_header_row(ws, detail_headers, current_row + 1)
                        current_row += 2

                        for item in detail_data['data']:
                            ws.cell(row=current_row, column=1, value=item.get('dimension'))
                            ws.cell(row=current_row, column=2, value=format_number(item.get('gmv'), is_currency=True))
                            ws.cell(row=current_row, column=3, value=format_number(item.get('gmv_ratio'), is_percentage=True))
                            ws.cell(row=current_row, column=4, value=format_number(item.get('gmv_wow'), is_percentage=True))
                            ws.cell(row=current_row, column=5, value=format_number(item.get('store_count')))
                            ws.cell(row=current_row, column=6, value=format_number(item.get('store_count_wow'), is_percentage=True))
                            ws.cell(row=current_row, column=7, value=format_number(item.get('store_penetration'), is_percentage=True))
                            ws.cell(row=current_row, column=8, value=format_number(item.get('store_penetration_wow'), is_percentage=True))
                            ws.cell(row=current_row, column=9, value=format_number(item.get('store_activity_rate'), is_percentage=True))
                            ws.cell(row=current_row, column=10, value=format_number(item.get('store_activity_rate_wow'), is_percentage=True))
                            ws.cell(row=current_row, column=11, value=format_number(item.get('avg_sku_per_store')))
                            ws.cell(row=current_row, column=12, value=format_number(item.get('avg_sku_per_store_wow'), is_percentage=True))
                            ws.cell(row=current_row, column=13, value=format_number(item.get('sku_sold_out_rate'), is_percentage=True))
                            current_row += 1

                        current_row += 2
                except Exception as e:
                    logger.error(f"获取供给{dimension}维度数据失败: {e}")
                    continue
        else:
            # 如果没有数据，创建说明工作表
            ws = wb.create_sheet(title='数据说明')
            ws.cell(row=1, column=1, value='供给表现数据').font = Font(size=14, bold=True)
            ws.cell(row=3, column=1, value=f'第{week}周暂无供给数据')
            ws.cell(row=4, column=1, value=f'品牌筛选: {brand if brand else "全品牌"}')

    except Exception as e:
        logger.error(f"导出供给数据失败: {e}")
        # 创建错误说明工作表
        ws = wb.create_sheet(title='错误说明')
        ws.cell(row=1, column=1, value='供给表现数据').font = Font(size=14, bold=True)
        ws.cell(row=3, column=1, value=f'第{week}周供给数据导出失败')
        ws.cell(row=4, column=1, value=f'错误信息: {str(e)}')

    return wb

def generate_excel_filename(page_name, week, brand=''):
    """生成Excel文件名（用于显示）"""
    try:
        # 获取周次的日期范围
        current_year = datetime.now().year
        week_start, week_end = get_week_date_range(current_year, int(week))

        # 将斜杠替换为短横线，避免被当作目录分隔符
        safe_week_start = week_start.replace('/', '-')
        safe_week_end = week_end.replace('/', '-')

        # 构建显示用的文件名
        brand_part = f"_{brand}" if brand else ""
        filename = f"{page_name}第{week}周({safe_week_start}至{safe_week_end})周报{brand_part}.xlsx"

        return filename
    except Exception as e:
        logger.error(f"生成文件名失败: {e}")
        return f"{page_name}第{week}周周报.xlsx"

def generate_safe_filename(page_name, week, brand=''):
    """生成安全的文件名（避免中文路径问题）"""
    import uuid
    import re

    try:
        # 页面名称映射为英文
        page_mapping = {
            '交易表现': 'Trading',
            '用户表现': 'User',
            '活动表现': 'Activity',
            'RTB表现': 'RTB',
            '供给表现': 'Supply'
        }

        page_en = page_mapping.get(page_name, 'Data')

        # 清理品牌名称，只保留字母数字
        safe_brand = re.sub(r'[^\w]', '', brand) if brand else ''
        brand_part = f"_{safe_brand}" if safe_brand else ""

        # 生成唯一标识符避免冲突
        unique_id = str(uuid.uuid4())[:8]

        filename = f"{page_en}_Week{week}{brand_part}_{unique_id}.xlsx"

        return filename
    except Exception as e:
        logger.error(f"生成安全文件名失败: {e}")
        return f"Report_{uuid.uuid4().hex[:8]}.xlsx"

def export_page_data(page_name, week, brand='', ai_insights='', chart_images=None):
    """导出指定页面的数据"""
    try:
        logger.info(f"开始导出{page_name}数据 - 周次: {week}, 品牌: {brand}, AI洞察: {'有' if ai_insights else '无'}, 图表: {'有' if chart_images else '无'}")

        if page_name == '交易表现':
            wb = export_trading_data(week, brand, ai_insights, chart_images)
        elif page_name == '用户表现':
            wb = export_user_data(week, brand, chart_images)
        elif page_name == '活动表现':
            wb = export_activity_data(week, brand, chart_images)
        elif page_name == 'RTB表现':
            wb = export_rtb_data(week, brand, chart_images)
        elif page_name == '供给表现':
            wb = export_supply_data(week, brand, chart_images)
        else:
            raise ValueError(f"不支持的页面类型: {page_name}")

        # 生成安全的文件名（避免中文路径问题）
        safe_filename = generate_safe_filename(page_name, week, brand)
        display_filename = generate_excel_filename(page_name, week, brand)

        # 保存到临时文件
        temp_dir = tempfile.gettempdir()
        filepath = os.path.join(temp_dir, safe_filename)
        wb.save(filepath)

        # 验证文件是否成功创建
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"Excel文件创建失败: {filepath}")

        logger.info(f"Excel文件已生成: {filepath}")
        return filepath, display_filename

    except Exception as e:
        logger.error(f"导出{page_name}数据失败: {e}")
        raise e
