#!/usr/bin/env python3
"""
调试API响应内容
"""

import requests
import json

def debug_api_response():
    """调试API响应内容"""
    print("=== 调试API响应内容 ===")
    
    base_url = "http://localhost:5002/api"
    
    try:
        response = requests.get(f"{base_url}/trading/summary", params={"week": 30})
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应数据结构:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
        else:
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"异常: {e}")

if __name__ == "__main__":
    debug_api_response()
