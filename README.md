# 周报自动化看板

这是一个基于Flask + React的周报自动化看板系统，主要用于展示交易数据分析。

## 项目结构

```
Weekly_Report_Cursor/
├── backend/                 # Flask后端
│   ├── app.py              # 主应用文件
│   └── requirements.txt    # Python依赖
├── frontend/               # React前端
│   ├── public/            # 静态资源
│   ├── src/               # 源代码
│   │   ├── components/    # React组件
│   │   ├── services/      # API服务
│   │   ├── App.js         # 主应用组件
│   │   └── index.js       # 入口文件
│   └── package.json       # Node.js依赖
└── README.md              # 项目说明
```

## 功能特性

### 已实现功能

1. **顶部工具栏**
   - 周维度日期筛选控件
   - 导出按钮（长图、PDF、Excel）

2. **左侧导航栏**
   - 数据领域菜单（交易数据、活动数据、RTB投放、供给表现、用户数据）

3. **交易数据模块**
   - 平台筛选器（全平台、美团、饿了么、京东到家、多点、淘鲜达）
   - GMV概览卡片（Week xx、GMV MTD、GMV YTD）
   - 图表展示（饼图、复合图）
   - 维度分析（支持子品牌、品线、渠道类型、大区）
   - 明细数据表格（支持排序）
   - Top10排行榜（商品、城市、零售商）

### 技术栈

- **后端**: Python Flask + Flask-CORS
- **前端**: React + Ant Design + ECharts
- **数据**: 当前使用Mock数据

## 安装与运行

### 后端启动

```bash
cd backend
pip install -r requirements.txt
python app.py
```

后端将在 http://localhost:5001 启动

### 前端启动

```bash
cd frontend
npm install
npm start
```

前端将在 http://localhost:3000 启动

## API接口

### 交易数据接口

- `GET /api/trading/summary?week=45&platform=all` - 获取GMV概览数据
- `GET /api/trading/trends?platform=all` - 获取趋势图表数据
- `GET /api/trading/dimensions?type=子品牌` - 获取维度分析数据
- `GET /api/trading/top10?category=商品` - 获取Top10数据

### 健康检查

- `GET /api/health` - 系统健康状态检查

## 数据格式

### GMV概览数据
```json
{
  "week_data": {
    "week": 45,
    "gmv": 10000000.0,
    "yoy": 0.15,
    "wow": 0.08
  },
  "mtd_data": {
    "current": 30000000.0,
    "target": 40000000,
    "rate": 0.75,
    "yoy": 0.12,
    "mom": 0.05
  },
  "ytd_data": {
    "current": 450000000.0,
    "target": 600000000,
    "rate": 0.75,
    "yoy": 0.10
  }
}
```

## 后续开发计划

1. 替换Mock数据为真实数据源
2. 实现导出功能
3. 添加其他数据模块（活动数据、RTB投放等）
4. 用户权限管理
5. 数据缓存优化
6. 移动端适配优化

## 使用说明

### 快速启动

**方式一：使用启动脚本**
```bash
./start.sh
```

**方式二：分别启动**

启动后端：
```bash
cd backend
pip install -r requirements.txt
python app.py
```

启动前端：
```bash
cd frontend
npm install
npm start
```

### 访问地址

- 前端界面: http://localhost:3000
- 后端API: http://localhost:5001

## 开发说明

- 所有组件都支持响应式设计
- 使用Ant Design组件库保证UI一致性
- ECharts图表支持交互和动画
- 支持表格排序和筛选功能
- 颜色主题遵循Ant Design设计规范 