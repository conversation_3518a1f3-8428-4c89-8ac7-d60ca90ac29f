#!/usr/bin/env python3
"""
调试周环比计算差异问题
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from database import execute_query
from real_data_queries import get_week_date_range, get_supply_summary_real, get_supply_detail_real
import logging
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_summary_wow_calculation():
    """调试供给概览的周环比计算"""
    print("🔍 调试供给概览的周环比计算...")
    
    try:
        # 获取第28周和第27周的日期
        week28_start, week28_end = get_week_date_range(2025, 28)
        week27_start, week27_end = get_week_date_range(2025, 27)
        
        # 获取周六日期
        week28_start_date = datetime.strptime(week28_start, '%Y-%m-%d')
        week27_start_date = datetime.strptime(week27_start, '%Y-%m-%d')
        
        week28_saturday = week28_start_date + timedelta(days=5)
        week27_saturday = week27_start_date + timedelta(days=5)
        
        week28_saturday_yyyymmdd = week28_saturday.strftime('%Y%m%d')
        week27_saturday_yyyymmdd = week27_saturday.strftime('%Y%m%d')
        
        print(f"第28周周六: {week28_saturday_yyyymmdd}")
        print(f"第27周周六: {week27_saturday_yyyymmdd}")
        
        # 查询第28周数据
        sql_28 = f"""
        SELECT
            COALESCE(SUM(onsale_poi_num), 0) as store_count,
            COALESCE(AVG(poi_permeate_rate), 0) as store_penetration,
            COALESCE(AVG(poi_sold_rate), 0) as store_activity_rate,
            COALESCE(AVG(sku_sold_out_rate), 0) as sku_sold_out_rate
        FROM dws_mt_channel_city_details_daily_data_attribute_d
        WHERE ds = '{week28_saturday_yyyymmdd}'
        AND platform = '美团'
        AND collect_brand = '圣农'
        """
        
        result_28 = execute_query(sql_28)
        
        # 查询第27周数据
        sql_27 = f"""
        SELECT
            COALESCE(SUM(onsale_poi_num), 0) as store_count,
            COALESCE(AVG(poi_permeate_rate), 0) as store_penetration,
            COALESCE(AVG(poi_sold_rate), 0) as store_activity_rate,
            COALESCE(AVG(sku_sold_out_rate), 0) as sku_sold_out_rate
        FROM dws_mt_channel_city_details_daily_data_attribute_d
        WHERE ds = '{week27_saturday_yyyymmdd}'
        AND platform = '美团'
        AND collect_brand = '圣农'
        """
        
        result_27 = execute_query(sql_27)
        
        if result_28 and result_27:
            week28_data = result_28[0]
            week27_data = result_27[0]
            
            print("\n📊 原始数据对比:")
            print(f"第28周店铺数: {week28_data['store_count']:,}")
            print(f"第27周店铺数: {week27_data['store_count']:,}")
            
            # 计算周环比
            current_count = int(week28_data['store_count'])
            prev_count = int(week27_data['store_count'])
            
            if prev_count > 0:
                wow = (current_count - prev_count) / prev_count
                print(f"\n📈 周环比计算:")
                print(f"   计算公式: ({current_count} - {prev_count}) / {prev_count}")
                print(f"   结果: {wow:.6f} = {wow:.1%}")
            else:
                print(f"\n📈 周环比计算: 上周数据为0，无法计算")
                
        else:
            print("❌ 查询数据失败")
            
    except Exception as e:
        print(f"❌ 供给概览周环比调试失败: {e}")

def debug_detail_wow_calculation():
    """调试供给明细的周环比计算"""
    print("\n🔍 调试供给明细的周环比计算...")
    
    try:
        # 直接调用供给明细函数，查看其周环比计算逻辑
        result = get_supply_detail_real(dimension_type='子品牌', brand='圣农', selected_week=28)
        
        if result.get('data_source') == 'error':
            print(f"❌ 供给明细查询失败: {result.get('error')}")
            return
            
        data = result.get('data', [])
        if data:
            item = data[0]
            print("📊 供给明细数据:")
            print(f"   维度值: {item.get('dimension_value', 'N/A')}")
            print(f"   店铺数: {item.get('store_count', 0):,}")
            print(f"   店铺数周环比: {item.get('store_count_wow', 0):.1%}")
            
            # 检查是否有周环比字段
            if 'store_count_wow' in item:
                print(f"   周环比字段存在: {item['store_count_wow']}")
            else:
                print("   ❌ 周环比字段不存在")
                
        else:
            print("❌ 供给明细数据为空")
            
    except Exception as e:
        print(f"❌ 供给明细周环比调试失败: {e}")

def check_detail_wow_implementation():
    """检查明细表的周环比实现"""
    print("\n🔍 检查明细表的周环比实现...")
    
    # 查看子品牌特殊处理函数的实现
    print("📋 当前子品牌明细函数的周环比实现:")
    print("   在 get_supply_detail_sub_brand_special 函数中:")
    print("   'gmv_wow': 0.0,  # 暂不计算周环比")
    print("   'store_count_wow': 缺失  # 没有店铺数周环比字段")
    
    print("\n💡 问题分析:")
    print("   1. 供给概览使用完整的周环比计算逻辑")
    print("   2. 供给明细的子品牌特殊处理函数中周环比被硬编码为0")
    print("   3. 需要在子品牌特殊处理函数中添加真实的周环比计算")

def test_api_wow_data():
    """测试API返回的周环比数据"""
    print("\n🔍 测试API返回的周环比数据...")
    
    try:
        import requests
        
        # 测试供给概览API
        summary_response = requests.get("http://localhost:5001/api/supply/summary", 
                                      params={'week': 28, 'brand': '圣农'}, 
                                      timeout=10)
        
        if summary_response.status_code == 200:
            summary_data = summary_response.json()
            store_count_data = summary_data.get('store_count', {})
            
            print("📊 供给概览API周环比:")
            print(f"   店铺数: {store_count_data.get('value', 0):,}")
            print(f"   店铺数周环比: {store_count_data.get('wow', 0):.1%}")
        else:
            print(f"❌ 供给概览API失败: {summary_response.status_code}")
        
        # 测试供给明细API
        detail_response = requests.get("http://localhost:5001/api/supply/detail", 
                                     params={'type': '子品牌', 'brand': '圣农', 'selected_week': 28}, 
                                     timeout=10)
        
        if detail_response.status_code == 200:
            detail_data = detail_response.json()
            result_data = detail_data.get('data', [])
            
            if result_data:
                item = result_data[0]
                print("\n📊 供给明细API周环比:")
                print(f"   店铺数: {item.get('store_count', 0):,}")
                print(f"   店铺数周环比: {item.get('store_count_wow', 'N/A')}")
                
                # 检查所有可能的周环比字段
                wow_fields = [k for k in item.keys() if 'wow' in k.lower()]
                if wow_fields:
                    print(f"   所有周环比字段: {wow_fields}")
                else:
                    print("   ❌ 没有找到周环比字段")
            else:
                print("❌ 供给明细API数据为空")
        else:
            print(f"❌ 供给明细API失败: {detail_response.status_code}")
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")

def main():
    """主函数"""
    print("🚀 开始调试周环比计算差异问题\n")
    
    # 1. 调试供给概览的周环比计算
    debug_summary_wow_calculation()
    
    # 2. 调试供给明细的周环比计算
    debug_detail_wow_calculation()
    
    # 3. 检查明细表的周环比实现
    check_detail_wow_implementation()
    
    # 4. 测试API返回的周环比数据
    test_api_wow_data()
    
    print("\n🎯 问题总结:")
    print("   上方卡片使用完整的周环比计算逻辑")
    print("   下方表格的子品牌维度周环比被硬编码为0")
    print("   需要修复子品牌特殊处理函数中的周环比计算")
    
    print("\n🎉 调试完成！")

if __name__ == "__main__":
    main()
