# MTD计算逻辑修复说明

## 问题描述
**日期**：2025年8月1日  
**问题**：交易表现中的GMV MTD显示为0，但用户期望看到截止到2025年7月31日的7月份完成情况。

## 问题根因分析

### 原始逻辑问题
原始的MTD计算逻辑存在以下问题：

1. **依赖上一个完整周**：要求上一个完整周的结束日期必须在当前月份内才计算MTD
2. **月初问题**：当当前日期是月初时，上一个完整周在上个月，导致MTD被设为0
3. **用户期望不符**：用户在月初希望看到上个月的完整数据作为参考

### 具体场景
- **当前时间**：2025年8月1日（第31周）
- **上一个完整周**：第30周（2025年7月21日-7月27日）
- **问题**：第30周结束于7月27日，不在8月份，所以MTD被设为0
- **用户期望**：看到7月份的完整GMV数据（723,434,855.11）

## 解决方案

### 新的MTD计算逻辑
修改了 `calculate_mtd_gmv` 函数，采用更合理的逻辑：

```python
def calculate_mtd_gmv(platform='全平台', brand=''):
    """
    计算MTD GMV

    逻辑：
    1. 如果当前日期不是月初（日期>1），计算本月1日到昨天的GMV
    2. 如果当前日期是月初（日期=1），计算上月完整的GMV作为参考
    3. 这样可以确保用户总是能看到有意义的MTD数据
    """
```

### 新的YTD计算逻辑
同样修改了 `calculate_ytd_gmv` 函数：

```python
def calculate_ytd_gmv(platform='全平台', brand=''):
    """
    计算YTD GMV

    逻辑：
    1. 如果当前日期不是年初（不是1月1日），计算本年1月1日到昨天的GMV
    2. 如果当前日期是年初（1月1日），计算去年完整的GMV作为参考
    3. 这样可以确保用户总是能看到有意义的YTD数据
    """
```

### 优化版本更新
同时更新了 `calculate_mtd_ytd_optimized` 函数，使其使用新的逻辑并保持一次查询获取所有数据的性能优势。

## 修复结果

### 修复前
- **MTD GMV**: 0
- **原因**: 上一个完整周不在当前月份内

### 修复后
- **MTD GMV**: 723,434,855.11（7月份完整数据）
- **MTD年同比**: -4.89%
- **MTD月环比**: 14.64%
- **YTD GMV**: 5,591,595,555.11（1月1日到7月31日）
- **YTD年同比**: 14.07%

### 验证结果
通过API测试验证：
```json
{
  "mtd_data": {
    "current": 723434855.11,
    "mom": 0.14644677530238648,
    "rate": 11.858810805236768,
    "target": 61003996.69,
    "yoy": -0.04893576513883109
  },
  "ytd_data": {
    "current": 5591595555.11,
    "rate": 8.570744319352729,
    "target": 652404895.86,
    "yoy": 0.1407269657115412
  }
}
```

## 修改文件
- `backend/real_data_queries.py`
  - `calculate_mtd_gmv()` 函数
  - `calculate_ytd_gmv()` 函数  
  - `calculate_mtd_ytd_optimized()` 函数

## 业务逻辑说明

### MTD逻辑
1. **非月初**（如8月2日-31日）：显示8月1日到昨天的累计GMV
2. **月初**（如8月1日）：显示上月（7月）完整的GMV作为参考

### YTD逻辑
1. **非年初**（如2月1日-12月31日）：显示1月1日到昨天的累计GMV
2. **年初**（如1月1日）：显示去年完整的GMV作为参考

### 优势
1. **用户友好**：月初时显示上月完整数据，符合用户查看上月业绩的需求
2. **数据连续性**：确保MTD/YTD始终有有意义的数据显示
3. **业务合理性**：月初查看上月完整业绩是常见的业务需求

## 测试验证
创建了多个测试脚本验证修复效果：
- `debug_mtd_calculation.py` - 分析问题根因
- `test_new_mtd_logic.py` - 测试新逻辑
- `test_mtd_api.py` - 验证API接口
- `debug_api_response.py` - 调试API响应

所有测试均通过，确认修复成功。
