#!/usr/bin/env python3
"""
检查供给相关表的字段结构
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from database import execute_query
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_table_columns(table_name):
    """检查表的字段结构"""
    print(f"\n🔍 检查表 {table_name} 的字段结构...")
    
    try:
        # 检查表结构
        sql = f"""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = '{table_name}'
        ORDER BY column_name
        """
        
        result = execute_query(sql)
        if result:
            print(f"✅ 表 {table_name} 包含 {len(result)} 个字段:")
            for row in result:
                print(f"   - {row['column_name']}: {row['data_type']}")
        else:
            print(f"❌ 表 {table_name} 不存在或无字段")
            
    except Exception as e:
        print(f"❌ 检查表 {table_name} 失败: {e}")

def check_sample_data(table_name, limit=3):
    """检查表的示例数据"""
    print(f"\n📊 检查表 {table_name} 的示例数据...")
    
    try:
        sql = f"""
        SELECT * 
        FROM {table_name}
        WHERE collect_brand = '圣农'
        AND platform = '美团'
        LIMIT {limit}
        """
        
        result = execute_query(sql)
        if result:
            print(f"✅ 表 {table_name} 示例数据 ({len(result)} 条):")
            for i, row in enumerate(result):
                print(f"   记录 {i+1}:")
                for key, value in row.items():
                    if value is not None:
                        print(f"     {key}: {value}")
        else:
            print(f"❌ 表 {table_name} 无数据或查询失败")
            
    except Exception as e:
        print(f"❌ 检查表 {table_name} 数据失败: {e}")

def main():
    """主函数"""
    print("🚀 开始检查供给相关表的结构\n")
    
    tables = [
        'dws_mt_brand_city_details_daily_data_attribute_d',
        'dws_mt_channel_city_details_daily_data_attribute_d', 
        'dws_t_mt_sales_by_upc_list_attribute_d'
    ]
    
    for table in tables:
        check_table_columns(table)
        check_sample_data(table)
        print("-" * 80)

if __name__ == "__main__":
    main()
