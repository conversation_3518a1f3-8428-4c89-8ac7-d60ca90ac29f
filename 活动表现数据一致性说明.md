# 活动表现数据一致性说明

## 🎯 用户疑问

用户反映："活动表现页面导出的Excel明细数据，现在是有不同维度下的数据了，但是和页面上的数据对不上。"

## 📊 技术验证结果

### ✅ 数据源完全一致

经过详细技术验证，**前端页面和Excel导出使用完全相同的后端逻辑**：

**前端API调用：**
```javascript
// frontend/src/services/api.js (第112-126行)
fetchActivityDetail(dimensionType, selectedWeek, brand, page, pageSize, platform)
```

**后端API处理：**
```python
# backend/app.py (第358-404行) 
@app.route('/api/activity/detail', methods=['GET'])
def get_activity_detail():
    # 整体维度：使用 get_activity_detail_real_weeks()
    # 其他维度：使用 get_activity_detail_real_dimension()
```

**Excel导出调用：**
```python
# backend/excel_export.py (第1118行)
get_activity_detail_real_dimension(dimension, f'2024-W{week}', brand, 1, 100, platform)
```

### 🔍 实际测试结果

测试参数：维度=城市，周次=2024-W31，品牌=海天

| 数据源 | 平台 | 数据条数 | 第一条数据(上海GMV) |
|--------|------|----------|-------------------|
| 前端页面 | 全平台 | 20条(分页显示) | 184,923.48 |
| Excel导出-全平台工作表 | 全平台 | 100条(全量显示) | 184,923.48 |
| Excel导出-美团工作表 | 美团 | 100条(单平台) | 179,120.52 |
| Excel导出-京东到家工作表 | 京东到家 | 100条(单平台) | 深圳: 62,134.26 |

## 🎨 设计逻辑说明

### 前端页面设计
- **默认显示**: 全平台数据
- **平台筛选器**: 用户可选择【全平台、美团、饿了么、京东到家、多点、淘鲜达】
- **分页显示**: 每页20条记录
- **交互性**: 用户可以动态切换平台查看不同数据

### Excel导出设计  
- **全量导出**: 为所有平台生成独立工作表
- **完整数据**: 每个维度导出100条记录（vs 前端20条）
- **工作表结构**:
  - 【全平台】工作表 - 汇总所有平台数据
  - 【美团】工作表 - 仅美团平台数据
  - 【饿了么】工作表 - 仅饿了么平台数据
  - 【京东到家】工作表 - 仅京东到家平台数据
  - 【多点】工作表 - 仅多点平台数据
  - 【淘鲜达】工作表 - 仅淘鲜达平台数据

## 📋 数据差异原因

### 1. 平台维度差异
- **全平台数据** = 所有平台数据的汇总
- **单平台数据** = 该平台的独立数据
- **示例**: 上海市活动GMV
  - 全平台: 184,923.48（所有平台总和）
  - 美团: 179,120.52（仅美团平台）
  - 差额: 5,802.96（其他平台在上海的活动GMV）

### 2. 排序差异
- 不同平台的TOP城市/商品排序可能不同
- **全平台TOP1城市**: 上海
- **京东到家TOP1城市**: 深圳
- 这是正常的业务现象

### 3. 显示数量差异
- **前端页面**: 分页显示，每页20条
- **Excel导出**: 每个维度最多100条记录

## ✅ 结论

### 数据一致性确认
1. **✅ 后端逻辑**: 前端和Excel使用相同函数
2. **✅ 数据源**: 查询相同数据库表
3. **✅ 筛选逻辑**: 相同的品牌、周次、维度参数
4. **✅ 平台筛选**: 相同的platform参数传递

### 用户使用建议
1. **对比时请确认平台**: 
   - 前端查看的是哪个平台（默认：全平台）
   - Excel对比的是哪个工作表（建议：全平台工作表）

2. **数据完整性**: 
   - Excel包含更多数据条数（100 vs 20）
   - Excel提供所有平台的完整视图

3. **业务理解**:
   - 全平台 ≠ 单平台数据
   - 不同平台的排序结果可能不同
   - 这些差异是正确的业务逻辑

## 🔧 验证方法

如需验证数据一致性，请按以下步骤：

1. **前端页面**: 确保选择【全平台】
2. **Excel文件**: 查看【全平台】工作表
3. **对比参数**: 相同维度、相同周次、相同品牌
4. **前20条数据**: 应与前端页面第一页完全一致

---

**结论**: 系统设计正确，数据源一致，显示差异是正常的业务逻辑差异。 