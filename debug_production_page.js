// 在浏览器控制台中运行此脚本来调试正式页面
// 复制粘贴到浏览器控制台并执行

(function() {
    console.log('🔍 开始调试正式页面的活动趋势图问题');
    
    // 1. 检查页面基本信息
    function checkPageInfo() {
        console.log('\n📋 1. 页面基本信息检查');
        console.log('URL:', window.location.href);
        console.log('User Agent:', navigator.userAgent);
        console.log('React版本:', window.React ? window.React.version : '未检测到');
        console.log('ECharts版本:', window.echarts ? window.echarts.version : '未检测到');
    }
    
    // 2. 检查DOM结构
    function checkDOMStructure() {
        console.log('\n🏗️ 2. DOM结构检查');
        
        // 查找活动趋势图容器
        const trendCharts = document.querySelectorAll('[class*="trading-section"]');
        console.log('找到的trading-section数量:', trendCharts.length);
        
        trendCharts.forEach((section, index) => {
            const title = section.querySelector('.ant-card-head-title, .section-title, h3, h4');
            const titleText = title ? title.textContent : '无标题';
            console.log(`Section ${index + 1}: ${titleText}`);
            
            // 查找ECharts容器
            const chartContainers = section.querySelectorAll('div[style*="height"]');
            chartContainers.forEach((container, cIndex) => {
                const rect = container.getBoundingClientRect();
                console.log(`  图表容器 ${cIndex + 1}:`, {
                    width: rect.width,
                    height: rect.height,
                    style: container.style.cssText,
                    hasEChartsInstance: !!window.echarts?.getInstanceByDom(container)
                });
            });
        });
    }
    
    // 3. 检查React组件状态
    function checkReactComponents() {
        console.log('\n⚛️ 3. React组件状态检查');
        
        // 查找React Fiber节点
        const rootElement = document.getElementById('root');
        if (rootElement && rootElement._reactInternalFiber) {
            console.log('React Fiber根节点存在');
        } else if (rootElement && rootElement._reactInternals) {
            console.log('React 18+ 内部结构存在');
        } else {
            console.log('⚠️ 未找到React内部结构');
        }
        
        // 检查是否有错误边界
        const errorElements = document.querySelectorAll('[class*="error"], [class*="Error"]');
        if (errorElements.length > 0) {
            console.log('⚠️ 发现可能的错误元素:', errorElements.length);
            errorElements.forEach(el => console.log('错误元素:', el.textContent));
        }
    }
    
    // 4. 检查网络请求
    function checkNetworkRequests() {
        console.log('\n🌐 4. 网络请求监控');
        
        // 监控fetch请求
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const url = args[0];
            if (typeof url === 'string' && url.includes('/activity/trends')) {
                console.log('🔗 活动趋势API调用:', url);
                return originalFetch.apply(this, args)
                    .then(response => {
                        console.log('📥 API响应:', response.status, response.statusText);
                        return response;
                    })
                    .catch(error => {
                        console.error('❌ API错误:', error);
                        throw error;
                    });
            }
            return originalFetch.apply(this, args);
        };
        
        console.log('✅ 网络请求监控已启用');
    }
    
    // 5. 检查ECharts实例
    function checkEChartsInstances() {
        console.log('\n📊 5. ECharts实例检查');
        
        const allDivs = document.querySelectorAll('div');
        let echartsCount = 0;
        
        allDivs.forEach((div, index) => {
            const instance = window.echarts?.getInstanceByDom(div);
            if (instance) {
                echartsCount++;
                const rect = div.getBoundingClientRect();
                console.log(`ECharts实例 ${echartsCount}:`, {
                    element: div,
                    width: rect.width,
                    height: rect.height,
                    isDisposed: instance.isDisposed(),
                    hasOption: !!instance.getOption()
                });
            }
        });
        
        console.log(`总共找到 ${echartsCount} 个ECharts实例`);
    }
    
    // 6. 模拟平台切换
    function simulatePlatformSwitch() {
        console.log('\n🔄 6. 模拟平台切换');
        
        const platformButtons = document.querySelectorAll('.ant-radio-button, [class*="platform"]');
        console.log('找到的平台按钮数量:', platformButtons.length);
        
        platformButtons.forEach((button, index) => {
            console.log(`按钮 ${index + 1}:`, {
                text: button.textContent,
                className: button.className,
                isActive: button.classList.contains('ant-radio-button-checked') || 
                         button.classList.contains('active')
            });
        });
        
        // 尝试点击美团按钮
        const meituanButton = Array.from(platformButtons).find(btn => 
            btn.textContent.includes('美团')
        );
        
        if (meituanButton) {
            console.log('🎯 尝试点击美团按钮');
            meituanButton.click();
            
            // 延迟检查结果
            setTimeout(() => {
                console.log('平台切换后的状态检查:');
                checkEChartsInstances();
            }, 1000);
        } else {
            console.log('❌ 未找到美团按钮');
        }
    }
    
    // 7. 检查控制台错误
    function checkConsoleErrors() {
        console.log('\n🚨 7. 控制台错误监控');
        
        // 监控console.error
        const originalError = console.error;
        console.error = function(...args) {
            if (args.some(arg => 
                typeof arg === 'string' && 
                (arg.includes('ActivityInteractiveTrendChart') || 
                 arg.includes('echarts') || 
                 arg.includes('trends'))
            )) {
                console.log('🔴 发现相关错误:', args);
            }
            return originalError.apply(this, args);
        };
        
        console.log('✅ 错误监控已启用');
    }
    
    // 8. 检查CSS样式
    function checkCSSStyles() {
        console.log('\n🎨 8. CSS样式检查');
        
        const chartContainers = document.querySelectorAll('div[style*="height"]');
        chartContainers.forEach((container, index) => {
            const computedStyle = window.getComputedStyle(container);
            const rect = container.getBoundingClientRect();
            
            if (rect.height > 300) { // 可能是图表容器
                console.log(`图表容器 ${index + 1} 样式:`, {
                    width: computedStyle.width,
                    height: computedStyle.height,
                    display: computedStyle.display,
                    visibility: computedStyle.visibility,
                    overflow: computedStyle.overflow,
                    position: computedStyle.position
                });
            }
        });
    }
    
    // 执行所有检查
    function runAllChecks() {
        checkPageInfo();
        checkDOMStructure();
        checkReactComponents();
        checkNetworkRequests();
        checkEChartsInstances();
        checkConsoleErrors();
        checkCSSStyles();
        
        console.log('\n🎯 调试工具已启动，现在可以尝试切换平台');
        console.log('💡 提示: 切换平台后，观察控制台输出的调试信息');
        
        // 提供手动触发函数
        window.debugActivityChart = {
            checkECharts: checkEChartsInstances,
            checkDOM: checkDOMStructure,
            simulateSwitch: simulatePlatformSwitch,
            checkStyles: checkCSSStyles
        };
        
        console.log('🔧 可用的调试函数:');
        console.log('- window.debugActivityChart.checkECharts() - 检查ECharts实例');
        console.log('- window.debugActivityChart.checkDOM() - 检查DOM结构');
        console.log('- window.debugActivityChart.simulateSwitch() - 模拟平台切换');
        console.log('- window.debugActivityChart.checkStyles() - 检查CSS样式');
    }
    
    // 启动调试
    runAllChecks();
})();
