<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端趋势图调试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
        }
        .debug-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        .debug-content {
            background: #f6f6f6;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', '<PERSON><PERSON>', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .platform-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .platform-btn {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .platform-btn.active {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        .error {
            color: #ff4d4f;
            background: #fff2f0;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ffccc7;
        }
        .success {
            color: #52c41a;
            background: #f6ffed;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #b7eb8f;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 前端趋势图调试工具</h1>
        <p>模拟前端ActivityDataModule的数据加载和传递过程</p>

        <div class="platform-buttons">
            <button class="platform-btn active" data-platform="全平台">全平台</button>
            <button class="platform-btn" data-platform="美团">美团</button>
            <button class="platform-btn" data-platform="饿了么">饿了么</button>
            <button class="platform-btn" data-platform="京东到家">京东到家</button>
        </div>

        <div class="debug-section">
            <div class="debug-title">📊 1. API调用参数</div>
            <div id="api-params" class="debug-content">等待测试...</div>
        </div>

        <div class="debug-section">
            <div class="debug-title">📈 2. API响应数据</div>
            <div id="api-response" class="debug-content">等待测试...</div>
        </div>

        <div class="debug-section">
            <div class="debug-title">🔍 3. 数据验证</div>
            <div id="data-validation" class="debug-content">等待测试...</div>
        </div>

        <div class="debug-section">
            <div class="debug-title">⚙️ 4. 前端逻辑模拟</div>
            <div id="frontend-logic" class="debug-content">等待测试...</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5002/api';
        const BRAND = '圣农';
        let currentPlatform = '全平台';

        // 平台切换事件
        document.querySelectorAll('.platform-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                // 更新按钮状态
                document.querySelectorAll('.platform-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                // 更新当前平台
                currentPlatform = btn.dataset.platform;
                
                // 重新测试
                runDebugTest();
            });
        });

        async function runDebugTest() {
            console.log('🔍 开始调试测试，平台:', currentPlatform);
            
            // 1. 显示API调用参数
            const selectedWeek = '2025-W30';
            let weekNumber = null;
            if (selectedWeek && selectedWeek.includes('-W')) {
                weekNumber = parseInt(selectedWeek.split('-W')[1]);
            } else if (selectedWeek) {
                weekNumber = parseInt(selectedWeek);
            }
            if (!weekNumber || isNaN(weekNumber)) {
                weekNumber = 26; // 默认值
            }

            const apiParams = {
                selectedWeek: selectedWeek,
                weekNumber: weekNumber,
                brand: BRAND,
                selectedPlatform: currentPlatform
            };

            document.getElementById('api-params').innerHTML = `
                <div class="success">✅ API调用参数</div>
                <pre>${JSON.stringify(apiParams, null, 2)}</pre>
                <br><strong>实际API URL:</strong><br>
                ${API_BASE}/activity/trends?brand=${BRAND}&end_week=${weekNumber}&platform=${currentPlatform}
            `;

            // 2. 调用API并显示响应
            try {
                const response = await fetch(`${API_BASE}/activity/trends?brand=${BRAND}&end_week=${weekNumber}&platform=${currentPlatform}`);
                const data = await response.json();

                document.getElementById('api-response').innerHTML = `
                    <div class="success">✅ API响应成功 (${response.status})</div>
                    <strong>响应数据结构:</strong><br>
                    - 状态码: ${response.status}<br>
                    - 数据键: ${Object.keys(data).join(', ')}<br>
                    - trend_data长度: ${data.trend_data ? data.trend_data.length : 'undefined'}<br>
                    <br><strong>完整响应:</strong><br>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;

                // 3. 数据验证
                validateData(data);

                // 4. 模拟前端逻辑
                simulateFrontendLogic(data);

            } catch (error) {
                document.getElementById('api-response').innerHTML = `
                    <div class="error">❌ API调用失败</div>
                    <pre>错误: ${error.message}</pre>
                `;
                
                document.getElementById('data-validation').innerHTML = `
                    <div class="error">❌ 无法验证数据 - API调用失败</div>
                `;
                
                document.getElementById('frontend-logic').innerHTML = `
                    <div class="error">❌ 无法模拟前端逻辑 - API调用失败</div>
                `;
            }
        }

        function validateData(data) {
            let validationResults = [];
            
            // 检查基本结构
            if (!data) {
                validationResults.push('❌ 数据为空');
            } else {
                validationResults.push('✅ 数据不为空');
            }

            if (!data.trend_data) {
                validationResults.push('❌ 缺少trend_data字段');
            } else {
                validationResults.push('✅ trend_data字段存在');
                
                if (!Array.isArray(data.trend_data)) {
                    validationResults.push('❌ trend_data不是数组');
                } else {
                    validationResults.push(`✅ trend_data是数组，长度: ${data.trend_data.length}`);
                    
                    if (data.trend_data.length === 0) {
                        validationResults.push('⚠️  trend_data为空数组');
                    } else {
                        // 检查第一个元素的字段
                        const firstItem = data.trend_data[0];
                        const requiredFields = ['week', 'activity_gmv', 'activity_gmv_yoy', 'activity_gmv_ratio', 'activity_gmv_ratio_yoy'];
                        const missingFields = requiredFields.filter(field => !(field in firstItem));
                        
                        if (missingFields.length === 0) {
                            validationResults.push('✅ 所有必需字段都存在');
                        } else {
                            validationResults.push(`❌ 缺少字段: ${missingFields.join(', ')}`);
                        }
                        
                        // 检查数据值
                        const activityGmv = firstItem.activity_gmv;
                        if (activityGmv === 0) {
                            validationResults.push('⚠️  activity_gmv为0，可能导致图表显示问题');
                        } else {
                            validationResults.push(`✅ activity_gmv有值: ${activityGmv}`);
                        }
                    }
                }
            }

            document.getElementById('data-validation').innerHTML = `
                <div class="success">📋 数据验证结果</div>
                ${validationResults.map(result => `<div>${result}</div>`).join('')}
            `;
        }

        function simulateFrontendLogic(data) {
            let logicResults = [];
            
            // 模拟ActivityInteractiveTrendChart的逻辑
            const loading = false;
            const selectedMetric = 'activity_gmv';
            
            logicResults.push('🔍 模拟ActivityInteractiveTrendChart组件逻辑:');
            logicResults.push(`- loading: ${loading}`);
            logicResults.push(`- data: ${data ? '存在' : '不存在'}`);
            logicResults.push(`- data.trend_data: ${data && data.trend_data ? '存在' : '不存在'}`);
            logicResults.push(`- selectedMetric: ${selectedMetric}`);
            
            // 检查条件
            if (loading || !data || !data.trend_data || !selectedMetric) {
                logicResults.push('❌ 条件检查失败，图表会显示loading状态');
                logicResults.push('原因分析:');
                if (loading) logicResults.push('  - loading为true');
                if (!data) logicResults.push('  - data为空');
                if (!data.trend_data) logicResults.push('  - data.trend_data为空');
                if (!selectedMetric) logicResults.push('  - selectedMetric为空');
            } else {
                logicResults.push('✅ 条件检查通过，图表应该正常渲染');
                
                // 模拟数据处理
                const trendData = data.trend_data;
                const weeks = trendData.map(item => `第${item.week}周`);
                const mainValues = trendData.map(item => Math.round(item.activity_gmv));
                const yoyValues = trendData.map(item => (item.activity_gmv_yoy * 100).toFixed(2));
                
                logicResults.push('📊 数据处理结果:');
                logicResults.push(`- 周次: ${weeks.join(', ')}`);
                logicResults.push(`- 主要数值: ${mainValues.join(', ')}`);
                logicResults.push(`- 年同比: ${yoyValues.join(', ')}%`);
                
                // 检查是否有有效数据
                const hasValidData = mainValues.some(val => val > 0);
                if (hasValidData) {
                    logicResults.push('✅ 有有效的数据值，图表应该显示');
                } else {
                    logicResults.push('⚠️  所有数据值都为0，图表可能显示为空');
                }
            }

            document.getElementById('frontend-logic').innerHTML = `
                <div class="success">⚙️ 前端逻辑模拟</div>
                ${logicResults.map(result => `<div>${result}</div>`).join('')}
            `;
        }

        // 初始测试
        runDebugTest();
    </script>
</body>
</html>
