#!/usr/bin/env python3
"""
调试活动趋势API的具体问题
"""

import requests
import json

def test_activity_trends_api():
    """测试活动趋势API"""
    base_url = "http://localhost:5002/api"
    brand = "圣农"
    
    print("🔍 调试活动趋势API")
    print("=" * 50)
    
    # 测试不同的参数组合
    test_cases = [
        {
            "name": "全平台 - 无end_week",
            "params": {"brand": brand, "platform": "全平台"}
        },
        {
            "name": "全平台 - 有end_week",
            "params": {"brand": brand, "platform": "全平台", "end_week": 26}
        },
        {
            "name": "美团 - 无end_week",
            "params": {"brand": brand, "platform": "美团"}
        },
        {
            "name": "美团 - 有end_week",
            "params": {"brand": brand, "platform": "美团", "end_week": 26}
        },
        {
            "name": "饿了么 - 有end_week",
            "params": {"brand": brand, "platform": "饿了么", "end_week": 26}
        },
        {
            "name": "无效平台测试",
            "params": {"brand": brand, "platform": "无效平台", "end_week": 26}
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📊 测试: {test_case['name']}")
        print(f"参数: {test_case['params']}")
        print("-" * 40)
        
        try:
            response = requests.get(f"{base_url}/activity/trends", params=test_case['params'])
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                trend_data = data.get('trend_data', [])
                print(f"✅ 成功 - 趋势数据条数: {len(trend_data)}")
                
                if trend_data:
                    first_item = trend_data[0]
                    print(f"第一条数据字段: {list(first_item.keys())}")
                    print(f"第一条数据: {first_item}")
                    
                    # 检查数据是否为0
                    activity_gmv = first_item.get('activity_gmv', 0)
                    if activity_gmv == 0:
                        print("⚠️  警告: activity_gmv为0，可能是数据问题")
                    else:
                        print(f"✅ activity_gmv正常: {activity_gmv}")
                else:
                    print("❌ 趋势数据为空")
            else:
                print(f"❌ 失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except Exception as e:
            print(f"❌ 异常: {e}")
    
    print("\n" + "=" * 50)
    print("🔍 检查后端日志以获取更多信息")

if __name__ == "__main__":
    test_activity_trends_api()
